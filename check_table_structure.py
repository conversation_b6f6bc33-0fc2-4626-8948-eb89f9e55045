#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def check_table_structure():
    """检查数据库表结构"""
    db_path = 'equipment_maintenance.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("=== 检查表结构 ===")
        
        # 检查equipment表结构
        print("\nequipment表结构:")
        cursor.execute("PRAGMA table_info(equipment)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else ''}")
        
        # 检查spare_parts表结构
        print("\nspare_parts表结构:")
        cursor.execute("PRAGMA table_info(spare_parts)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else ''}")
        
        # 检查maintenance_records表结构
        print("\nmaintenance_records表结构:")
        cursor.execute("PRAGMA table_info(maintenance_records)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else ''}")
        
        # 检查数据数量
        print("\n=== 数据数量 ===")
        cursor.execute("SELECT COUNT(*) FROM equipment")
        eq_count = cursor.fetchone()[0]
        print(f"设备数量: {eq_count}")
        
        cursor.execute("SELECT COUNT(*) FROM spare_parts")
        sp_count = cursor.fetchone()[0]
        print(f"零备件数量: {sp_count}")
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_records")
        mr_count = cursor.fetchone()[0]
        print(f"维保记录数量: {mr_count}")
        
        # 显示设备数据
        if eq_count > 0:
            print("\n设备数据:")
            cursor.execute("SELECT * FROM equipment LIMIT 5")
            equipment = cursor.fetchall()
            for eq in equipment:
                print(f"  {eq}")
        
        # 显示零备件数据
        if sp_count > 0:
            print("\n零备件数据:")
            cursor.execute("SELECT * FROM spare_parts")
            parts = cursor.fetchall()
            for part in parts:
                print(f"  {part}")
        
    except Exception as e:
        print(f"❌ 检查表结构时出错: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    check_table_structure()
