#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据管理CRUD API接口
"""

import requests
import json

def test_crud_apis():
    """测试CRUD API接口"""
    print("🧪 测试数据管理CRUD API接口...")
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # ==================== 测试区域管理 CRUD ====================
        print("\n1️⃣ 测试区域管理CRUD...")
        
        # 1. 创建新区域
        print("   创建新区域...")
        create_area_data = {
            "name": "测试区域",
            "description": "这是一个测试区域"
        }
        response = requests.post(f"{base_url}/api/areas", json=create_area_data)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                new_area_id = result['data']['id']
                print(f"   ✅ 区域创建成功，ID: {new_area_id}")
            else:
                print(f"   ❌ 区域创建失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ 区域创建失败: HTTP {response.status_code}")
            return False
        
        # 2. 更新区域
        print("   更新区域...")
        update_area_data = {
            "name": "测试区域(已更新)",
            "description": "这是一个更新后的测试区域"
        }
        response = requests.put(f"{base_url}/api/areas/{new_area_id}", json=update_area_data)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("   ✅ 区域更新成功")
            else:
                print(f"   ❌ 区域更新失败: {result.get('error')}")
        else:
            print(f"   ❌ 区域更新失败: HTTP {response.status_code}")
        
        # ==================== 测试设备管理 CRUD ====================
        print("\n2️⃣ 测试设备管理CRUD...")
        
        # 1. 创建新设备
        print("   创建新设备...")
        create_equipment_data = {
            "area_id": new_area_id,
            "name": "测试设备",
            "type": "测试类型",
            "description": "这是一个测试设备"
        }
        response = requests.post(f"{base_url}/api/equipment", json=create_equipment_data)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                new_equipment_id = result['data']['id']
                print(f"   ✅ 设备创建成功，ID: {new_equipment_id}")
            else:
                print(f"   ❌ 设备创建失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ 设备创建失败: HTTP {response.status_code}")
            return False
        
        # 2. 更新设备
        print("   更新设备...")
        update_equipment_data = {
            "area_id": new_area_id,
            "name": "测试设备(已更新)",
            "type": "更新类型",
            "description": "这是一个更新后的测试设备"
        }
        response = requests.put(f"{base_url}/api/equipment/{new_equipment_id}", json=update_equipment_data)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("   ✅ 设备更新成功")
            else:
                print(f"   ❌ 设备更新失败: {result.get('error')}")
        else:
            print(f"   ❌ 设备更新失败: HTTP {response.status_code}")
        
        # ==================== 测试零备件管理 CRUD ====================
        print("\n3️⃣ 测试零备件管理CRUD...")
        
        # 1. 创建新零备件
        print("   创建新零备件...")
        create_spare_part_data = {
            "equipment_id": new_equipment_id,
            "name": "测试零备件",
            "maintenance_cycle_months": 6,
            "description": "这是一个测试零备件"
        }
        response = requests.post(f"{base_url}/api/spare-parts", json=create_spare_part_data)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                new_spare_part_id = result['data']['id']
                print(f"   ✅ 零备件创建成功，ID: {new_spare_part_id}")
            else:
                print(f"   ❌ 零备件创建失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ 零备件创建失败: HTTP {response.status_code}")
            return False
        
        # 2. 更新零备件
        print("   更新零备件...")
        update_spare_part_data = {
            "equipment_id": new_equipment_id,
            "name": "测试零备件(已更新)",
            "maintenance_cycle_months": 12,
            "description": "这是一个更新后的测试零备件"
        }
        response = requests.put(f"{base_url}/api/spare-parts/{new_spare_part_id}", json=update_spare_part_data)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("   ✅ 零备件更新成功")
            else:
                print(f"   ❌ 零备件更新失败: {result.get('error')}")
        else:
            print(f"   ❌ 零备件更新失败: HTTP {response.status_code}")
        
        # ==================== 测试删除功能 ====================
        print("\n4️⃣ 测试删除功能...")
        
        # 1. 删除零备件
        print("   删除零备件...")
        response = requests.delete(f"{base_url}/api/spare-parts/{new_spare_part_id}")
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("   ✅ 零备件删除成功")
            else:
                print(f"   ❌ 零备件删除失败: {result.get('error')}")
        else:
            print(f"   ❌ 零备件删除失败: HTTP {response.status_code}")
        
        # 2. 删除设备
        print("   删除设备...")
        response = requests.delete(f"{base_url}/api/equipment/{new_equipment_id}")
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("   ✅ 设备删除成功")
            else:
                print(f"   ❌ 设备删除失败: {result.get('error')}")
        else:
            print(f"   ❌ 设备删除失败: HTTP {response.status_code}")
        
        # 3. 删除区域
        print("   删除区域...")
        response = requests.delete(f"{base_url}/api/areas/{new_area_id}")
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("   ✅ 区域删除成功")
            else:
                print(f"   ❌ 区域删除失败: {result.get('error')}")
        else:
            print(f"   ❌ 区域删除失败: HTTP {response.status_code}")
        
        print(f"\n🎉 CRUD API测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

def main():
    """主函数"""
    print("🔧 数据管理CRUD API测试工具")
    print("=" * 50)
    
    success = test_crud_apis()
    
    if success:
        print("\n✅ 所有CRUD功能测试通过!")
    else:
        print("\n❌ CRUD功能测试失败")

if __name__ == "__main__":
    main()
