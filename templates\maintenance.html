{% extends "base.html" %}

{% block title %}维保管理 - 设备零备件周期管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-wrench me-2"></i>维保管理</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-primary" onclick="filterAlerts(7)">7天内</button>
            <button type="button" class="btn btn-outline-primary" onclick="filterAlerts(15)">15天内</button>
            <button type="button" class="btn btn-outline-primary active" onclick="filterAlerts(30)">30天内</button>
            <button type="button" class="btn btn-outline-primary" onclick="filterAlerts(60)">60天内</button>
        </div>
        <button type="button" class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#createPlanModal">
            <i class="fas fa-calendar-plus me-1"></i>创建维保计划
        </button>
        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addMaintenanceModal">
            <i class="fas fa-plus me-1"></i>添加维保记录
        </button>
    </div>
</div>

<!-- 维保提醒卡片 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bell me-2"></i>维保提醒 <span id="alert-period">(30天内)</span></h5>
            </div>
            <div class="card-body">
                <div id="maintenance-alerts" class="row">
                    <div class="col-12 text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 维保计划表格 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-calendar-check me-2"></i>维保计划</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="maintenancePlansTable">
                <thead>
                    <tr>
                        <th>零备件</th>
                        <th>区域</th>
                        <th>设备</th>
                        <th>计划日期</th>
                        <th>状态</th>
                        <th>备注</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="maintenancePlansTableBody">
                    <tr>
                        <td colspan="7" class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 维保记录表格 -->
<div class="card" id="maintenanceRecordsCard">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-history me-2"></i>维保记录</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="maintenanceRecordsTable">
                <thead>
                    <tr>
                        <th>区域</th>
                        <th>设备</th>
                        <th>零备件</th>
                        <th>维保时间</th>
                        <th>下次维保</th>
                        <th>维保人员</th>
                        <th>使用数量</th>
                        <th>剩余天数</th>
                        <th>库存状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="maintenanceRecordsBody">
                    <tr>
                        <td colspan="10" class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- 分页组件 -->
        <div class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
                <span id="recordsInfo">显示第 1-10 条，共 0 条记录</span>
            </div>
            <nav aria-label="维保记录分页">
                <ul class="pagination mb-0" id="maintenancePagination">
                    <!-- 分页按钮将通过JavaScript动态生成 -->
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- 创建维保计划模态框 -->
<div class="modal fade" id="createPlanModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-calendar-plus me-2"></i>创建维保计划</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createPlanForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="plan-area-select" class="form-label">选择区域</label>
                                <select class="form-select" id="plan-area-select" required onchange="loadEquipmentForPlan()">
                                    <option value="">请选择区域</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="plan-equipment-select" class="form-label">选择设备</label>
                                <select class="form-select" id="plan-equipment-select" required>
                                    <option value="">请先选择区域</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="plan-spare-part-select" class="form-label">选择零备件</label>
                        <select class="form-select" id="plan-spare-part-select" required>
                            <option value="">请选择零备件</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="planned-date" class="form-label">计划维保日期</label>
                        <input type="date" class="form-control" id="planned-date" required>
                    </div>
                    <div class="mb-3">
                        <label for="plan-notes" class="form-label">备注</label>
                        <textarea class="form-control" id="plan-notes" rows="3" placeholder="请输入维保计划备注"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitMaintenancePlan()">
                    <i class="fas fa-save me-1"></i>创建计划
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 执行维保计划模态框 -->
<div class="modal fade" id="executePlanModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-play me-2"></i>执行维保计划</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>计划信息：</strong>
                    <div id="plan-info" class="mt-2"></div>
                </div>
                <form id="executePlanForm">
                    <input type="hidden" id="execute-plan-id">
                    <div class="mb-3">
                        <label for="execute-maintenance-date" class="form-label">实际维保日期</label>
                        <input type="date" class="form-control" id="execute-maintenance-date" required>
                    </div>
                    <div class="mb-3">
                        <label for="execute-technician" class="form-label">维保人员</label>
                        <input type="text" class="form-control" id="execute-technician" placeholder="请输入维保人员姓名">
                    </div>
                    <div class="mb-3">
                        <label for="execute-quantity-used" class="form-label">使用数量</label>
                        <input type="number" class="form-control" id="execute-quantity-used" value="1" min="1" required>
                    </div>
                    <div class="mb-3">
                        <label for="execute-notes" class="form-label">维保备注</label>
                        <textarea class="form-control" id="execute-notes" rows="3" placeholder="请输入维保备注"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" onclick="submitExecutePlan()">
                    <i class="fas fa-check me-1"></i>完成维保
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 添加维保记录模态框 -->
<div class="modal fade" id="addMaintenanceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus me-2"></i>添加维保记录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addMaintenanceForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="area-select" class="form-label">选择区域</label>
                                <select class="form-select" id="area-select" required onchange="loadEquipmentForArea()">
                                    <option value="">请选择区域</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="equipment-select" class="form-label">选择设备</label>
                                <select class="form-select" id="equipment-select" required>
                                    <option value="">请先选择区域</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="spare-part-select" class="form-label">选择零备件</label>
                        <select class="form-select" id="spare-part-select" required>
                            <option value="">请选择零备件</option>
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="maintenance-date" class="form-label">维保日期</label>
                                <input type="date" class="form-control" id="maintenance-date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quantity-used" class="form-label">使用数量</label>
                                <input type="number" class="form-control" id="quantity-used" value="1" min="1" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="technician" class="form-label">维保人员</label>
                        <input type="text" class="form-control" id="technician" placeholder="请输入维保人员姓名">
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">备注</label>
                        <textarea class="form-control" id="notes" rows="3" placeholder="请输入维保备注"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" onclick="submitMaintenanceRecord()">
                    <i class="fas fa-save me-1"></i>保存
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentDays = 30;

// 页面加载完成后初始化
$(document).ready(function() {
    loadMaintenanceAlerts(30);
    loadMaintenanceRecords();
    loadMaintenancePlans();
    loadAreasForSelect();
    loadSparePartsForSelect();

    // 设置默认日期为今天
    document.getElementById('maintenance-date').valueAsDate = new Date();
    document.getElementById('planned-date').valueAsDate = new Date();
});

// 加载维保提醒
function loadMaintenanceAlerts(days) {
    currentDays = days;
    $('#alert-period').text(`(${days}天内)`);
    
    // 更新按钮状态
    $('.btn-group .btn').removeClass('active');
    $(`.btn-group .btn:contains('${days}天内')`).addClass('active');
    
    $.get(`/api/maintenance/alerts?days=${days}`)
        .done(function(response) {
            if (response.success) {
                updateMaintenanceAlerts(response.data);
            }
        })
        .fail(function() {
            $('#maintenance-alerts').html('<div class="col-12 text-center text-danger">加载失败</div>');
        });
}

// 更新维保提醒显示
function updateMaintenanceAlerts(data) {
    const container = $('#maintenance-alerts');
    container.empty();
    
    if (data.length === 0) {
        container.html('<div class="col-12 text-center text-muted">暂无维保提醒</div>');
        return;
    }
    
    data.forEach(function(item) {
        const priorityClass = {
            'high': 'border-danger',
            'medium': 'border-warning',
            'low': 'border-info'
        }[item.priority] || 'border-secondary';
        
        // 根据priority_text设置不同颜色
        const priorityBadge = {
            '逾期': 'bg-danger',      // 深红色 - 最严重
            '紧急': 'bg-warning',     // 橙色 - 需要注意
            '一般': 'bg-info'         // 蓝色 - 正常状态
        }[item.priority_text] || 'bg-secondary';
        
        const daysText = item.days_remaining !== null ? 
            (item.days_remaining < 0 ? `逾期${Math.abs(item.days_remaining)}天` : `剩余${item.days_remaining}天`) : 
            '无记录';
        
        const alertCard = `
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card h-100 ${priorityClass}">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title mb-0">${item.spare_part_name}</h6>
                            <span class="badge ${priorityBadge}">${item.priority_text}</span>
                        </div>
                        <p class="card-text text-muted mb-1">
                            <i class="fas fa-map-marker-alt me-1"></i>${item.area_name} - ${item.equipment_name}
                        </p>
                        <p class="card-text">
                            <i class="fas fa-clock me-1"></i>${daysText}
                        </p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-boxes me-1"></i>库存: ${item.quantity || 0}
                            </small>
                            <button class="btn btn-sm btn-outline-primary" onclick="createMaintenancePlanFromAlert('${item.spare_part_name}', '${item.area_name}', '${item.equipment_name}')">
                                <i class="fas fa-wrench me-1"></i>维保
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        container.append(alertCard);
    });
}

// 过滤提醒
function filterAlerts(days) {
    loadMaintenanceAlerts(days);
}

// 当前页码
let currentPage = 1;
const recordsPerPage = 10;

// 加载维保记录
function loadMaintenanceRecords(page = 1, scrollToTable = false) {
    currentPage = page;
    $.get(`/api/maintenance/records?page=${page}&per_page=${recordsPerPage}`)
        .done(function(response) {
            if (response.success) {
                updateMaintenanceRecordsTable(response.data);
                updatePagination(response.pagination);

                // 如果需要滚动到表格位置（分页点击时）
                if (scrollToTable) {
                    scrollToMaintenanceTable();
                }
            }
        })
        .fail(function() {
            $('#maintenanceRecordsBody').html('<tr><td colspan="10" class="text-center text-danger">加载失败</td></tr>');
            $('#maintenancePagination').empty();
        });
}

// 更新维保记录表格
function updateMaintenanceRecordsTable(data) {
    const tbody = $('#maintenanceRecordsBody');
    tbody.empty();
    
    if (data.length === 0) {
        tbody.html('<tr><td colspan="10" class="text-center">暂无数据</td></tr>');
        return;
    }

    data.forEach(function(item) {
        const stockBadge = getStockStatusBadge(item.stock_status, item.stock_text);
        const daysText = item.days_remaining !== null ?
            (item.days_remaining < 0 ? `逾期${Math.abs(item.days_remaining)}天` : `${item.days_remaining}天`) :
            '-';

        const row = `
            <tr>
                <td>${item.area_name || '-'}</td>
                <td>${item.equipment_name || '-'}</td>
                <td><strong>${item.spare_part_name || '-'}</strong></td>
                <td>${item.maintenance_date || '-'}</td>
                <td>${item.next_maintenance_date || '-'}</td>
                <td>${item.technician || '-'}</td>
                <td>${item.quantity_used || 1}</td>
                <td>${daysText}</td>
                <td>${stockBadge}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="quickMaintenance(${item.spare_part_id})">
                        <i class="fas fa-wrench"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// 获取优先级徽章
function getPriorityBadge(daysRemaining) {
    if (daysRemaining === null) return '<span class="badge bg-secondary">无记录</span>';
    if (daysRemaining < 0) return '<span class="badge bg-danger">紧急</span>';
    if (daysRemaining <= 7) return '<span class="badge bg-danger">紧急</span>';
    if (daysRemaining <= 30) return '<span class="badge bg-warning">重要</span>';
    return '<span class="badge bg-success">正常</span>';
}

// 获取库存状态徽章
function getStockStatusBadge(status, text) {
    const badgeClass = {
        'out_of_stock': 'bg-danger',
        'low_stock': 'bg-warning',
        'sufficient': 'bg-success'
    };
    return `<span class="badge ${badgeClass[status] || 'bg-secondary'}">${text || '未知'}</span>`;
}

// 更新分页组件
function updatePagination(pagination) {
    const paginationContainer = $('#maintenancePagination');
    const recordsInfo = $('#recordsInfo');

    // 更新记录信息
    const start = (pagination.current_page - 1) * pagination.per_page + 1;
    const end = Math.min(pagination.current_page * pagination.per_page, pagination.total_records);
    recordsInfo.text(`显示第 ${start}-${end} 条，共 ${pagination.total_records} 条记录`);

    // 清空分页容器
    paginationContainer.empty();

    if (pagination.total_pages <= 1) {
        return; // 只有一页或没有数据时不显示分页
    }

    // 上一页按钮
    const prevDisabled = !pagination.has_prev ? 'disabled' : '';
    paginationContainer.append(`
        <li class="page-item ${prevDisabled}">
            <a class="page-link" href="#" onclick="return handlePaginationClick(${pagination.current_page - 1}, event)" aria-label="上一页">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>
    `);

    // 页码按钮
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

    // 如果不是从第1页开始，显示第1页和省略号
    if (startPage > 1) {
        paginationContainer.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="return handlePaginationClick(1, event)">1</a>
            </li>
        `);
        if (startPage > 2) {
            paginationContainer.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
        }
    }

    // 显示当前页附近的页码
    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === pagination.current_page ? 'active' : '';
        paginationContainer.append(`
            <li class="page-item ${activeClass}">
                <a class="page-link" href="#" onclick="return handlePaginationClick(${i}, event)">${i}</a>
            </li>
        `);
    }

    // 如果不是到最后一页，显示省略号和最后一页
    if (endPage < pagination.total_pages) {
        if (endPage < pagination.total_pages - 1) {
            paginationContainer.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
        }
        paginationContainer.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="return handlePaginationClick(${pagination.total_pages}, event)">${pagination.total_pages}</a>
            </li>
        `);
    }

    // 下一页按钮
    const nextDisabled = !pagination.has_next ? 'disabled' : '';
    paginationContainer.append(`
        <li class="page-item ${nextDisabled}">
            <a class="page-link" href="#" onclick="return handlePaginationClick(${pagination.current_page + 1}, event)" aria-label="下一页">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
    `);
}

// 滚动到维保记录表格
function scrollToMaintenanceTable() {
    const cardElement = document.getElementById('maintenanceRecordsCard');
    if (cardElement) {
        // 使用平滑滚动，并留出一些顶部空间
        const offsetTop = cardElement.offsetTop - 80; // 减去80px，留出导航栏空间
        window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
        });
    }
}

// 分页点击处理函数
function handlePaginationClick(page, event) {
    // 阻止默认的链接跳转行为
    if (event) {
        event.preventDefault();
    }

    // 加载指定页面的数据，并滚动到表格位置
    loadMaintenanceRecords(page, true);

    return false; // 确保不会触发页面跳转
}

// 快速维保
function quickMaintenance(sparePartId) {
    $('#spare-part-select').val(sparePartId);
    $('#addMaintenanceModal').modal('show');
}

// 全局变量标记计划来源
let planFromAlert = false;
let alertSourceInfo = null;

// 从维保提醒创建维保计划
function createMaintenancePlanFromAlert(sparePartName, areaName, equipmentName) {
    // 标记这个计划来自维保提醒
    planFromAlert = true;
    alertSourceInfo = {
        sparePartName: sparePartName,
        areaName: areaName,
        equipmentName: equipmentName
    };

    // 打开创建维保计划模态框
    $('#createPlanModal').modal('show');

    // 预填充区域信息
    setTimeout(() => {
        // 先选择区域
        $('#plan-area-select option').each(function() {
            if ($(this).text() === areaName) {
                $('#plan-area-select').val($(this).val());
                $('#plan-area-select').trigger('change');

                // 等待设备列表加载后选择设备
                setTimeout(() => {
                    $('#plan-equipment-select option').each(function() {
                        if ($(this).text() === equipmentName) {
                            $('#plan-equipment-select').val($(this).val());
                            return false;
                        }
                    });
                }, 500);

                return false;
            }
        });

        // 选择零备件
        $('#plan-spare-part-select option').each(function() {
            if ($(this).text() === sparePartName) {
                $('#plan-spare-part-select').val($(this).val());
                return false;
            }
        });

        // 设置默认计划日期为今天
        document.getElementById('planned-date').valueAsDate = new Date();
    }, 100);
}

// 加载维保计划
function loadMaintenancePlans(status = 'pending') {
    console.log('开始加载维保计划，状态:', status);
    $.get('/api/maintenance/plans?status=' + status)
        .done(function(response) {
            console.log('维保计划API响应:', response);
            if (response.success) {
                console.log('维保计划数据:', response.data);
                updateMaintenancePlansTable(response.data);
            } else {
                console.error('维保计划API返回错误:', response.error);
                $('#maintenancePlansTableBody').html('<tr><td colspan="7" class="text-center text-danger">API错误: ' + response.error + '</td></tr>');
            }
        })
        .fail(function(xhr, status, error) {
            console.error('维保计划加载失败:', status, error);
            $('#maintenancePlansTableBody').html('<tr><td colspan="7" class="text-center text-danger">加载失败: ' + error + '</td></tr>');
        });
}

// 更新维保计划表格
function updateMaintenancePlansTable(data) {
    console.log('开始更新维保计划表格，数据条数:', data.length);
    const tbody = $('#maintenancePlansTableBody');
    tbody.empty();

    if (data.length === 0) {
        console.log('没有维保计划数据');
        tbody.html('<tr><td colspan="7" class="text-center text-muted">暂无维保计划</td></tr>');
        return;
    }

    data.forEach(function(item) {
        const statusBadge = item.plan_status === 'pending' ?
            '<span class="badge bg-warning">待执行</span>' :
            '<span class="badge bg-success">已完成</span>';

        const row = `
            <tr>
                <td>${item.spare_part_name}</td>
                <td>${item.area_name}</td>
                <td>${item.equipment_name}</td>
                <td>${item.planned_date}</td>
                <td>${statusBadge}</td>
                <td>${item.notes || '-'}</td>
                <td>
                    ${item.plan_status === 'pending' ?
                        `<button class="btn btn-sm btn-success" onclick="executePlan(${item.id})">
                            <i class="fas fa-play"></i> 执行
                        </button>` :
                        '<span class="text-muted">已完成</span>'
                    }
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// 执行维保计划
function executePlan(planId) {
    // 获取维保计划详情
    $.get(`/api/maintenance/plan/${planId}`)
        .done(function(response) {
            if (response.success) {
                const plan = response.data;

                // 填充计划信息
                $('#plan-info').html(`
                    <strong>零备件：</strong>${plan.spare_part_name}<br>
                    <strong>设备：</strong>${plan.area_name} - ${plan.equipment_name}<br>
                    <strong>计划日期：</strong>${plan.planned_date}<br>
                    <strong>维保周期：</strong>${plan.maintenance_cycle_months}个月<br>
                    <strong>计划备注：</strong>${plan.notes || '无'}
                `);

                // 设置表单数据
                $('#execute-plan-id').val(planId);
                $('#execute-maintenance-date').val(new Date().toISOString().split('T')[0]); // 默认今天
                $('#execute-technician').val('');
                $('#execute-quantity-used').val(1);
                $('#execute-notes').val('');

                // 显示模态框
                $('#executePlanModal').modal('show');
            } else {
                alert('获取维保计划详情失败: ' + response.error);
            }
        })
        .fail(function() {
            alert('获取维保计划详情失败，请稍后重试');
        });
}

// 提交执行维保计划
function submitExecutePlan() {
    const planId = $('#execute-plan-id').val();
    const formData = {
        maintenance_date: $('#execute-maintenance-date').val(),
        technician: $('#execute-technician').val(),
        quantity_used: parseInt($('#execute-quantity-used').val()),
        notes: $('#execute-notes').val()
    };

    if (!formData.maintenance_date) {
        alert('请选择维保日期');
        return;
    }

    if (isNaN(formData.quantity_used) || formData.quantity_used < 1) {
        alert('请输入有效的使用数量');
        return;
    }

    $.ajax({
        url: `/api/maintenance/plan/${planId}/execute`,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                alert('维保计划执行成功！');
                $('#executePlanModal').modal('hide');
                $('#executePlanForm')[0].reset();

                // 刷新相关数据
                loadMaintenancePlans();
                loadMaintenanceRecords();
                loadMaintenanceAlerts(30);

                // 通知其他页面刷新数据
                localStorage.setItem('maintenanceDataUpdated', Date.now().toString());
            } else {
                alert('执行失败: ' + response.error);
            }
        },
        error: function(xhr) {
            let errorMsg = '执行失败，请稍后重试';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMsg = '执行失败: ' + xhr.responseJSON.error;
            }
            alert(errorMsg);
        }
    });
}

// 加载区域选项
function loadAreasForSelect() {
    $.get('/api/areas')
        .done(function(response) {
            if (response.success) {
                // 为维保记录模态框加载区域
                const select = $('#area-select');
                select.empty().append('<option value="">请选择区域</option>');
                response.data.forEach(function(area) {
                    select.append(`<option value="${area.id}">${area.name}</option>`);
                });

                // 为维保计划模态框加载区域
                const planSelect = $('#plan-area-select');
                planSelect.empty().append('<option value="">请选择区域</option>');
                response.data.forEach(function(area) {
                    planSelect.append(`<option value="${area.id}">${area.name}</option>`);
                });
            }
        });
}

// 加载设备选项
function loadEquipmentForArea() {
    const areaId = $('#area-select').val();
    const equipmentSelect = $('#equipment-select');

    if (!areaId) {
        equipmentSelect.empty().append('<option value="">请先选择区域</option>');
        return;
    }

    $.get(`/api/equipment/${areaId}`)
        .done(function(response) {
            if (response.success) {
                equipmentSelect.empty().append('<option value="">请选择设备</option>');
                response.data.forEach(function(equipment) {
                    equipmentSelect.append(`<option value="${equipment.id}">${equipment.name}</option>`);
                });
            }
        });
}

// 加载零备件选项
function loadSparePartsForSelect() {
    $.get('/api/spare-parts')
        .done(function(response) {
            if (response.success) {
                // 为维保记录模态框加载零备件
                const select = $('#spare-part-select');
                select.empty().append('<option value="">请选择零备件</option>');
                response.data.forEach(function(part) {
                    select.append(`<option value="${part.id}">${part.name}</option>`);
                });

                // 为维保计划模态框加载零备件
                const planSelect = $('#plan-spare-part-select');
                planSelect.empty().append('<option value="">请选择零备件</option>');
                response.data.forEach(function(part) {
                    planSelect.append(`<option value="${part.id}">${part.name} (${part.maintenance_cycle_months}个月周期)</option>`);
                });
            }
        });
}

// 提交维保记录
function submitMaintenanceRecord() {
    const formData = {
        spare_part_id: $('#spare-part-select').val(),
        area_id: $('#area-select').val(),
        equipment_id: $('#equipment-select').val(),
        maintenance_date: $('#maintenance-date').val(),
        technician: $('#technician').val(),
        notes: $('#notes').val(),
        quantity_used: parseInt($('#quantity-used').val()) || 1
    };

    if (!formData.spare_part_id || !formData.area_id || !formData.equipment_id || !formData.maintenance_date) {
        alert('请填写所有必填字段');
        return;
    }
    
    $.ajax({
        url: '/api/maintenance/record',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                alert('维保记录添加成功');
                $('#addMaintenanceModal').modal('hide');
                $('#addMaintenanceForm')[0].reset();
                loadMaintenanceAlerts(currentDays);
                loadMaintenanceRecords();
            } else {
                alert('添加失败: ' + response.error);
            }
        },
        error: function() {
            alert('添加失败，请稍后重试');
        }
    });
}

// 为维保计划加载设备选项
function loadEquipmentForPlan() {
    const areaId = $('#plan-area-select').val();
    const equipmentSelect = $('#plan-equipment-select');

    if (!areaId) {
        equipmentSelect.empty().append('<option value="">请先选择区域</option>');
        return;
    }

    $.get(`/api/equipment/${areaId}`)
        .done(function(response) {
            if (response.success) {
                equipmentSelect.empty().append('<option value="">请选择设备</option>');
                response.data.forEach(function(equipment) {
                    equipmentSelect.append(`<option value="${equipment.id}">${equipment.name}</option>`);
                });
            }
        });
}

// 提交维保计划
function submitMaintenancePlan() {
    const formData = {
        spare_part_id: parseInt($('#plan-spare-part-select').val()),
        area_id: parseInt($('#plan-area-select').val()),
        equipment_id: parseInt($('#plan-equipment-select').val()),
        planned_date: $('#planned-date').val(),
        notes: $('#plan-notes').val(),
        from_alert: planFromAlert,
        alert_source: alertSourceInfo
    };

    if (!formData.spare_part_id || !formData.area_id || !formData.equipment_id || !formData.planned_date) {
        alert('请填写所有必填项');
        return;
    }

    $.ajax({
        url: '/api/maintenance/plan',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                alert('维保计划创建成功');
                $('#createPlanModal').modal('hide');
                $('#createPlanForm')[0].reset();
                loadMaintenancePlans();

                // 重置标记变量
                planFromAlert = false;
                alertSourceInfo = null;

                // 通知其他页面刷新数据
                localStorage.setItem('maintenanceDataUpdated', Date.now().toString());

                // 如果是从维保提醒创建的，刷新维保提醒数据
                if (response.from_alert) {
                    loadMaintenanceAlerts();
                }
            } else {
                alert('创建失败: ' + response.error);
            }
        },
        error: function() {
            alert('创建失败，请稍后重试');
        }
    });
}
</script>
{% endblock %}
