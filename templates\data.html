{% extends "base.html" %}

{% block title %}数据管理 - 设备零备件周期管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-database me-2"></i>数据管理</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary" onclick="refreshAllData()">
            <i class="fas fa-sync-alt me-1"></i>刷新数据
        </button>
    </div>
</div>

<!-- 区域管理 -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <button class="btn btn-link text-decoration-none p-0 text-white" type="button" data-bs-toggle="collapse" data-bs-target="#areasCollapse">
                <i class="fas fa-map-marker-alt me-2"></i>区域管理
                <i class="fas fa-chevron-down ms-2"></i>
            </button>
        </h5>
        <button class="btn btn-success btn-sm" onclick="showAddAreaModal()">
            <i class="fas fa-plus me-1"></i>新增区域
        </button>
    </div>
    <div id="areasCollapse" class="collapse show">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>区域名称</th>
                            <th>描述</th>
                            <th>设备数量</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="areasTableBody">
                        <tr>
                            <td colspan="5" class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 设备管理 -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <button class="btn btn-link text-decoration-none p-0 text-white collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#equipmentCollapse">
                <i class="fas fa-cogs me-2"></i>设备管理
                <i class="fas fa-chevron-down ms-2"></i>
            </button>
        </h5>
        <button class="btn btn-success btn-sm" onclick="showAddEquipmentModal()">
            <i class="fas fa-plus me-1"></i>新增设备
        </button>
    </div>
    <div id="equipmentCollapse" class="collapse">
        <div class="card-body">
            <div class="mb-3">
                <select class="form-select" id="areaFilter" onchange="filterEquipmentByArea()">
                    <option value="">选择区域查看设备</option>
                </select>
            </div>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>设备名称</th>
                            <th>设备类型</th>
                            <th>所属区域</th>
                            <th>零备件数量</th>
                            <th>描述</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="equipmentTableBody">
                        <tr>
                            <td colspan="6" class="text-center text-muted">请选择区域</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 零备件管理 -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <button class="btn btn-link text-decoration-none p-0 text-white collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sparePartsCollapse">
                <i class="fas fa-wrench me-2"></i>零备件管理
                <i class="fas fa-chevron-down ms-2"></i>
            </button>
        </h5>
        <button class="btn btn-success btn-sm" onclick="showAddSparePartModal()">
            <i class="fas fa-plus me-1"></i>新增零备件
        </button>
    </div>
    <div id="sparePartsCollapse" class="collapse">
        <div class="card-body">
            <div class="mb-3">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    零备件现在是独立管理的，可以用于多个不同的设备
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>零备件名称</th>
                            <th>维保周期</th>
                            <th>描述</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="sparePartsTableBody">
                        <tr>
                            <td colspan="5" class="text-center text-muted">加载中...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 系统信息 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <button class="btn btn-link text-decoration-none p-0 text-white collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#systemInfoCollapse">
                <i class="fas fa-info-circle me-2"></i>系统信息
                <i class="fas fa-chevron-down ms-2"></i>
            </button>
        </h5>
    </div>
    <div id="systemInfoCollapse" class="collapse">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6><i class="fas fa-chart-pie me-2"></i>数据统计</h6>
                    <ul class="list-unstyled">
                        <li><strong>区域总数:</strong> <span id="total-areas">-</span></li>
                        <li><strong>设备总数:</strong> <span id="total-equipment">-</span></li>
                        <li><strong>零备件总数:</strong> <span id="total-spare-parts">-</span></li>
                        <li><strong>维保记录总数:</strong> <span id="total-maintenance-records">-</span></li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6><i class="fas fa-server me-2"></i>系统状态</h6>
                    <ul class="list-unstyled">
                        <li><strong>数据库状态:</strong> <span class="badge bg-success">正常</span></li>
                        <li><strong>最后更新:</strong> <span id="last-update">-</span></li>
                        <li><strong>系统版本:</strong> v1.0</li>
                        <li><strong>技术栈:</strong> Python + Flask + SQLite</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 区域管理模态框 -->
<div class="modal fade" id="areaModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="areaModalTitle">新增区域</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="areaForm">
                    <input type="hidden" id="areaId">
                    <div class="mb-3">
                        <label for="areaName" class="form-label">区域名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="areaName" required>
                    </div>
                    <div class="mb-3">
                        <label for="areaDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="areaDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveArea()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 设备管理模态框 -->
<div class="modal fade" id="equipmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="equipmentModalTitle">新增设备</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="equipmentForm">
                    <input type="hidden" id="equipmentId">
                    <div class="mb-3">
                        <label for="equipmentAreaId" class="form-label">所属区域 <span class="text-danger">*</span></label>
                        <select class="form-select" id="equipmentAreaId" required>
                            <option value="">请选择区域</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="equipmentName" class="form-label">设备名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="equipmentName" required>
                    </div>
                    <div class="mb-3">
                        <label for="equipmentType" class="form-label">设备类型 <span class="text-danger">*</span></label>
                        <select class="form-select" id="equipmentType" required>
                            <option value="">请选择类型</option>
                            <option value="堆垛机">堆垛机</option>
                            <option value="AGV小车">AGV小车</option>
                            <option value="入库皮带">入库皮带</option>
                            <option value="出库皮带">出库皮带</option>
                            <option value="分拣设备">分拣设备</option>
                            <option value="包装设备">包装设备</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="equipmentDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="equipmentDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveEquipment()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 零备件管理模态框 -->
<div class="modal fade" id="sparePartModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sparePartModalTitle">新增零备件</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="sparePartForm">
                    <input type="hidden" id="sparePartId">
                    <div class="mb-3">
                        <label for="sparePartName" class="form-label">零备件名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="sparePartName" required placeholder="请输入零备件名称">
                    </div>
                    <div class="mb-3">
                        <label for="sparePartCycle" class="form-label">维保周期（月） <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="sparePartCycle" min="1" required placeholder="请输入维保周期">
                    </div>
                    <div class="mb-3">
                        <label for="sparePartDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="sparePartDescription" rows="3" placeholder="请输入零备件的详细描述（可选）"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveSparePart()">保存</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// 数据管理页面 - 修复版本 2025-07-22-16:10
let areasData = [];
let equipmentData = [];
let sparePartsData = [];

// 页面加载完成后初始化
$(document).ready(function() {
    loadAreasData();
    loadAllSpareParts();
    loadSystemStats();
    updateLastUpdateTime();
});

// 加载区域数据
function loadAreasData() {
    $.get('/api/areas')
        .done(function(response) {
            if (response.success) {
                areasData = response.data;
                updateAreasTable(response.data);
                updateAreaFilter(response.data);
            }
        })
        .fail(function() {
            $('#areasTableBody').html('<tr><td colspan="4" class="text-center text-danger">加载失败</td></tr>');
        });
}

// 更新区域表格
function updateAreasTable(data) {
    const tbody = $('#areasTableBody');
    tbody.empty();

    if (data.length === 0) {
        tbody.html('<tr><td colspan="5" class="text-center">暂无数据</td></tr>');
        return;
    }

    data.forEach(function(area) {
        const row = `
            <tr>
                <td><strong>${area.name}</strong></td>
                <td>${area.description || '-'}</td>
                <td><span class="badge bg-info" id="equipment-count-${area.id}">-</span></td>
                <td>${area.created_at || '-'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editArea(${area.id}, '${area.name}', '${area.description || ''}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteArea(${area.id}, '${area.name}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);

        // 加载每个区域的设备数量
        loadEquipmentCount(area.id);
    });
}

// 加载设备数量
function loadEquipmentCount(areaId) {
    $.get(`/api/equipment/${areaId}`)
        .done(function(response) {
            if (response.success) {
                $(`#equipment-count-${areaId}`).text(response.data.length);
            }
        });
}

// 更新区域过滤器
function updateAreaFilter(data) {
    const select = $('#areaFilter');
    select.empty().append('<option value="">选择区域查看设备</option>');
    
    data.forEach(function(area) {
        select.append(`<option value="${area.id}">${area.name}</option>`);
    });
}

// 根据区域过滤设备
function filterEquipmentByArea() {
    const areaId = $('#areaFilter').val();
    
    if (!areaId) {
        $('#equipmentTableBody').html('<tr><td colspan="6" class="text-center text-muted">请选择区域</td></tr>');
        return;
    }
    
    $.get(`/api/equipment/${areaId}`)
        .done(function(response) {
            if (response.success) {
                equipmentData = response.data;
                updateEquipmentTable(response.data);
            }
        })
        .fail(function() {
            $('#equipmentTableBody').html('<tr><td colspan="6" class="text-center text-danger">加载失败</td></tr>');
        });
}

// 更新设备表格
function updateEquipmentTable(data) {
    const tbody = $('#equipmentTableBody');
    tbody.empty();

    if (data.length === 0) {
        tbody.html('<tr><td colspan="6" class="text-center">该区域暂无设备</td></tr>');
        return;
    }

    const selectedArea = areasData.find(area => area.id == $('#areaFilter').val());

    data.forEach(function(equipment) {
        const row = `
            <tr>
                <td><strong>${equipment.name}</strong></td>
                <td><span class="badge bg-primary">${equipment.type}</span></td>
                <td>${selectedArea ? selectedArea.name : '-'}</td>
                <td><span class="badge bg-success spare-parts-count">通用</span></td>
                <td>${equipment.description || '-'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editEquipment(${equipment.id}, ${equipment.area_id}, '${equipment.name}', '${equipment.type}', '${equipment.description || ''}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteEquipment(${equipment.id}, '${equipment.name}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);

        // 零备件现在是独立管理，不再按设备统计
    });
}

// 零备件现在是独立管理，不再按设备统计数量
// 显示统一的零备件总数
function updateSparePartsCountDisplay() {
    if (sparePartsData && sparePartsData.length > 0) {
        // 为所有设备显示相同的零备件总数
        $('.spare-parts-count').text(sparePartsData.length);
    }
}

// 设备过滤器已移除，零备件现在是独立管理

// 加载所有零备件
function loadAllSpareParts() {
    $.get('/api/spare-parts')
        .done(function(response) {
            if (response.success) {
                sparePartsData = response.data;
                updateSparePartsTable(response.data);
                updateSparePartsCountDisplay(); // 更新零备件数量显示
            }
        })
        .fail(function() {
            $('#sparePartsTableBody').html('<tr><td colspan="5" class="text-center text-danger">加载失败</td></tr>');
        });
}

// 更新零备件表格
function updateSparePartsTable(data) {
    const tbody = $('#sparePartsTableBody');
    tbody.empty();

    if (data.length === 0) {
        tbody.html('<tr><td colspan="5" class="text-center">暂无零备件</td></tr>');
        return;
    }

    data.forEach(function(sparePart) {
        const createdAt = sparePart.created_at ? new Date(sparePart.created_at).toLocaleDateString() : '-';
        const description = (sparePart.description || '').replace(/'/g, '\\\'');
        const row = `
            <tr>
                <td><strong>${sparePart.name}</strong></td>
                <td><span class="badge bg-primary">${sparePart.maintenance_cycle_months}个月</span></td>
                <td>${sparePart.description || '-'}</td>
                <td>${createdAt}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editSparePart(${sparePart.id}, '${sparePart.name}', ${sparePart.maintenance_cycle_months}, '${description}')" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteSparePart(${sparePart.id}, '${sparePart.name}')" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// 加载系统统计
function loadSystemStats() {
    // 加载概览数据获取统计信息
    $.get('/api/overview')
        .done(function(response) {
            if (response.success) {
                $('#total-spare-parts').text(response.data.total_parts || 0);
            }
        });
    
    // 加载维保状态获取维保记录数
    $.get('/api/maintenance/status')
        .done(function(response) {
            if (response.success) {
                const recordsWithMaintenance = response.data.filter(item => item.maintenance_date);
                $('#total-maintenance-records').text(recordsWithMaintenance.length);
            }
        });
}

// 更新最后更新时间
function updateLastUpdateTime() {
    const now = new Date();
    const timeString = now.toLocaleString('zh-CN');
    $('#last-update').text(timeString);
}

// 刷新所有数据
function refreshAllData() {
    loadAreasData();
    loadAllSpareParts();
    loadSystemStats();
    updateLastUpdateTime();

    // 重置过滤器
    $('#areaFilter').val('');
    $('#equipmentTableBody').html('<tr><td colspan="6" class="text-center text-muted">请选择区域</td></tr>');
}

// 监听区域数据加载完成后更新统计
$(document).on('ajaxComplete', function() {
    setTimeout(function() {
        $('#total-areas').text(areasData.length);
        
        // 计算设备总数
        let totalEquipment = 0;
        areasData.forEach(function(area) {
            const count = parseInt($(`#equipment-count-${area.id}`).text()) || 0;
            totalEquipment += count;
        });
        $('#total-equipment').text(totalEquipment);
    }, 1000);
});

// ==================== 区域管理 CRUD 功能 ====================
function showAddAreaModal() {
    $('#areaModalTitle').text('新增区域');
    $('#areaForm')[0].reset();
    $('#areaId').val('');
    $('#areaModal').modal('show');
}

function editArea(id, name, description) {
    $('#areaModalTitle').text('编辑区域');
    $('#areaId').val(id);
    $('#areaName').val(name);
    $('#areaDescription').val(description);
    $('#areaModal').modal('show');
}

function saveArea() {
    const id = $('#areaId').val();
    const name = $('#areaName').val().trim();
    const description = $('#areaDescription').val().trim();

    if (!name) {
        alert('请输入区域名称');
        return;
    }

    const isEdit = !!id;
    const url = isEdit ? `/api/areas/${id}` : '/api/areas';
    const method = isEdit ? 'PUT' : 'POST';

    $.ajax({
        url: url,
        method: method,
        contentType: 'application/json',
        data: JSON.stringify({
            name: name,
            description: description
        }),
        success: function(response) {
            if (response.success) {
                $('#areaModal').modal('hide');
                loadAreasData(); // 重新加载数据
                alert(response.message);
            } else {
                alert('操作失败: ' + response.error);
            }
        },
        error: function() {
            alert('操作失败，请稍后重试');
        }
    });
}

function deleteArea(id, name) {
    if (!confirm(`确定要删除区域"${name}"吗？\n注意：删除区域前请先删除该区域下的所有设备。`)) {
        return;
    }

    $.ajax({
        url: `/api/areas/${id}`,
        method: 'DELETE',
        success: function(response) {
            if (response.success) {
                loadAreasData(); // 重新加载数据
                alert(response.message);
            } else {
                alert('删除失败: ' + response.error);
            }
        },
        error: function() {
            alert('删除失败，请稍后重试');
        }
    });
}

// ==================== 设备管理 CRUD 功能 ====================
function showAddEquipmentModal() {
    $('#equipmentModalTitle').text('新增设备');
    $('#equipmentForm')[0].reset();
    $('#equipmentId').val('');

    // 填充区域选项
    const areaSelect = $('#equipmentAreaId');
    areaSelect.empty().append('<option value="">请选择区域</option>');
    areasData.forEach(function(area) {
        areaSelect.append(`<option value="${area.id}">${area.name}</option>`);
    });

    $('#equipmentModal').modal('show');
}

function editEquipment(id, areaId, name, type, description) {
    $('#equipmentModalTitle').text('编辑设备');
    $('#equipmentId').val(id);
    $('#equipmentName').val(name);
    $('#equipmentType').val(type);
    $('#equipmentDescription').val(description);

    // 填充区域选项
    const areaSelect = $('#equipmentAreaId');
    areaSelect.empty().append('<option value="">请选择区域</option>');
    areasData.forEach(function(area) {
        areaSelect.append(`<option value="${area.id}">${area.name}</option>`);
    });
    areaSelect.val(areaId);

    $('#equipmentModal').modal('show');
}

function saveEquipment() {
    const id = $('#equipmentId').val();
    const areaId = $('#equipmentAreaId').val();
    const name = $('#equipmentName').val().trim();
    const type = $('#equipmentType').val();
    const description = $('#equipmentDescription').val().trim();

    if (!areaId || !name || !type) {
        alert('请填写所有必填项');
        return;
    }

    const isEdit = !!id;
    const url = isEdit ? `/api/equipment/${id}` : '/api/equipment';
    const method = isEdit ? 'PUT' : 'POST';

    $.ajax({
        url: url,
        method: method,
        contentType: 'application/json',
        data: JSON.stringify({
            area_id: parseInt(areaId),
            name: name,
            type: type,
            description: description
        }),
        success: function(response) {
            if (response.success) {
                $('#equipmentModal').modal('hide');
                // 重新加载当前区域的设备数据
                const currentAreaId = $('#areaFilter').val();
                if (currentAreaId) {
                    filterEquipmentByArea();
                }
                loadAreasData(); // 重新加载区域数据以更新设备数量
                alert(response.message);
            } else {
                alert('操作失败: ' + response.error);
            }
        },
        error: function() {
            alert('操作失败，请稍后重试');
        }
    });
}

function deleteEquipment(id, name) {
    if (!confirm(`确定要删除设备"${name}"吗？\n注意：删除设备可能会影响相关的维保记录。`)) {
        return;
    }

    $.ajax({
        url: `/api/equipment/${id}`,
        method: 'DELETE',
        success: function(response) {
            if (response.success) {
                // 重新加载当前区域的设备数据
                const currentAreaId = $('#areaFilter').val();
                if (currentAreaId) {
                    filterEquipmentByArea();
                }
                loadAreasData(); // 重新加载区域数据以更新设备数量
                alert(response.message);
            } else {
                alert('删除失败: ' + response.error);
            }
        },
        error: function() {
            alert('删除失败，请稍后重试');
        }
    });
}

// ==================== 零备件管理 CRUD 功能 ====================
function showAddSparePartModal() {
    $('#sparePartModalTitle').text('新增零备件');
    $('#sparePartForm')[0].reset();
    $('#sparePartId').val('');
    $('#sparePartModal').modal('show');
}

function editSparePart(id, name, cycle, description) {
    $('#sparePartModalTitle').text('编辑零备件');
    $('#sparePartId').val(id);
    $('#sparePartName').val(name);
    $('#sparePartCycle').val(cycle);
    $('#sparePartDescription').val(description);
    $('#sparePartModal').modal('show');
}

// 零备件不再绑定设备，此函数已移除

function saveSparePart() {
    const id = $('#sparePartId').val();
    const name = $('#sparePartName').val().trim();
    const cycle = $('#sparePartCycle').val();
    const description = $('#sparePartDescription').val().trim();

    if (!name || !cycle) {
        alert('请填写所有必填项');
        return;
    }

    if (parseInt(cycle) <= 0) {
        alert('维保周期必须大于0');
        return;
    }

    const isEdit = !!id;
    const url = isEdit ? `/api/spare-parts/${id}` : '/api/spare-parts';
    const method = isEdit ? 'PUT' : 'POST';

    $.ajax({
        url: url,
        method: method,
        contentType: 'application/json',
        data: JSON.stringify({
            name: name,
            maintenance_cycle_months: parseInt(cycle),
            description: description
        }),
        success: function(response) {
            if (response.success) {
                $('#sparePartModal').modal('hide');
                // 重新加载零备件数据
                loadAllSpareParts();
                alert(response.message);
            } else {
                alert('操作失败: ' + response.error);
            }
        },
        error: function() {
            alert('操作失败，请稍后重试');
        }
    });
}

function deleteSparePart(id, name) {
    if (!confirm(`确定要删除零备件"${name}"吗？\n注意：删除零备件可能会影响相关的维保记录和计划。`)) {
        return;
    }

    $.ajax({
        url: `/api/spare-parts/${id}`,
        method: 'DELETE',
        success: function(response) {
            if (response.success) {
                // 重新加载零备件数据
                loadAllSpareParts();
                alert(response.message);
            } else {
                alert('删除失败: ' + response.error);
            }
        },
        error: function() {
            alert('删除失败，请稍后重试');
        }
    });
}
</script>
{% endblock %}
