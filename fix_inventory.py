#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复库存问题 - 为维保计划中的零备件添加库存
"""

import sqlite3
from datetime import datetime

def fix_inventory_for_maintenance_plans():
    """为维保计划中的零备件添加库存"""
    print("🔧 修复维保计划相关的库存问题...")
    
    try:
        # 连接数据库
        conn = sqlite3.connect('equipment_maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 1. 查找待执行的维保计划
        print("\n1️⃣ 查找待执行的维保计划...")
        cursor.execute("""
            SELECT 
                mp.id,
                mp.spare_part_id,
                sp.name as spare_part_name,
                mp.area_id,
                a.name as area_name,
                mp.equipment_id,
                e.name as equipment_name,
                mp.planned_date,
                mp.plan_status
            FROM maintenance_plans mp
            LEFT JOIN spare_parts sp ON mp.spare_part_id = sp.id
            LEFT JOIN areas a ON mp.area_id = a.id
            LEFT JOIN equipment e ON mp.equipment_id = e.id
            WHERE mp.plan_status = 'pending'
            ORDER BY mp.planned_date
        """)
        
        pending_plans = cursor.fetchall()
        print(f"找到 {len(pending_plans)} 个待执行的维保计划")
        
        if not pending_plans:
            print("没有待执行的维保计划")
            return
        
        # 2. 检查每个计划对应的库存状态
        print("\n2️⃣ 检查库存状态...")
        for plan in pending_plans:
            print(f"\n计划ID:{plan['id']} - {plan['spare_part_name']}")
            
            # 检查全局库存
            cursor.execute("""
                SELECT total_quantity, min_quantity 
                FROM inventory_global 
                WHERE spare_part_name = ?
            """, (plan['spare_part_name'],))
            
            inventory = cursor.fetchone()
            
            if inventory:
                print(f"   当前库存: {inventory['total_quantity']}")
                print(f"   最小库存: {inventory['min_quantity']}")
                
                if inventory['total_quantity'] <= 0:
                    print(f"   ⚠️  库存不足，需要补充")
                    
                    # 补充库存到最小库存的2倍
                    new_quantity = max(inventory['min_quantity'] * 2, 10)
                    
                    cursor.execute("""
                        UPDATE inventory_global 
                        SET total_quantity = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE spare_part_name = ?
                    """, (new_quantity, plan['spare_part_name']))
                    
                    print(f"   ✅ 已补充库存到: {new_quantity}")
                else:
                    print(f"   ✅ 库存充足")
            else:
                print(f"   ❌ 未找到库存记录，创建新记录")
                
                # 创建新的库存记录
                cursor.execute("""
                    INSERT INTO inventory_global 
                    (spare_part_name, total_quantity, min_quantity, created_at, updated_at)
                    VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """, (plan['spare_part_name'], 20, 5))
                
                print(f"   ✅ 已创建库存记录: 库存=20, 最小库存=5")
        
        # 3. 提交更改
        conn.commit()
        print(f"\n3️⃣ 库存修复完成!")
        
        # 4. 验证修复结果
        print(f"\n4️⃣ 验证修复结果...")
        for plan in pending_plans:
            cursor.execute("""
                SELECT total_quantity 
                FROM inventory_global 
                WHERE spare_part_name = ?
            """, (plan['spare_part_name'],))
            
            inventory = cursor.fetchone()
            if inventory:
                print(f"   {plan['spare_part_name']}: {inventory['total_quantity']}")
            else:
                print(f"   {plan['spare_part_name']}: 未找到库存")
        
        conn.close()
        print("\n✅ 库存修复完成!")
        
    except Exception as e:
        print(f"❌ 修复库存时出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔧 维保计划库存修复工具")
    print("=" * 50)
    
    fix_inventory_for_maintenance_plans()
    
    print("\n🎉 修复完成! 现在可以尝试执行维保计划了。")

if __name__ == "__main__":
    main()
