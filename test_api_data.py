#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API数据
"""

import requests
import json

def test_api_data():
    """测试API数据"""
    print("测试维保状态API...")
    
    try:
        response = requests.get('http://127.0.0.1:5000/api/maintenance/status')
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API正常，返回 {len(data['data'])} 条记录")
            
            # 显示前10条记录
            print("\n前10条维保记录:")
            for i, item in enumerate(data['data'][:10]):
                notes = item.get('notes', '无备注')
                print(f"{i+1:2d}. {item['spare_part_name']:12s} - {item['area_name']:6s} - {item['status']:8s} - {notes}")
            
            # 统计不同状态
            status_count = {}
            for item in data['data']:
                status = item['status']
                status_count[status] = status_count.get(status, 0) + 1
            
            print(f"\n状态统计:")
            for status, count in status_count.items():
                print(f"  {status}: {count}")
                
        else:
            print(f"❌ API错误，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    print("\n测试统计API...")
    try:
        response = requests.get('http://127.0.0.1:5000/api/statistics')
        if response.status_code == 200:
            data = response.json()
            print("✅ 统计API正常")
            
            print("\n区域分布:")
            for area in data['data']['area_stats']:
                print(f"  {area['area']}: {area['count']} 条维保记录")
                
            print("\n状态分布:")
            for status in data['data']['status_stats']:
                print(f"  {status['status']}: {status['count']} 条记录")
        else:
            print(f"❌ 统计API错误，状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 统计API请求失败: {e}")

if __name__ == "__main__":
    test_api_data()
