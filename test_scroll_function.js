// 测试滚动功能的JavaScript代码
// 在浏览器控制台中运行此代码来测试滚动功能

console.log('🧪 开始测试维保记录分页滚动功能...');

// 1. 检查必要的函数是否存在
function checkFunctions() {
    console.log('\n1️⃣ 检查函数是否存在...');
    
    const functions = [
        'loadMaintenanceRecords',
        'handlePaginationClick', 
        'scrollToMaintenanceTable',
        'updatePagination'
    ];
    
    functions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✅ ${funcName} 函数存在`);
        } else {
            console.log(`❌ ${funcName} 函数不存在`);
        }
    });
}

// 2. 检查DOM元素是否存在
function checkElements() {
    console.log('\n2️⃣ 检查DOM元素是否存在...');
    
    const elements = [
        'maintenanceRecordsCard',
        'maintenanceRecordsTable',
        'maintenanceRecordsBody',
        'maintenancePagination',
        'recordsInfo'
    ];
    
    elements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            console.log(`✅ #${elementId} 元素存在`);
        } else {
            console.log(`❌ #${elementId} 元素不存在`);
        }
    });
}

// 3. 测试滚动函数
function testScrollFunction() {
    console.log('\n3️⃣ 测试滚动函数...');
    
    if (typeof scrollToMaintenanceTable === 'function') {
        try {
            // 先滚动到页面顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
            
            setTimeout(() => {
                console.log('📍 当前滚动位置:', window.pageYOffset);
                console.log('🎯 执行滚动到维保记录表格...');
                
                scrollToMaintenanceTable();
                
                setTimeout(() => {
                    console.log('📍 滚动后位置:', window.pageYOffset);
                    
                    const cardElement = document.getElementById('maintenanceRecordsCard');
                    if (cardElement) {
                        const cardTop = cardElement.offsetTop;
                        console.log('📦 维保记录卡片位置:', cardTop);
                        console.log('📏 滚动偏移量:', window.pageYOffset - cardTop);
                    }
                }, 1000);
            }, 1000);
            
        } catch (error) {
            console.log('❌ 滚动函数执行出错:', error);
        }
    } else {
        console.log('❌ scrollToMaintenanceTable 函数不存在');
    }
}

// 4. 测试分页点击处理
function testPaginationClick() {
    console.log('\n4️⃣ 测试分页点击处理...');
    
    if (typeof handlePaginationClick === 'function') {
        try {
            console.log('🖱️ 模拟点击第2页...');
            
            // 创建模拟事件对象
            const mockEvent = {
                preventDefault: () => console.log('✅ preventDefault 被调用')
            };
            
            const result = handlePaginationClick(2, mockEvent);
            console.log('🔄 分页点击处理结果:', result);
            
        } catch (error) {
            console.log('❌ 分页点击处理出错:', error);
        }
    } else {
        console.log('❌ handlePaginationClick 函数不存在');
    }
}

// 5. 检查分页按钮的onclick属性
function checkPaginationButtons() {
    console.log('\n5️⃣ 检查分页按钮...');
    
    const paginationContainer = document.getElementById('maintenancePagination');
    if (paginationContainer) {
        const buttons = paginationContainer.querySelectorAll('a.page-link');
        console.log(`📊 找到 ${buttons.length} 个分页按钮`);
        
        buttons.forEach((button, index) => {
            const onclick = button.getAttribute('onclick');
            if (onclick && onclick.includes('handlePaginationClick')) {
                console.log(`✅ 按钮 ${index + 1}: ${onclick.substring(0, 50)}...`);
            } else {
                console.log(`❌ 按钮 ${index + 1}: onclick 属性不正确`);
            }
        });
    } else {
        console.log('❌ 分页容器不存在');
    }
}

// 运行所有测试
function runAllTests() {
    console.log('🚀 开始运行所有测试...');
    
    checkFunctions();
    checkElements();
    checkPaginationButtons();
    
    // 延迟执行需要用户交互的测试
    setTimeout(() => {
        testScrollFunction();
    }, 2000);
    
    setTimeout(() => {
        testPaginationClick();
    }, 4000);
    
    console.log('\n📝 测试说明:');
    console.log('- 如果所有检查都显示 ✅，说明功能实现正确');
    console.log('- 如果有 ❌，请检查对应的代码实现');
    console.log('- 滚动测试会在2秒后自动执行');
    console.log('- 分页点击测试会在4秒后自动执行');
}

// 导出测试函数到全局作用域
window.testScrollFunctions = {
    runAllTests,
    checkFunctions,
    checkElements,
    testScrollFunction,
    testPaginationClick,
    checkPaginationButtons
};

console.log('\n📋 可用的测试命令:');
console.log('- testScrollFunctions.runAllTests() - 运行所有测试');
console.log('- testScrollFunctions.checkFunctions() - 检查函数');
console.log('- testScrollFunctions.checkElements() - 检查DOM元素');
console.log('- testScrollFunctions.testScrollFunction() - 测试滚动');
console.log('- testScrollFunctions.testPaginationClick() - 测试分页点击');
console.log('- testScrollFunctions.checkPaginationButtons() - 检查分页按钮');

console.log('\n🎯 要开始测试，请在控制台输入: testScrollFunctions.runAllTests()');
