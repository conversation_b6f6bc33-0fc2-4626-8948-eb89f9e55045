#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试设备管理API接口
"""

import requests
import json

def test_equipment_api():
    """测试设备管理API"""
    print("🧪 测试设备管理API接口...")
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 1. 测试获取区域列表
        print("\n1️⃣ 测试获取区域列表...")
        response = requests.get(f"{base_url}/api/areas")
        
        if response.status_code != 200:
            print(f"❌ 获取区域列表失败: {response.status_code}")
            return False
        
        areas_data = response.json()
        if not areas_data.get('success'):
            print(f"❌ 获取区域列表失败: {areas_data.get('error')}")
            return False
        
        areas = areas_data['data']
        print(f"✅ 成功获取 {len(areas)} 个区域:")
        for area in areas:
            print(f"   ID:{area['id']} - {area['name']}")
        
        # 2. 测试获取每个区域的设备
        print("\n2️⃣ 测试获取各区域设备...")
        for area in areas:
            area_id = area['id']
            area_name = area['name']
            
            print(f"\n测试区域: {area_name} (ID: {area_id})")
            response = requests.get(f"{base_url}/api/equipment/{area_id}")
            
            if response.status_code != 200:
                print(f"❌ 获取区域 {area_name} 设备失败: {response.status_code}")
                print(f"   响应内容: {response.text}")
                continue
            
            equipment_data = response.json()
            if not equipment_data.get('success'):
                print(f"❌ 获取区域 {area_name} 设备失败: {equipment_data.get('error')}")
                continue
            
            equipment_list = equipment_data['data']
            print(f"✅ 区域 {area_name} 有 {len(equipment_list)} 个设备:")
            for eq in equipment_list:
                print(f"   - {eq['name']} ({eq['type']})")
        
        # 3. 测试获取零备件API
        print("\n3️⃣ 测试获取零备件API...")
        if areas:
            # 先获取第一个区域的设备
            first_area = areas[0]
            response = requests.get(f"{base_url}/api/equipment/{first_area['id']}")
            
            if response.status_code == 200:
                equipment_data = response.json()
                if equipment_data.get('success') and equipment_data['data']:
                    first_equipment = equipment_data['data'][0]
                    equipment_id = first_equipment['id']
                    
                    print(f"测试设备: {first_equipment['name']} (ID: {equipment_id})")
                    response = requests.get(f"{base_url}/api/spare-parts/{equipment_id}")
                    
                    if response.status_code == 200:
                        spare_parts_data = response.json()
                        if spare_parts_data.get('success'):
                            spare_parts = spare_parts_data['data']
                            print(f"✅ 设备 {first_equipment['name']} 有 {len(spare_parts)} 个零备件")
                        else:
                            print(f"❌ 获取零备件失败: {spare_parts_data.get('error')}")
                    else:
                        print(f"❌ 获取零备件失败: {response.status_code}")
        
        print(f"\n🎉 设备管理API测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

def test_direct_database():
    """直接测试数据库"""
    print("\n🗄️ 直接测试数据库...")
    
    try:
        from database import DatabaseManager
        
        db = DatabaseManager()
        
        # 测试获取区域
        areas = db.get_areas()
        print(f"✅ 数据库中有 {len(areas)} 个区域")
        
        # 测试获取每个区域的设备
        for area in areas:
            equipment = db.get_equipment_by_area(area['id'])
            print(f"   {area['name']}: {len(equipment)} 个设备")
            for eq in equipment:
                print(f"     - {eq['name']} ({eq['type']})")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 设备管理API测试工具")
    print("=" * 40)
    
    # 先测试数据库
    db_success = test_direct_database()
    
    if db_success:
        # 再测试API
        api_success = test_equipment_api()
        
        if api_success:
            print("\n✅ 所有测试通过!")
        else:
            print("\n❌ API测试失败")
    else:
        print("\n❌ 数据库测试失败")

if __name__ == "__main__":
    main()
