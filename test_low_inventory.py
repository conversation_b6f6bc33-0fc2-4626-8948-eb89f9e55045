#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试库存不足的情况
"""

import sqlite3
import requests
import json
from datetime import datetime

def create_low_inventory_scenario():
    """创建库存不足的测试场景"""
    print("🔧 创建库存不足的测试场景...")
    
    try:
        # 连接数据库
        conn = sqlite3.connect('equipment_maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 1. 找到一个待执行的维保计划
        cursor.execute("""
            SELECT 
                mp.id,
                mp.spare_part_id,
                sp.name as spare_part_name
            FROM maintenance_plans mp
            LEFT JOIN spare_parts sp ON mp.spare_part_id = sp.id
            WHERE mp.plan_status = 'pending'
            LIMIT 1
        """)
        
        plan = cursor.fetchone()
        if not plan:
            print("❌ 没有找到待执行的维保计划")
            return None
        
        print(f"✅ 选择维保计划: ID={plan['id']}, 零备件={plan['spare_part_name']}")
        
        # 2. 将该零备件的库存设置为0
        cursor.execute("""
            UPDATE inventory_global 
            SET total_quantity = 0, updated_at = CURRENT_TIMESTAMP
            WHERE spare_part_name = ?
        """, (plan['spare_part_name'],))
        
        # 如果没有库存记录，创建一个库存为0的记录
        if cursor.rowcount == 0:
            cursor.execute("""
                INSERT INTO inventory_global 
                (spare_part_name, total_quantity, min_quantity, created_at, updated_at)
                VALUES (?, 0, 5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """, (plan['spare_part_name'],))
        
        conn.commit()
        print(f"✅ 已将 '{plan['spare_part_name']}' 的库存设置为0")
        
        conn.close()
        return {
            'plan_id': plan['id'],
            'spare_part_name': plan['spare_part_name'],
            'spare_part_id': plan['spare_part_id']
        }
        
    except Exception as e:
        print(f"❌ 创建测试场景时出错: {e}")
        return None

def test_low_inventory_scenario(scenario_info):
    """测试库存不足的场景"""
    print(f"\n🧪 测试库存不足场景...")
    
    base_url = "http://127.0.0.1:5000"
    plan_id = scenario_info['plan_id']
    spare_part_name = scenario_info['spare_part_name']
    
    try:
        # 1. 验证库存确实为0
        print(f"\n1️⃣ 验证库存状态...")
        response = requests.get(f"{base_url}/api/inventory/status")
        
        if response.status_code == 200:
            inventory_data = response.json()
            if inventory_data.get('success'):
                inventory_list = inventory_data['data']
                # 查找对应的零备件库存
                spare_part_inventory = None
                for item in inventory_list:
                    if item['spare_part_name'] == spare_part_name:
                        spare_part_inventory = item
                        break
                
                if spare_part_inventory:
                    print(f"✅ 库存状态:")
                    print(f"   零备件: {spare_part_inventory['spare_part_name']}")
                    print(f"   当前库存: {spare_part_inventory['total_quantity']}")
                    print(f"   库存状态: {spare_part_inventory['status_text']}")
                else:
                    print(f"⚠️  未找到零备件 '{spare_part_name}' 的库存信息")
        
        # 2. 测试执行维保计划（应该显示库存不足的错误）
        print(f"\n2️⃣ 测试执行维保计划（预期：库存不足错误）...")
        execute_data = {
            "maintenance_date": datetime.now().strftime('%Y-%m-%d'),
            "technician": "测试维保员",
            "quantity_used": 1,
            "notes": "测试库存不足场景"
        }
        
        response = requests.post(
            f"{base_url}/api/maintenance/plan/{plan_id}/execute",
            headers={'Content-Type': 'application/json'},
            data=json.dumps(execute_data)
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 400:
            result = response.json()
            if not result.get('success'):
                error_message = result.get('error', '')
                if '库存不足' in error_message or '没有库存记录' in error_message:
                    print("✅ 维保计划执行的库存检查功能正常工作!")
                    print(f"   错误信息: {error_message}")
                    return True
                else:
                    print(f"⚠️  错误信息不符合预期: {error_message}")
            else:
                print("⚠️  预期失败但返回成功")
        elif response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("❌ 预期库存不足但执行成功了！库存检查功能有问题！")
                return False
            else:
                print(f"❌ 执行失败: {result.get('error')}")
        else:
            print(f"❌ 意外的HTTP状态码: {response.status_code}")
        
        return False
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def restore_inventory(scenario_info):
    """恢复库存到正常水平"""
    print(f"\n🔧 恢复库存到正常水平...")
    
    try:
        # 连接数据库
        conn = sqlite3.connect('equipment_maintenance.db')
        cursor = conn.cursor()
        
        # 恢复库存到20
        cursor.execute("""
            UPDATE inventory_global 
            SET total_quantity = 20, updated_at = CURRENT_TIMESTAMP
            WHERE spare_part_name = ?
        """, (scenario_info['spare_part_name'],))
        
        conn.commit()
        conn.close()
        
        print(f"✅ 已将 '{scenario_info['spare_part_name']}' 的库存恢复到20")
        
    except Exception as e:
        print(f"❌ 恢复库存时出错: {e}")

def main():
    """主函数"""
    print("🧪 库存不足场景测试工具")
    print("=" * 50)
    
    # 1. 创建库存不足的测试场景
    scenario_info = create_low_inventory_scenario()
    if not scenario_info:
        print("❌ 无法创建测试场景")
        return
    
    # 2. 测试库存不足的场景
    success = test_low_inventory_scenario(scenario_info)
    
    # 3. 恢复库存
    restore_inventory(scenario_info)
    
    if success:
        print("\n✅ 库存检查功能正常工作!")
    else:
        print("\n❌ 库存检查功能有问题!")

if __name__ == "__main__":
    main()
