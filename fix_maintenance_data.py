#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
from datetime import datetime, timed<PERSON>ta

def fix_maintenance_data():
    """修复维保数据问题"""
    db_path = 'equipment_maintenance.db'
    
    if not os.path.exists(db_path):
        print("数据库文件不存在，请先运行应用初始化数据库")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("=== 检查数据库表结构 ===")
        
        # 检查maintenance_records表结构
        cursor.execute("PRAGMA table_info(maintenance_records)")
        columns = cursor.fetchall()
        print("maintenance_records表结构:")
        for col in columns:
            print(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else ''}")
        
        # 检查是否有area_id和equipment_id列
        column_names = [col[1] for col in columns]
        has_area_id = 'area_id' in column_names
        has_equipment_id = 'equipment_id' in column_names
        
        print(f"是否有area_id列: {has_area_id}")
        print(f"是否有equipment_id列: {has_equipment_id}")
        
        # 如果缺少列，添加它们
        if not has_area_id:
            print("添加area_id列...")
            cursor.execute("ALTER TABLE maintenance_records ADD COLUMN area_id INTEGER")
        
        if not has_equipment_id:
            print("添加equipment_id列...")
            cursor.execute("ALTER TABLE maintenance_records ADD COLUMN equipment_id INTEGER")
        
        # 检查现有数据
        cursor.execute("SELECT COUNT(*) FROM maintenance_records")
        count = cursor.fetchone()[0]
        print(f"现有维保记录数量: {count}")
        
        if count == 0:
            print("=== 创建测试维保数据 ===")
            
            # 获取区域数据
            cursor.execute("SELECT id, name FROM areas")
            areas = cursor.fetchall()
            print(f"区域数量: {len(areas)}")
            
            # 获取设备数据
            cursor.execute("SELECT id, name, area_id FROM equipment")
            equipment = cursor.fetchall()
            print(f"设备数量: {len(equipment)}")
            
            # 获取零件数据
            cursor.execute("SELECT id, name, maintenance_cycle_months FROM spare_parts")
            spare_parts = cursor.fetchall()
            print(f"零件数量: {len(spare_parts)}")
            
            if areas and equipment and spare_parts:
                # 创建维保记录
                today = datetime.now().date()
                
                maintenance_records = []
                for i, (eq_id, eq_name, eq_area_id) in enumerate(equipment):
                    for j, (sp_id, sp_name, cycle_months) in enumerate(spare_parts):
                        # 计算维保日期
                        days_offset = (i * len(spare_parts) + j) * 10  # 每10天一个记录
                        maintenance_date = today - timedelta(days=days_offset)
                        next_maintenance_date = maintenance_date + timedelta(days=cycle_months * 30)
                        
                        maintenance_records.append((
                            sp_id,                    # spare_part_id
                            eq_area_id,              # area_id
                            eq_id,                   # equipment_id
                            maintenance_date.strftime('%Y-%m-%d'),  # maintenance_date
                            next_maintenance_date.strftime('%Y-%m-%d'),  # next_maintenance_date
                            f'技术员{(i + j) % 3 + 1}',  # technician
                            f'维保记录 - {eq_name} - {sp_name}',  # notes
                            1                        # quantity_used
                        ))
                
                # 插入维保记录
                if has_area_id and has_equipment_id:
                    insert_sql = """
                        INSERT INTO maintenance_records 
                        (spare_part_id, area_id, equipment_id, maintenance_date, next_maintenance_date, technician, notes, quantity_used)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """
                else:
                    insert_sql = """
                        INSERT INTO maintenance_records 
                        (spare_part_id, maintenance_date, next_maintenance_date, technician, notes)
                        VALUES (?, ?, ?, ?, ?)
                    """
                    maintenance_records = [(r[0], r[3], r[4], r[5], r[6]) for r in maintenance_records]
                
                cursor.executemany(insert_sql, maintenance_records)
                print(f"插入了 {len(maintenance_records)} 条维保记录")
            else:
                print("缺少基础数据（区域、设备或零件），无法创建维保记录")
        
        # 验证数据
        print("\n=== 验证维保数据 ===")
        cursor.execute("SELECT COUNT(*) FROM maintenance_records")
        count = cursor.fetchone()[0]
        print(f"维保记录总数: {count}")
        
        if count > 0:
            # 显示前几条记录
            if has_area_id and has_equipment_id:
                cursor.execute("""
                    SELECT mr.id, sp.name as spare_part_name, a.name as area_name, 
                           e.name as equipment_name, mr.maintenance_date, mr.next_maintenance_date
                    FROM maintenance_records mr
                    JOIN spare_parts sp ON mr.spare_part_id = sp.id
                    LEFT JOIN areas a ON mr.area_id = a.id
                    LEFT JOIN equipment e ON mr.equipment_id = e.id
                    LIMIT 3
                """)
            else:
                cursor.execute("""
                    SELECT mr.id, sp.name as spare_part_name, mr.maintenance_date, mr.next_maintenance_date
                    FROM maintenance_records mr
                    JOIN spare_parts sp ON mr.spare_part_id = sp.id
                    LIMIT 3
                """)
            
            records = cursor.fetchall()
            print("前3条维保记录:")
            for record in records:
                print(f"  {record}")
        
        conn.commit()
        print("\n=== 修复完成 ===")
        print("现在可以刷新浏览器页面查看维保图表")
        
    except Exception as e:
        print(f"修复过程中出错: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    fix_maintenance_data()
