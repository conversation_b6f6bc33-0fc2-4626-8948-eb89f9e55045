#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复设备名称重复问题
"""

import sqlite3

def main():
    print("=== 快速修复设备名称重复问题 ===")
    
    # 连接数据库
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 直接更新设备名称
        updates = [
            ("成品区堆垛机1号", 1),
            ("成品区堆垛机2号", 2),
            ("成品区堆垛机3号", 3),
            ("成品区堆垛机4号", 4),
            ("成品区入库皮带", 5),
            ("辅料区堆垛机1号", 6),
            ("辅料区堆垛机2号", 7),
            ("辅料区堆垛机3号", 8),
            ("辅料区AGV小车", 9),
            ("片烟区堆垛机1号", 10),
            ("片烟区堆垛机2号", 11),
            ("片烟区堆垛机3号", 12)
        ]
        
        for new_name, eq_id in updates:
            cursor.execute("UPDATE equipment SET name = ? WHERE id = ?", (new_name, eq_id))
        
        conn.commit()
        print(f"✅ 成功更新了 {len(updates)} 个设备的名称")
        
        # 验证更新结果
        cursor.execute("SELECT id, name FROM equipment ORDER BY id")
        equipment = cursor.fetchall()
        
        print("\n更新后的设备列表:")
        for eq in equipment:
            print(f"ID: {eq[0]}, 名称: {eq[1]}")
        
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    main()
