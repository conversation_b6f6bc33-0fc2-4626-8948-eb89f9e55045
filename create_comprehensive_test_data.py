#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建符合新需求的完整测试数据
包含所有状态的展示效果：
- 全局统一库存管理
- 首次维保计划和后续维保记录
- 各种维保状态（正常、警告、逾期）
- 各种库存状态（充足、不足、缺货）
"""

import sqlite3
from datetime import datetime, timedelta
import random

def create_comprehensive_test_data():
    """创建全面的测试数据"""
    print("🚀 开始创建全面的测试数据...")
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 1. 创建维保计划数据
        create_maintenance_plans(cursor)
        
        # 2. 创建多样化的维保记录
        create_diverse_maintenance_records(cursor)
        
        # 3. 更新全局库存数据
        update_global_inventory_data(cursor)
        
        conn.commit()
        print("✅ 全面测试数据创建完成！")
        
        # 4. 验证数据
        verify_test_data(cursor)
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()
    
    return True

def create_maintenance_plans(cursor):
    """创建维保计划数据"""
    print("📋 创建维保计划数据...")
    
    # 获取零备件数据
    cursor.execute("""
        SELECT sp.id, sp.name, sp.equipment_id, e.area_id
        FROM spare_parts sp
        JOIN equipment e ON sp.equipment_id = e.id
        LIMIT 10
    """)
    spare_parts = cursor.fetchall()
    
    plans_data = []
    today = datetime.now().date()
    
    for i, (sp_id, sp_name, eq_id, area_id) in enumerate(spare_parts):
        # 创建一些待执行的维保计划
        if i < 5:
            planned_date = today + timedelta(days=random.randint(1, 30))
            status = 'pending'
            notes = f"计划维保 {sp_name}"
        else:
            # 创建一些已完成的维保计划
            planned_date = today - timedelta(days=random.randint(1, 10))
            status = 'completed'
            notes = f"已完成维保 {sp_name}"
        
        plans_data.append((
            sp_id, area_id, eq_id,
            planned_date.strftime('%Y-%m-%d'),
            status, notes
        ))
    
    # 插入维保计划数据
    cursor.executemany("""
        INSERT INTO maintenance_plans 
        (spare_part_id, area_id, equipment_id, planned_date, plan_status, notes)
        VALUES (?, ?, ?, ?, ?, ?)
    """, plans_data)
    
    print(f"   创建了 {len(plans_data)} 个维保计划")

def create_diverse_maintenance_records(cursor):
    """创建多样化的维保记录"""
    print("🔧 创建多样化的维保记录...")
    
    # 获取零备件数据
    cursor.execute("""
        SELECT sp.id, sp.name, sp.maintenance_cycle_months, sp.equipment_id, e.area_id
        FROM spare_parts sp
        JOIN equipment e ON sp.equipment_id = e.id
    """)
    spare_parts = cursor.fetchall()
    
    # 清除现有的维保记录
    cursor.execute("DELETE FROM maintenance_records")
    
    maintenance_data = []
    today = datetime.now().date()
    technicians = ["张师傅", "李师傅", "王师傅", "赵师傅", "刘师傅", "陈师傅"]
    
    for sp_id, sp_name, cycle_months, eq_id, area_id in spare_parts:
        # 为每个零备件创建不同状态的维保记录
        
        # 1. 正常状态 (30%)
        if random.random() < 0.3:
            days_ago = random.randint(1, cycle_months * 15)  # 周期的前半段
            maintenance_date = today - timedelta(days=days_ago)
            next_maintenance_date = maintenance_date + timedelta(days=cycle_months * 30)
            
        # 2. 即将到期状态 (40%)
        elif random.random() < 0.7:
            # 下次维保在30天内
            next_days = random.randint(1, 30)
            next_maintenance_date = today + timedelta(days=next_days)
            maintenance_date = next_maintenance_date - timedelta(days=cycle_months * 30)
            
        # 3. 逾期状态 (30%)
        else:
            # 下次维保已经过期
            overdue_days = random.randint(1, 60)
            next_maintenance_date = today - timedelta(days=overdue_days)
            maintenance_date = next_maintenance_date - timedelta(days=cycle_months * 30)
        
        technician = random.choice(technicians)
        quantity_used = random.randint(1, 3)
        notes = f"定期维保 {sp_name}，状态良好"
        
        maintenance_data.append((
            sp_id, area_id, eq_id,
            maintenance_date.strftime('%Y-%m-%d'),
            next_maintenance_date.strftime('%Y-%m-%d'),
            technician, notes, quantity_used,
            False, 'completed'  # is_first_maintenance, maintenance_status
        ))
    
    # 插入维保记录
    cursor.executemany("""
        INSERT INTO maintenance_records
        (spare_part_id, area_id, equipment_id, maintenance_date, next_maintenance_date,
         technician, notes, quantity_used, is_first_maintenance, maintenance_status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, maintenance_data)
    
    print(f"   创建了 {len(maintenance_data)} 条维保记录")

def update_global_inventory_data(cursor):
    """更新全局库存数据，创建不同的库存状态"""
    print("📦 更新全局库存数据...")
    
    # 获取现有的全局库存
    cursor.execute("SELECT spare_part_name FROM inventory_global")
    spare_part_names = [row[0] for row in cursor.fetchall()]
    
    inventory_updates = []
    locations = ["中央仓库A区", "中央仓库B区", "中央仓库C区", "紧急备件库", "维修车间"]
    
    for i, spare_part_name in enumerate(spare_part_names):
        # 创建不同的库存状态
        if i % 4 == 0:
            # 25% 缺货状态
            quantity = 0
            min_quantity = random.randint(2, 5)
        elif i % 4 == 1:
            # 25% 库存不足状态
            min_quantity = random.randint(3, 8)
            quantity = random.randint(1, min_quantity)
        else:
            # 50% 库存充足状态
            min_quantity = random.randint(2, 5)
            quantity = random.randint(min_quantity + 1, min_quantity + 20)
        
        location = random.choice(locations)
        
        inventory_updates.append((quantity, min_quantity, location, spare_part_name))
    
    # 更新库存数据
    cursor.executemany("""
        UPDATE inventory_global
        SET total_quantity = ?, min_quantity = ?, location = ?, updated_at = CURRENT_TIMESTAMP
        WHERE spare_part_name = ?
    """, inventory_updates)
    
    print(f"   更新了 {len(inventory_updates)} 个库存记录")

def verify_test_data(cursor):
    """验证测试数据"""
    print("🔍 验证测试数据...")
    
    # 检查维保计划
    cursor.execute("SELECT COUNT(*) FROM maintenance_plans WHERE plan_status = 'pending'")
    pending_plans = cursor.fetchone()[0]
    cursor.execute("SELECT COUNT(*) FROM maintenance_plans WHERE plan_status = 'completed'")
    completed_plans = cursor.fetchone()[0]
    print(f"   维保计划: 待执行 {pending_plans} 个, 已完成 {completed_plans} 个")
    
    # 检查维保记录状态分布
    today = datetime.now().date()
    cursor.execute("""
        SELECT
            SUM(CASE WHEN next_maintenance_date < ? THEN 1 ELSE 0 END) as overdue,
            SUM(CASE WHEN next_maintenance_date BETWEEN ? AND ? THEN 1 ELSE 0 END) as upcoming,
            SUM(CASE WHEN next_maintenance_date > ? THEN 1 ELSE 0 END) as normal
        FROM maintenance_records
    """, (today, today, today + timedelta(days=30), today + timedelta(days=30)))
    
    overdue, upcoming, normal = cursor.fetchone()
    print(f"   维保状态: 逾期 {overdue} 个, 即将到期 {upcoming} 个, 正常 {normal} 个")
    
    # 检查库存状态分布
    cursor.execute("""
        SELECT
            SUM(CASE WHEN total_quantity = 0 THEN 1 ELSE 0 END) as out_of_stock,
            SUM(CASE WHEN total_quantity > 0 AND total_quantity <= min_quantity THEN 1 ELSE 0 END) as low_stock,
            SUM(CASE WHEN total_quantity > min_quantity THEN 1 ELSE 0 END) as sufficient
        FROM inventory_global
    """)
    
    out_of_stock, low_stock, sufficient = cursor.fetchone()
    print(f"   库存状态: 缺货 {out_of_stock} 个, 不足 {low_stock} 个, 充足 {sufficient} 个")

def main():
    """主函数"""
    print("📊 创建符合新需求的完整测试数据")
    print("=" * 50)
    
    success = create_comprehensive_test_data()
    
    if success:
        print("=" * 50)
        print("🎉 测试数据创建成功！")
        print("现在可以启动系统查看各种状态的展示效果：")
        print("  • 维保计划管理")
        print("  • 多种维保状态（正常、警告、逾期）")
        print("  • 多种库存状态（充足、不足、缺货）")
        print("  • 全局统一库存管理")
    else:
        print("❌ 测试数据创建失败")
    
    return success

if __name__ == "__main__":
    main()
