#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def update_areas():
    """更新区域数据为正确的区域名称"""
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        print("=== 更新区域数据 ===")
        
        # 更新区域数据
        areas_update = [
            (1, '品区', '成品生产区域'),
            (2, '辅料区', '辅助材料处理区域'),
            (3, '片烟区', '片烟加工区域')
        ]
        
        # 先清除现有区域数据
        cursor.execute('DELETE FROM areas')
        
        # 插入新的区域数据
        cursor.executemany('''
            INSERT INTO areas (id, name, description) 
            VALUES (?, ?, ?)
        ''', areas_update)
        
        # 更新设备的区域分配，使其更符合实际
        equipment_updates = [
            (1, '冲压机001', 'hydraulic_press', 1, '品区大型液压冲压设备'),
            (2, '焊接机器人002', 'welding_robot', 1, '品区自动化焊接机器人'),
            (3, '包装机003', 'packaging_machine', 1, '品区自动包装设备'),
            (4, '搅拌机004', 'mixer', 2, '辅料区原料搅拌设备'),
            (5, '传送带005', 'conveyor', 2, '辅料区自动传送系统'),
            (6, '切片机006', 'cutting_machine', 3, '片烟区烟叶切片设备')
        ]
        
        # 清除现有设备数据
        cursor.execute('DELETE FROM equipment')
        
        # 插入新的设备数据
        cursor.executemany('''
            INSERT INTO equipment (id, name, type, area_id, description) 
            VALUES (?, ?, ?, ?, ?)
        ''', equipment_updates)
        
        conn.commit()
        
        # 验证更新结果
        print("\n=== 验证更新结果 ===")
        
        cursor.execute('SELECT id, name, description FROM areas ORDER BY id')
        areas = cursor.fetchall()
        print("区域列表:")
        for area in areas:
            print(f"  {area[0]}. {area[1]} - {area[2]}")
        
        cursor.execute('''
            SELECT e.name, a.name as area_name, e.description 
            FROM equipment e 
            JOIN areas a ON e.area_id = a.id 
            ORDER BY a.id, e.id
        ''')
        equipment = cursor.fetchall()
        print(f"\n设备分布:")
        current_area = None
        for eq in equipment:
            if eq[1] != current_area:
                current_area = eq[1]
                print(f"\n{current_area}:")
            print(f"  - {eq[0]}: {eq[2]}")
        
        print("\n✅ 区域数据更新完成！")
        
    except Exception as e:
        print(f"❌ 更新区域数据时出错: {e}")
        conn.rollback()
        
    finally:
        conn.close()

if __name__ == "__main__":
    update_areas()
