#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys

def test_final_data():
    """测试最终数据"""
    
    try:
        conn = sqlite3.connect('equipment_maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("=== 数据库测试结果 ===")
        
        # 检查各表的记录数量
        tables = ['areas', 'equipment', 'spare_parts', 'inventory', 'maintenance_records']
        for table in tables:
            cursor.execute(f'SELECT COUNT(*) FROM {table}')
            count = cursor.fetchone()[0]
            print(f"{table}: {count} 条记录")
        
        # 检查库存状态分布
        print("\n=== 库存状态分布 ===")
        cursor.execute('''
            SELECT 
                CASE 
                    WHEN quantity = 0 THEN '缺货'
                    WHEN quantity <= min_quantity THEN '库存不足'
                    ELSE '充足'
                END as status,
                COUNT(*) as count
            FROM inventory 
            GROUP BY 
                CASE 
                    WHEN quantity = 0 THEN '缺货'
                    WHEN quantity <= min_quantity THEN '库存不足'
                    ELSE '充足'
                END
        ''')
        
        for row in cursor.fetchall():
            print(f"{row['status']}: {row['count']} 条")
        
        # 检查维保状态分布
        print("\n=== 维保状态分布 ===")
        cursor.execute('''
            SELECT 
                CASE 
                    WHEN next_maintenance_date < date('now') THEN '已过期'
                    WHEN next_maintenance_date <= date('now', '+7 days') THEN '即将到期'
                    ELSE '正常'
                END as status,
                COUNT(*) as count
            FROM maintenance_records 
            GROUP BY 
                CASE 
                    WHEN next_maintenance_date < date('now') THEN '已过期'
                    WHEN next_maintenance_date <= date('now', '+7 days') THEN '即将到期'
                    ELSE '正常'
                END
        ''')
        
        for row in cursor.fetchall():
            print(f"{row['status']}: {row['count']} 条")
        
        # 显示一些具体的库存数据
        print("\n=== 库存详情示例 ===")
        cursor.execute('''
            SELECT 
                sp.name,
                i.quantity,
                i.min_quantity,
                CASE 
                    WHEN i.quantity = 0 THEN '缺货'
                    WHEN i.quantity <= i.min_quantity THEN '库存不足'
                    ELSE '充足'
                END as status
            FROM inventory i
            JOIN spare_parts sp ON i.spare_part_id = sp.id
            ORDER BY i.quantity
            LIMIT 6
        ''')
        
        for row in cursor.fetchall():
            print(f"{row['name']}: {row['quantity']}/{row['min_quantity']} - {row['status']}")
        
        # 显示一些具体的维保数据
        print("\n=== 维保记录示例 ===")
        cursor.execute('''
            SELECT 
                e.name as equipment_name,
                sp.name as spare_part_name,
                mr.next_maintenance_date,
                CASE 
                    WHEN mr.next_maintenance_date < date('now') THEN '已过期'
                    WHEN mr.next_maintenance_date <= date('now', '+7 days') THEN '即将到期'
                    ELSE '正常'
                END as status
            FROM maintenance_records mr
            JOIN equipment e ON mr.equipment_id = e.id
            JOIN spare_parts sp ON mr.spare_part_id = sp.id
            ORDER BY mr.next_maintenance_date
            LIMIT 6
        ''')
        
        for row in cursor.fetchall():
            print(f"{row['equipment_name']} - {row['spare_part_name']}: {row['next_maintenance_date']} ({row['status']})")
        
        print("\n✅ 数据测试完成！")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 测试数据时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_final_data()
