#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备零备件周期管理系统 - 快速启动脚本
"""

import os
import sys
import subprocess
import sqlite3
from datetime import datetime

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✅ Python版本检查通过: {sys.version.split()[0]}")
    return True

def check_database():
    """检查数据库是否存在"""
    db_path = "maintenance_system.db"
    if os.path.exists(db_path):
        print("✅ 数据库文件已存在")
        return True
    else:
        print("⚠️  数据库文件不存在，正在初始化...")
        return initialize_database()

def initialize_database():
    """初始化数据库"""
    try:
        from initialize_system import initialize_complete_system
        initialize_complete_system()
        print("✅ 数据库初始化完成")
        return True
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False

def install_dependencies():
    """安装依赖包"""
    print("📦 检查并安装依赖包...")
    
    required_packages = [
        'flask',
        'flask-cors'
    ]
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"⚠️  正在安装 {package}...")
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError:
                print(f"❌ {package} 安装失败")
                return False
    
    return True

def check_port_available(port=5000):
    """检查端口是否可用"""
    import socket
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('localhost', port))
            print(f"✅ 端口 {port} 可用")
            return True
        except OSError:
            print(f"⚠️  端口 {port} 被占用，尝试使用其他端口")
            return False

def start_web_server():
    """启动Web服务器"""
    print("\n🚀 启动设备零备件周期管理系统...")
    print("=" * 60)
    print("📋 系统信息:")
    print(f"   • 系统名称: 设备零备件周期管理系统")
    print(f"   • 版本: v1.0")
    print(f"   • 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   • 访问地址: http://127.0.0.1:5000")
    print(f"   • 技术栈: Python + Flask + SQLite + Bootstrap + Chart.js")
    print("=" * 60)
    print("🌟 功能特色:")
    print("   • 三大区域管理 (成品区、辅料区、片烟区)")
    print("   • 智能维保提醒和进度跟踪")
    print("   • 库存状态监控和预警")
    print("   • 可视化图表和大屏展示")
    print("   • 响应式设计，适配各种设备")
    print("=" * 60)
    print("📖 使用说明:")
    print("   • 系统概览: 查看整体状态和统计数据")
    print("   • 维保管理: 管理维保计划和记录")
    print("   • 库存管理: 监控零备件库存状态")
    print("   • 数据管理: 查看系统基础数据")
    print("=" * 60)
    print("🔧 操作提示:")
    print("   • 按 Ctrl+C 停止服务器")
    print("   • 在浏览器中打开上述地址访问系统")
    print("   • 建议使用Chrome、Firefox等现代浏览器")
    print("=" * 60)
    
    try:
        from app import app
        app.run(debug=False, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n\n👋 系统已停止运行")
        print("感谢使用设备零备件周期管理系统！")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🔧 设备零备件周期管理系统 - 启动检查")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败，请手动安装")
        return False
    
    # 检查数据库
    if not check_database():
        return False
    
    # 检查端口
    if not check_port_available():
        print("⚠️  端口被占用，但系统仍会尝试启动")
    
    print("\n✅ 所有检查完成，准备启动系统...")
    
    # 启动Web服务器
    return start_web_server()

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ 系统启动失败")
            input("按回车键退出...")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 意外错误: {e}")
        input("按回车键退出...")
        sys.exit(1)
