#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def verify_database():
    """验证数据库状态"""
    try:
        conn = sqlite3.connect('equipment_maintenance.db')
        cursor = conn.cursor()
        
        print("=== 验证数据库状态 ===")
        
        # 检查维保记录状态分布
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN DATE(next_maintenance_date) < DATE('now') THEN 1 ELSE 0 END) as overdue,
                SUM(CASE WHEN DATE(next_maintenance_date) <= DATE('now', '+30 days') AND DATE(next_maintenance_date) >= DATE('now') THEN 1 ELSE 0 END) as upcoming,
                SUM(CASE WHEN DATE(next_maintenance_date) > DATE('now', '+30 days') THEN 1 ELSE 0 END) as normal
            FROM maintenance_records
        """)
        
        result = cursor.fetchone()
        print(f"总维保记录: {result[0]}")
        print(f"逾期记录: {result[1]}")
        print(f"即将到期: {result[2]}")
        print(f"正常记录: {result[3]}")
        
        # 检查逾期记录详情
        if result[1] > 0:
            cursor.execute("""
                SELECT sp.name, e.name, a.name, mr.next_maintenance_date
                FROM maintenance_records mr
                JOIN spare_parts sp ON mr.spare_part_id = sp.id
                JOIN equipment e ON mr.equipment_id = e.id
                JOIN areas a ON mr.area_id = a.id
                WHERE DATE(mr.next_maintenance_date) < DATE('now')
                ORDER BY mr.next_maintenance_date DESC
            """)
            
            overdue_details = cursor.fetchall()
            print(f"\n逾期记录详情:")
            for detail in overdue_details:
                print(f"  - {detail[0]} | {detail[1]} | {detail[2]} | {detail[3]}")
        
        # 检查各表数据量
        tables = ['areas', 'equipment', 'spare_parts', 'inventory', 'maintenance_records']
        print(f"\n各表数据量:")
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"  {table}: {count}")
        
        conn.close()
        print("\n✅ 数据库验证完成")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")

if __name__ == "__main__":
    verify_database()
