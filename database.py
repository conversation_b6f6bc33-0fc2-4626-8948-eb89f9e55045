#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备零备件周期管理系统 - 数据库管理模块
"""

import sqlite3
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

class DatabaseManager:
    """数据库管理类"""
    
    def __init__(self, db_path: str = "equipment_maintenance.db"):
        """初始化数据库管理器"""
        self.db_path = db_path
        # 检查数据库是否存在
        db_exists = os.path.exists(db_path)
        self.init_database()

        # 检查是否需要插入初始数据
        if not db_exists or self._need_initial_data():
            self.insert_initial_data()
    
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        return conn

    def _need_initial_data(self):
        """检查是否需要插入初始数据"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # 检查区域表是否有数据
            cursor.execute("SELECT COUNT(*) FROM areas")
            areas_count = cursor.fetchone()[0]

            # 检查设备表是否有数据
            cursor.execute("SELECT COUNT(*) FROM equipment")
            equipment_count = cursor.fetchone()[0]

            # 检查零备件表是否有数据
            cursor.execute("SELECT COUNT(*) FROM spare_parts")
            parts_count = cursor.fetchone()[0]

            # 如果任何一个表为空，就需要初始数据
            return areas_count == 0 or equipment_count == 0 or parts_count == 0

        except Exception as e:
            print(f"检查初始数据需求时出错: {e}")
            return True  # 出错时默认需要初始数据
        finally:
            conn.close()
    
    def init_database(self):
        """初始化数据库表结构"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # 创建区域表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS areas (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建设备表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS equipment (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    area_id INTEGER NOT NULL,
                    name TEXT NOT NULL UNIQUE,
                    type TEXT NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (area_id) REFERENCES areas (id)
                )
            ''')
            
            # 创建零备件表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS spare_parts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    maintenance_cycle_months INTEGER NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建库存表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS inventory (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    spare_part_id INTEGER NOT NULL,
                    quantity INTEGER DEFAULT 0,
                    min_quantity INTEGER DEFAULT 1,
                    location TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (spare_part_id) REFERENCES spare_parts (id)
                )
            ''')
            
            # 创建维保记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS maintenance_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    spare_part_id INTEGER NOT NULL,
                    area_id INTEGER NOT NULL,
                    equipment_id INTEGER NOT NULL,
                    maintenance_date DATE NOT NULL,
                    next_maintenance_date DATE NOT NULL,
                    technician TEXT,
                    notes TEXT,
                    quantity_used INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (spare_part_id) REFERENCES spare_parts (id),
                    FOREIGN KEY (area_id) REFERENCES areas (id),
                    FOREIGN KEY (equipment_id) REFERENCES equipment (id)
                )
            ''')

            # 创建维保计划表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS maintenance_plans (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    spare_part_id INTEGER NOT NULL,
                    area_id INTEGER NOT NULL,
                    equipment_id INTEGER NOT NULL,
                    planned_date DATE NOT NULL,
                    plan_status TEXT DEFAULT 'pending',
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (spare_part_id) REFERENCES spare_parts (id),
                    FOREIGN KEY (area_id) REFERENCES areas (id),
                    FOREIGN KEY (equipment_id) REFERENCES equipment (id)
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_equipment_area ON equipment(area_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_spare_parts_name ON spare_parts(name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_inventory_spare_part ON inventory(spare_part_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_maintenance_spare_part ON maintenance_records(spare_part_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_maintenance_area ON maintenance_records(area_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_maintenance_equipment ON maintenance_records(equipment_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_maintenance_next_date ON maintenance_records(next_maintenance_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_plans_spare_part ON maintenance_plans(spare_part_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_plans_area ON maintenance_plans(area_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_plans_equipment ON maintenance_plans(equipment_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_plans_date ON maintenance_plans(planned_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_plans_status ON maintenance_plans(plan_status)')
            
            conn.commit()
            print("数据库初始化完成")
            
        except Exception as e:
            print(f"数据库初始化错误: {e}")
            conn.rollback()
        finally:
            conn.close()
    
    def insert_initial_data(self):
        """插入初始数据"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # 检查是否已有数据
            cursor.execute("SELECT COUNT(*) FROM areas")
            if cursor.fetchone()[0] > 0:
                print("数据库已有数据，跳过初始化")
                return
            
            # 插入区域数据
            areas = [
                ("成品区", "成品区域，包含4台堆垛机和1条入库皮带"),
                ("辅料区", "辅料区域，包含3台堆垛机和1台AGV小车"),
                ("片烟区", "片烟区域，包含3台堆垛机")
            ]
            
            for area_name, area_desc in areas:
                cursor.execute("INSERT INTO areas (name, description) VALUES (?, ?)", 
                             (area_name, area_desc))
            
            # 获取区域ID
            cursor.execute("SELECT id, name FROM areas")
            area_map = {row[1]: row[0] for row in cursor.fetchall()}
            
            # 插入设备数据
            equipment_data = [
                # 成品区设备
                (area_map["成品区"], "成品区-堆垛机1号", "堆垛机", "成品区第1台堆垛机"),
                (area_map["成品区"], "成品区-堆垛机2号", "堆垛机", "成品区第2台堆垛机"),
                (area_map["成品区"], "成品区-堆垛机3号", "堆垛机", "成品区第3台堆垛机"),
                (area_map["成品区"], "成品区-堆垛机4号", "堆垛机", "成品区第4台堆垛机"),
                (area_map["成品区"], "成品区-入库皮带1号", "入库皮带", "成品区入库皮带"),
                # 辅料区设备
                (area_map["辅料区"], "辅料区-堆垛机1号", "堆垛机", "辅料区第1台堆垛机"),
                (area_map["辅料区"], "辅料区-堆垛机2号", "堆垛机", "辅料区第2台堆垛机"),
                (area_map["辅料区"], "辅料区-堆垛机3号", "堆垛机", "辅料区第3台堆垛机"),
                (area_map["辅料区"], "辅料区-AGV小车1号", "AGV小车", "辅料区AGV小车"),
                # 片烟区设备
                (area_map["片烟区"], "片烟区-堆垛机1号", "堆垛机", "片烟区第1台堆垛机"),
                (area_map["片烟区"], "片烟区-堆垛机2号", "堆垛机", "片烟区第2台堆垛机"),
                (area_map["片烟区"], "片烟区-堆垛机3号", "堆垛机", "片烟区第3台堆垛机")
            ]
            
            for eq_data in equipment_data:
                cursor.execute("INSERT INTO equipment (area_id, name, type, description) VALUES (?, ?, ?, ?)", eq_data)

            # 获取设备ID
            cursor.execute("SELECT id, name, type FROM equipment")
            equipment_list = cursor.fetchall()

            # 插入零备件数据
            spare_parts_data = []
            for eq_id, eq_name, eq_type in equipment_list:
                if eq_type == "堆垛机":
                    spare_parts_data.extend([
                        (eq_id, "主接触器", 6, f"{eq_name}主接触器，6个月维保周期"),
                        (eq_id, "变频器", 12, f"{eq_name}变频器，12个月维保周期"),
                        (eq_id, "伺服电机", 18, f"{eq_name}伺服电机，18个月维保周期"),
                        (eq_id, "减速机", 24, f"{eq_name}减速机，24个月维保周期")
                    ])
                elif eq_type == "入库皮带":
                    spare_parts_data.extend([
                        (eq_id, "皮带", 3, f"{eq_name}皮带，3个月维保周期"),
                        (eq_id, "电机", 12, f"{eq_name}电机，12个月维保周期"),
                        (eq_id, "传感器", 6, f"{eq_name}传感器，6个月维保周期")
                    ])
                elif eq_type == "AGV小车":
                    spare_parts_data.extend([
                        (eq_id, "电池", 6, f"{eq_name}电池，6个月维保周期"),
                        (eq_id, "驱动轮", 12, f"{eq_name}驱动轮，12个月维保周期"),
                        (eq_id, "导航传感器", 18, f"{eq_name}导航传感器，18个月维保周期")
                    ])

            for sp_data in spare_parts_data:
                cursor.execute("INSERT INTO spare_parts (equipment_id, name, maintenance_cycle_months, description) VALUES (?, ?, ?, ?)", sp_data)

            conn.commit()
            print("初始数据插入完成")
            
        except Exception as e:
            print(f"初始数据插入错误: {e}")
            conn.rollback()
        finally:
            conn.close()
    
    def get_areas(self) -> List[Dict]:
        """获取所有区域"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT * FROM areas ORDER BY name")
            return [dict(row) for row in cursor.fetchall()]
        finally:
            conn.close()
    
    def get_equipment_by_area(self, area_id: int) -> List[Dict]:
        """根据区域获取设备"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT * FROM equipment WHERE area_id = ? ORDER BY name", (area_id,))
            return [dict(row) for row in cursor.fetchall()]
        finally:
            conn.close()
    
    def get_all_spare_parts(self) -> List[Dict]:
        """获取所有零备件"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute("SELECT * FROM spare_parts ORDER BY name")
            return [dict(row) for row in cursor.fetchall()]
        finally:
            conn.close()

if __name__ == "__main__":
    # 测试数据库
    db = DatabaseManager()
    db.insert_initial_data()
    print("数据库测试完成")
