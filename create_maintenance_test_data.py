#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime, timedelta
import random

def create_maintenance_test_data():
    """创建维保测试数据"""
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        print("=== 创建维保测试数据 ===")
        
        # 清空现有维保记录
        cursor.execute('DELETE FROM maintenance_records')
        print("清空现有维保记录")
        
        # 获取设备和零备件信息
        cursor.execute('SELECT id, name FROM equipment ORDER BY id')
        equipment = cursor.fetchall()
        print(f"设备数量: {len(equipment)}")
        
        cursor.execute('SELECT id, name FROM spare_parts ORDER BY id')
        spare_parts = cursor.fetchall()
        print(f"零备件数量: {len(spare_parts)}")
        
        if not equipment or not spare_parts:
            print("❌ 没有设备或零备件数据，无法创建维保记录")
            return
        
        # 创建维保记录
        today = datetime.now().date()
        maintenance_records = []
        
        # 为每个设备创建2-3条维保记录
        record_id = 1
        for eq_id, eq_name in equipment:
            num_records = random.randint(2, 3)
            
            for i in range(num_records):
                # 随机选择零备件
                part_id, part_name = random.choice(spare_parts)
                
                # 创建不同状态的维保记录
                if i == 0:
                    # 已过期维保 (5-10天前)
                    maintenance_date = today - timedelta(days=random.randint(15, 25))
                    next_maintenance_date = today - timedelta(days=random.randint(5, 10))
                    description = f"{eq_name}定期维护（已过期）"
                    notes = f"需要更换{part_name}，维保已过期"
                elif i == 1:
                    # 即将到期维保 (3-7天内)
                    maintenance_date = today - timedelta(days=random.randint(20, 30))
                    next_maintenance_date = today + timedelta(days=random.randint(3, 7))
                    description = f"{eq_name}月度检查（即将到期）"
                    notes = f"检查{part_name}状态，即将需要维保"
                else:
                    # 正常维保 (30-60天后)
                    maintenance_date = today - timedelta(days=random.randint(10, 20))
                    next_maintenance_date = today + timedelta(days=random.randint(30, 60))
                    description = f"{eq_name}常规保养（正常）"
                    notes = f"{part_name}状态良好，按计划维保"
                
                maintenance_records.append((
                    record_id,
                    eq_id,
                    part_id,
                    maintenance_date.isoformat(),
                    next_maintenance_date.isoformat(),
                    description,
                    notes,
                    datetime.now().isoformat()
                ))
                
                record_id += 1
        
        # 插入维保记录
        cursor.executemany('''
            INSERT INTO maintenance_records 
            (id, equipment_id, spare_part_id, maintenance_date, next_maintenance_date, 
             description, notes, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', maintenance_records)
        
        print(f"创建了 {len(maintenance_records)} 条维保记录")
        
        # 验证创建结果
        cursor.execute('SELECT COUNT(*) FROM maintenance_records')
        total_count = cursor.fetchone()[0]
        print(f"数据库中维保记录总数: {total_count}")
        
        # 按状态统计
        cursor.execute('''
            SELECT 
                CASE 
                    WHEN next_maintenance_date < ? THEN 'overdue'
                    WHEN next_maintenance_date <= date(?, '+30 days') THEN 'warning'
                    ELSE 'normal'
                END as status,
                COUNT(*) as count
            FROM maintenance_records
            WHERE next_maintenance_date IS NOT NULL
            GROUP BY status
        ''', (today, today))
        
        status_stats = cursor.fetchall()
        print("\n维保状态统计:")
        for status, count in status_stats:
            status_name = {
                'overdue': '已过期',
                'warning': '即将到期', 
                'normal': '正常'
            }.get(status, status)
            print(f"  {status_name}: {count} 条记录")
        
        # 按区域统计
        cursor.execute('''
            SELECT a.name as area_name, COUNT(mr.id) as maintenance_count
            FROM areas a
            LEFT JOIN equipment e ON a.id = e.area_id
            LEFT JOIN maintenance_records mr ON e.id = mr.equipment_id
            GROUP BY a.id, a.name
            ORDER BY a.name
        ''')
        
        area_stats = cursor.fetchall()
        print("\n区域维保统计:")
        for area_name, count in area_stats:
            print(f"  {area_name}: {count} 条维保记录")
        
        conn.commit()
        print("\n✅ 维保测试数据创建成功！")
        
    except Exception as e:
        print(f"❌ 创建维保测试数据时出错: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
        
    finally:
        conn.close()

if __name__ == "__main__":
    create_maintenance_test_data()
