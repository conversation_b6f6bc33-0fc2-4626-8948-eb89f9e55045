#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_maintenance_api():
    """测试维保状态API"""
    try:
        print("=== 测试维保状态API ===")
        response = requests.get('http://127.0.0.1:5000/api/maintenance/status')
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应成功: {data.get('success')}")
            
            if data.get('success'):
                records = data.get('data', [])
                print(f"记录数量: {len(records)}")
                
                if records:
                    print("\n=== 第一条记录的字段 ===")
                    first_record = records[0]
                    for key, value in first_record.items():
                        print(f"  {key}: {value}")
                    
                    print("\n=== 检查progress字段 ===")
                    progress_fields = [record.get('progress') for record in records[:5]]
                    print(f"前5条记录的progress值: {progress_fields}")
                    
                    print("\n=== 检查必要字段 ===")
                    required_fields = ['area_name', 'equipment_name', 'spare_part_name', 'progress']
                    for field in required_fields:
                        values = [record.get(field) for record in records[:3]]
                        print(f"{field}: {values}")
                else:
                    print("没有维保记录")
            else:
                print(f"API错误: {data.get('error')}")
        else:
            print(f"HTTP错误: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

def test_statistics_api():
    """测试统计API"""
    try:
        print("\n=== 测试统计API ===")
        response = requests.get('http://127.0.0.1:5000/api/statistics')
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应成功: {data.get('success')}")
            
            if data.get('success'):
                stats = data.get('data', {})
                print("统计数据:")
                print(json.dumps(stats, indent=2, ensure_ascii=False))
            else:
                print(f"API错误: {data.get('error')}")
        else:
            print(f"HTTP错误: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

if __name__ == "__main__":
    test_maintenance_api()
    test_statistics_api()
