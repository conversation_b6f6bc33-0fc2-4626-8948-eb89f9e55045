#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复设备名称重复问题
清理现有数据并重新创建唯一的设备名称
"""

import sqlite3
import os

def main():
    print("=== 修复设备名称重复问题 ===")
    
    # 删除现有数据库
    if os.path.exists('equipment_maintenance.db'):
        os.remove('equipment_maintenance.db')
        print("✅ 已删除现有数据库")
    
    # 连接数据库（会自动创建新的）
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 1. 创建表结构
        print("1. 创建数据库表结构...")
        
        # 区域表
        cursor.execute('''
            CREATE TABLE areas (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 设备表
        cursor.execute('''
            CREATE TABLE equipment (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                area_id INTEGER NOT NULL,
                name TEXT NOT NULL UNIQUE,
                type TEXT NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (area_id) REFERENCES areas (id)
            )
        ''')
        
        # 零备件表
        cursor.execute('''
            CREATE TABLE spare_parts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                maintenance_cycle_months INTEGER NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (equipment_id) REFERENCES equipment (id)
            )
        ''')
        
        # 库存表
        cursor.execute('''
            CREATE TABLE inventory (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                spare_part_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL DEFAULT 0,
                min_quantity INTEGER NOT NULL DEFAULT 0,
                location TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (spare_part_id) REFERENCES spare_parts (id)
            )
        ''')
        
        # 维保记录表
        cursor.execute('''
            CREATE TABLE maintenance_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                spare_part_id INTEGER NOT NULL,
                area_id INTEGER NOT NULL,
                equipment_id INTEGER NOT NULL,
                maintenance_date DATE NOT NULL,
                next_maintenance_date DATE NOT NULL,
                technician TEXT,
                notes TEXT,
                quantity_used INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (spare_part_id) REFERENCES spare_parts (id),
                FOREIGN KEY (area_id) REFERENCES areas (id),
                FOREIGN KEY (equipment_id) REFERENCES equipment (id)
            )
        ''')
        
        print("✅ 数据库表结构创建完成")
        
        # 2. 运行修复后的数据插入脚本
        print("2. 重新创建数据...")
        conn.close()
        
        # 执行修复后的数据插入脚本
        exec(open('simple_data_insert.py').read())
        
        print("✅ 设备名称修复完成！")
        
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    main()
