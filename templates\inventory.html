{% extends "base.html" %}

{% block title %}库存管理 - 设备零备件周期管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-boxes me-2"></i>库存管理</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary" onclick="refreshInventory()">
            <i class="fas fa-sync-alt me-1"></i>刷新数据
        </button>
    </div>
</div>

<!-- 库存统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="stat-card success">
            <i class="fas fa-check-circle fa-2x mb-3"></i>
            <h3 id="sufficient-stock">-</h3>
            <p>库存充足</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="stat-card warning">
            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
            <h3 id="low-stock">-</h3>
            <p>库存不足</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="stat-card danger">
            <i class="fas fa-times-circle fa-2x mb-3"></i>
            <h3 id="out-of-stock">-</h3>
            <p>缺货</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="stat-card info">
            <i class="fas fa-boxes fa-2x mb-3"></i>
            <h3 id="total-items">-</h3>
            <p>总库存项目</p>
        </div>
    </div>
</div>

<!-- 库存状态表格 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-warehouse me-2"></i>库存状态详情</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="inventoryTable">
                <thead>
                    <tr>
                        <th>零备件名称</th>
                        <th>零备件描述</th>
                        <th>当前库存</th>
                        <th>最低库存</th>
                        <th>存放位置</th>
                        <th>库存状态</th>
                        <th>最后更新</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="inventoryTableBody">
                    <tr>
                        <td colspan="8" class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 更新库存模态框 -->
<div class="modal fade" id="updateInventoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-edit me-2"></i>更新库存</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="updateInventoryForm">
                    <input type="hidden" id="inventory-id">
                    <div class="mb-3">
                        <label class="form-label">零备件信息</label>
                        <div class="form-control-plaintext" id="spare-part-info"></div>
                    </div>
                    <div class="mb-3">
                        <label for="current-quantity" class="form-label">当前库存</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="current-quantity" min="0" required>
                            <span class="input-group-text">件</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="min-quantity" class="form-label">最低库存</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="min-quantity" min="1" required>
                            <span class="input-group-text">件</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="location" class="form-label">存放位置</label>
                        <input type="text" class="form-control" id="location" placeholder="请输入存放位置">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" onclick="submitInventoryUpdate()">
                    <i class="fas fa-save me-1"></i>保存
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载完成后初始化
$(document).ready(function() {
    loadInventoryData();
});

// 加载库存数据
function loadInventoryData() {
    $.get('/api/inventory/status')
        .done(function(response) {
            if (response.success) {
                updateInventoryTable(response.data);
                updateInventoryStats(response.data);
            }
        })
        .fail(function() {
            $('#inventoryTableBody').html('<tr><td colspan="8" class="text-center text-danger">加载失败</td></tr>');
        });
}

// 更新库存统计
function updateInventoryStats(data) {
    let sufficient = 0;
    let lowStock = 0;
    let outOfStock = 0;
    
    data.forEach(function(item) {
        switch(item.status) {
            case 'sufficient':
                sufficient++;
                break;
            case 'low_stock':
                lowStock++;
                break;
            case 'out_of_stock':
                outOfStock++;
                break;
        }
    });
    
    $('#sufficient-stock').text(sufficient);
    $('#low-stock').text(lowStock);
    $('#out-of-stock').text(outOfStock);
    $('#total-items').text(data.length);
}

// 更新库存表格
function updateInventoryTable(data) {
    const tbody = $('#inventoryTableBody');
    tbody.empty();
    
    if (data.length === 0) {
        tbody.html('<tr><td colspan="8" class="text-center">暂无数据</td></tr>');
        return;
    }
    
    data.forEach(function(item) {
        const statusBadge = getInventoryStatusBadge(item.status, item.status_text);
        const quantityClass = item.status === 'out_of_stock' ? 'text-danger fw-bold' : 
                             item.status === 'low_stock' ? 'text-warning fw-bold' : '';
        
        const lastUpdated = item.last_updated ? new Date(item.last_updated).toLocaleDateString('zh-CN') : '-';

        const row = `
            <tr>
                <td>${item.spare_part_name || '-'}</td>
                <td>全局统一管理</td>
                <td class="${quantityClass}">${item.total_quantity || 0}</td>
                <td>${item.min_quantity || 1}</td>
                <td>${item.location || '-'}</td>
                <td>${statusBadge}</td>
                <td>${lastUpdated}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editInventory('${item.spare_part_name}', '${item.spare_part_name}', ${item.total_quantity || 0}, ${item.min_quantity || 1}, '${item.location || ''}')">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// 获取库存状态徽章
function getInventoryStatusBadge(status, text) {
    const badgeClass = {
        'out_of_stock': 'bg-danger',
        'low_stock': 'bg-warning',
        'sufficient': 'bg-success'
    }[status] || 'bg-secondary';
    
    return `<span class="badge ${badgeClass}">${text}</span>`;
}

// 编辑库存 - 适应全局库存管理
function editInventory(sparePartName, sparePartInfo, quantity, minQuantity, location) {
    $('#inventory-id').val(sparePartName); // 现在存储零件名称而不是ID
    $('#spare-part-info').text(sparePartInfo + ' (全局统一管理)');
    $('#current-quantity').val(quantity);
    $('#min-quantity').val(minQuantity);
    $('#location').val(location);
    $('#updateInventoryModal').modal('show');
}

// 提交库存更新 - 使用全局库存API
function submitInventoryUpdate() {
    const formData = {
        spare_part_name: $('#inventory-id').val(), // 使用零件名称
        quantity: parseInt($('#current-quantity').val()),
        location: $('#location').val()
    };

    if (isNaN(formData.quantity)) {
        alert('请输入有效的数量');
        return;
    }

    $.ajax({
        url: '/api/inventory/update',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                alert('全局库存更新成功');
                $('#updateInventoryModal').modal('hide');
                loadInventoryData();
            } else {
                alert('更新失败: ' + response.error);
            }
        },
        error: function() {
            alert('更新失败，请稍后重试');
        }
    });
}

// 刷新库存数据
function refreshInventory() {
    loadInventoryData();
}
</script>
{% endblock %}
