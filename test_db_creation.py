#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库创建脚本
"""

import sqlite3
from database import DatabaseManager

def test_database_creation():
    """测试数据库创建"""
    print("开始测试数据库创建...")
    
    # 创建数据库管理器
    db = DatabaseManager()
    
    # 检查表结构
    conn = db.get_connection()
    cursor = conn.cursor()
    
    # 检查所有表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    print(f"创建的表: {[table[0] for table in tables]}")
    
    # 检查maintenance_records表结构
    cursor.execute("PRAGMA table_info(maintenance_records)")
    columns = cursor.fetchall()
    print("\nmaintenance_records表结构:")
    for col in columns:
        print(f"  {col[1]} - {col[2]} - {'NOT NULL' if col[3] else 'NULL'}")
    
    conn.close()
    print("数据库创建测试完成！")

if __name__ == "__main__":
    test_database_creation()
