#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查维保联动的数据库状态
"""

import sqlite3
from datetime import datetime

def check_maintenance_linkage():
    """检查维保联动的数据库状态"""
    print("🔍 检查维保联动的数据库状态")
    print("=" * 50)
    
    conn = sqlite3.connect('equipment_maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # 1. 检查维保记录状态
        print("\n1️⃣ 检查维保记录状态...")
        cursor.execute("""
            SELECT 
                mr.id,
                sp.name as spare_part_name,
                a.name as area_name,
                e.name as equipment_name,
                mr.maintenance_date,
                mr.next_maintenance_date,
                mr.technician,
                mr.maintenance_status,
                mr.notes
            FROM maintenance_records mr
            JOIN spare_parts sp ON mr.spare_part_id = sp.id
            JOIN areas a ON mr.area_id = a.id
            JOIN equipment e ON mr.equipment_id = e.id
            WHERE sp.name = '主接触器' AND e.name = '堆垛机1号'
            ORDER BY mr.maintenance_date DESC
        """)
        
        records = cursor.fetchall()
        print(f"   找到 {len(records)} 条相关维保记录:")
        for record in records:
            print(f"   ID:{record['id']} | {record['maintenance_date']} | {record['technician']} | {record['maintenance_status']} | {record['notes'][:50] if record['notes'] else ''}")
        
        # 2. 检查维保计划状态
        print("\n2️⃣ 检查维保计划状态...")
        cursor.execute("""
            SELECT 
                mp.id,
                sp.name as spare_part_name,
                a.name as area_name,
                e.name as equipment_name,
                mp.planned_date,
                mp.plan_status,
                mp.from_alert,
                mp.notes
            FROM maintenance_plans mp
            JOIN spare_parts sp ON mp.spare_part_id = sp.id
            JOIN areas a ON mp.area_id = a.id
            JOIN equipment e ON mp.equipment_id = e.id
            WHERE sp.name = '主接触器' AND e.name = '堆垛机1号'
            ORDER BY mp.created_at DESC
        """)
        
        plans = cursor.fetchall()
        print(f"   找到 {len(plans)} 条相关维保计划:")
        for plan in plans:
            print(f"   ID:{plan['id']} | {plan['planned_date']} | {plan['plan_status']} | from_alert:{plan['from_alert']} | {plan['notes'][:50] if plan['notes'] else ''}")
        
        # 3. 检查维保提醒状态
        print("\n3️⃣ 检查维保提醒状态...")
        today = datetime.now().date()
        cursor.execute("""
            SELECT 
                sp.name as spare_part_name,
                a.name as area_name,
                e.name as equipment_name,
                mr.next_maintenance_date,
                mr.maintenance_status,
                CASE 
                    WHEN mr.next_maintenance_date < date('now') THEN '逾期'
                    WHEN mr.next_maintenance_date <= date('now', '+30 days') THEN '即将到期'
                    ELSE '正常'
                END as status
            FROM maintenance_records mr
            JOIN spare_parts sp ON mr.spare_part_id = sp.id
            JOIN areas a ON mr.area_id = a.id
            JOIN equipment e ON mr.equipment_id = e.id
            WHERE sp.name = '主接触器' AND e.name = '堆垛机1号'
            AND (mr.maintenance_status IS NULL OR mr.maintenance_status != 'replaced_by_plan')
            ORDER BY mr.next_maintenance_date
        """)
        
        alerts = cursor.fetchall()
        print(f"   找到 {len(alerts)} 条有效的维保提醒:")
        for alert in alerts:
            print(f"   {alert['spare_part_name']} | {alert['equipment_name']} | {alert['next_maintenance_date']} | {alert['status']} | {alert['maintenance_status']}")
        
        # 4. 检查所有状态的维保记录
        print("\n4️⃣ 检查所有状态的维保记录...")
        cursor.execute("""
            SELECT 
                maintenance_status,
                COUNT(*) as count
            FROM maintenance_records
            GROUP BY maintenance_status
        """)
        
        status_counts = cursor.fetchall()
        print("   维保记录状态统计:")
        for status in status_counts:
            print(f"   {status['maintenance_status'] or 'NULL'}: {status['count']} 条")
            
    finally:
        conn.close()

if __name__ == "__main__":
    check_maintenance_linkage()
