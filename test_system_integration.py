#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统集成测试脚本
测试所有核心功能是否正常工作
"""

import requests
import json
from datetime import datetime
import time

def test_system_integration():
    """系统集成测试"""
    print("🧪 开始系统集成测试...")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    # 启动Flask应用进行测试
    print("📡 启动Flask应用...")
    import subprocess
    import threading
    
    # 在后台启动Flask应用
    def start_flask():
        subprocess.run(["python", "app.py"], cwd=".")
    
    flask_thread = threading.Thread(target=start_flask, daemon=True)
    flask_thread.start()
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(5)
    
    try:
        # 测试各个API端点
        test_results = []
        
        # 1. 测试系统概览API
        print("\n1️⃣ 测试系统概览API...")
        result = test_overview_api(base_url)
        test_results.append(("系统概览API", result))
        
        # 2. 测试维保状态API
        print("\n2️⃣ 测试维保状态API...")
        result = test_maintenance_status_api(base_url)
        test_results.append(("维保状态API", result))
        
        # 3. 测试维保提醒API
        print("\n3️⃣ 测试维保提醒API...")
        result = test_maintenance_alerts_api(base_url)
        test_results.append(("维保提醒API", result))
        
        # 4. 测试库存状态API
        print("\n4️⃣ 测试库存状态API...")
        result = test_inventory_status_api(base_url)
        test_results.append(("库存状态API", result))
        
        # 5. 测试维保计划API
        print("\n5️⃣ 测试维保计划API...")
        result = test_maintenance_plans_api(base_url)
        test_results.append(("维保计划API", result))
        
        # 6. 测试基础数据API
        print("\n6️⃣ 测试基础数据API...")
        result = test_basic_data_apis(base_url)
        test_results.append(("基础数据API", result))
        
        # 输出测试结果
        print("\n" + "=" * 50)
        print("📊 测试结果汇总:")
        
        passed = 0
        failed = 0
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
            else:
                failed += 1
        
        print(f"\n总计: {passed + failed} 个测试, 通过 {passed} 个, 失败 {failed} 个")
        
        if failed == 0:
            print("🎉 所有测试通过！系统功能正常")
            return True
        else:
            print(f"⚠️  有 {failed} 个测试失败，需要检查")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

def test_overview_api(base_url):
    """测试系统概览API"""
    try:
        response = requests.get(f"{base_url}/api/overview", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and 'data' in data:
                overview = data['data']
                print(f"   零备件总数: {overview.get('total_parts', 0)}")
                print(f"   逾期维保: {overview.get('overdue_maintenance', 0)}")
                print(f"   即将到期: {overview.get('upcoming_maintenance', 0)}")
                print(f"   库存不足: {overview.get('low_stock', 0)}")
                return True
        print(f"   API响应异常: {response.status_code}")
        return False
    except Exception as e:
        print(f"   请求失败: {e}")
        return False

def test_maintenance_status_api(base_url):
    """测试维保状态API"""
    try:
        response = requests.get(f"{base_url}/api/maintenance/status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and 'data' in data:
                records = data['data']
                print(f"   维保记录数量: {len(records)}")
                if records:
                    # 统计各种状态
                    status_count = {}
                    for record in records:
                        status = record.get('status', 'unknown')
                        status_count[status] = status_count.get(status, 0) + 1
                    
                    for status, count in status_count.items():
                        print(f"   {status}: {count} 条")
                return True
        print(f"   API响应异常: {response.status_code}")
        return False
    except Exception as e:
        print(f"   请求失败: {e}")
        return False

def test_maintenance_alerts_api(base_url):
    """测试维保提醒API"""
    try:
        response = requests.get(f"{base_url}/api/maintenance/alerts?days=30", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and 'data' in data:
                alerts = data['data']
                print(f"   维保提醒数量: {len(alerts)}")
                return True
        print(f"   API响应异常: {response.status_code}")
        return False
    except Exception as e:
        print(f"   请求失败: {e}")
        return False

def test_inventory_status_api(base_url):
    """测试库存状态API"""
    try:
        response = requests.get(f"{base_url}/api/inventory/status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and 'data' in data:
                inventory = data['data']
                print(f"   库存记录数量: {len(inventory)}")
                if inventory:
                    # 统计各种库存状态
                    status_count = {}
                    for item in inventory:
                        status = item.get('status', 'unknown')
                        status_count[status] = status_count.get(status, 0) + 1
                    
                    for status, count in status_count.items():
                        print(f"   {status}: {count} 个")
                return True
        print(f"   API响应异常: {response.status_code}")
        return False
    except Exception as e:
        print(f"   请求失败: {e}")
        return False

def test_maintenance_plans_api(base_url):
    """测试维保计划API"""
    try:
        response = requests.get(f"{base_url}/api/maintenance/plans", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and 'data' in data:
                plans = data['data']
                print(f"   维保计划数量: {len(plans)}")
                return True
        print(f"   API响应异常: {response.status_code}")
        return False
    except Exception as e:
        print(f"   请求失败: {e}")
        return False

def test_basic_data_apis(base_url):
    """测试基础数据API"""
    try:
        # 测试区域API
        response = requests.get(f"{base_url}/api/areas", timeout=10)
        if response.status_code != 200:
            print(f"   区域API失败: {response.status_code}")
            return False
        
        areas_data = response.json()
        if not (areas_data.get('success') and 'data' in areas_data):
            print("   区域API数据格式错误")
            return False
        
        areas = areas_data['data']
        print(f"   区域数量: {len(areas)}")
        
        # 测试零备件API
        response = requests.get(f"{base_url}/api/spare-parts", timeout=10)
        if response.status_code != 200:
            print(f"   零备件API失败: {response.status_code}")
            return False
        
        parts_data = response.json()
        if not (parts_data.get('success') and 'data' in parts_data):
            print("   零备件API数据格式错误")
            return False
        
        parts = parts_data['data']
        print(f"   零备件数量: {len(parts)}")
        
        return True
        
    except Exception as e:
        print(f"   请求失败: {e}")
        return False

def test_direct_database():
    """直接测试数据库功能"""
    print("🗄️ 直接测试数据库功能...")
    
    try:
        from maintenance_manager import MaintenanceManager
        
        manager = MaintenanceManager()
        
        # 测试系统概览
        overview = manager.get_system_overview()
        print(f"   系统概览: {overview}")
        
        # 测试维保状态
        status_list = manager.get_maintenance_status()
        print(f"   维保状态记录数: {len(status_list)}")
        
        # 测试库存状态
        inventory = manager.get_inventory_status()
        print(f"   库存记录数: {len(inventory)}")
        
        # 测试维保计划
        plans = manager.get_maintenance_plans()
        print(f"   维保计划数: {len(plans)}")
        
        print("✅ 数据库功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据库功能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 设备零备件周期管理系统 - 集成测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 首先测试数据库功能
    db_result = test_direct_database()
    
    if db_result:
        print("\n" + "=" * 50)
        print("✅ 数据库功能测试通过，系统核心功能正常")
        print("💡 建议手动启动Web服务器进行完整测试:")
        print("   python app.py")
        print("   然后访问: http://127.0.0.1:5000")
        return True
    else:
        print("\n❌ 数据库功能测试失败")
        return False

if __name__ == "__main__":
    main()
