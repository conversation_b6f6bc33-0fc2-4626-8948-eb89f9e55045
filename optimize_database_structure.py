#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库结构优化脚本 - 根据新需求优化数据库设计
"""

import sqlite3
from datetime import datetime, timedelta
import os

def backup_database():
    """备份当前数据库"""
    if os.path.exists('equipment_maintenance.db'):
        backup_name = f'equipment_maintenance_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        os.system(f'copy equipment_maintenance.db {backup_name}')
        print(f"✅ 数据库已备份为: {backup_name}")
        return True
    return False

def optimize_inventory_structure():
    """优化库存结构 - 实现全局统一管理"""
    print("🔧 开始优化库存结构...")
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 1. 创建新的库存表结构（全局统一管理）
        print("1. 创建新的库存表结构...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_global (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                spare_part_name TEXT NOT NULL UNIQUE,
                total_quantity INTEGER DEFAULT 0,
                min_quantity INTEGER DEFAULT 1,
                location TEXT DEFAULT '中央仓库',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 2. 从现有库存数据中聚合全局库存
        print("2. 聚合现有库存数据...")
        cursor.execute('''
            INSERT OR REPLACE INTO inventory_global (spare_part_name, total_quantity, min_quantity, location)
            SELECT 
                sp.name,
                SUM(i.quantity) as total_quantity,
                MAX(i.min_quantity) as min_quantity,
                '中央仓库' as location
            FROM inventory i
            JOIN spare_parts sp ON i.spare_part_id = sp.id
            GROUP BY sp.name
        ''')
        
        # 3. 检查聚合结果
        cursor.execute("SELECT COUNT(*) FROM inventory_global")
        global_count = cursor.fetchone()[0]
        print(f"   聚合后全局库存记录数: {global_count}")
        
        # 4. 显示前几条记录
        cursor.execute("SELECT * FROM inventory_global LIMIT 5")
        records = cursor.fetchall()
        print("   前5条全局库存记录:")
        for record in records:
            print(f"     {record[1]}: 总量{record[2]}, 最低{record[3]}, 位置{record[4]}")
        
        conn.commit()
        print("✅ 库存结构优化完成")
        
    except Exception as e:
        print(f"❌ 库存结构优化失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()
    
    return True

def clean_duplicate_spare_parts():
    """清理重复的零备件数据"""
    print("🧹 清理重复的零备件数据...")
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 1. 查找重复的零备件
        cursor.execute('''
            SELECT name, equipment_id, COUNT(*) as count
            FROM spare_parts
            GROUP BY name, equipment_id
            HAVING COUNT(*) > 1
        ''')
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"   发现 {len(duplicates)} 组重复的零备件")
            
            # 2. 删除重复记录，保留ID最小的
            for name, equipment_id, count in duplicates:
                cursor.execute('''
                    DELETE FROM spare_parts
                    WHERE name = ? AND equipment_id = ?
                    AND id NOT IN (
                        SELECT MIN(id)
                        FROM spare_parts
                        WHERE name = ? AND equipment_id = ?
                    )
                ''', (name, equipment_id, name, equipment_id))
                
                deleted = cursor.rowcount
                print(f"     删除重复的 {name} (设备{equipment_id}): {deleted} 条记录")
        else:
            print("   没有发现重复的零备件")
        
        conn.commit()
        print("✅ 重复数据清理完成")
        
    except Exception as e:
        print(f"❌ 清理重复数据失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()
    
    return True

def optimize_maintenance_logic():
    """优化维保逻辑 - 支持首次手动建立维保单"""
    print("⚙️ 优化维保逻辑...")
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 1. 检查维保记录表结构
        cursor.execute("PRAGMA table_info(maintenance_records)")
        columns = [row[1] for row in cursor.fetchall()]
        
        # 2. 添加首次维保标记字段（如果不存在）
        if 'is_first_maintenance' not in columns:
            print("   添加首次维保标记字段...")
            cursor.execute('''
                ALTER TABLE maintenance_records
                ADD COLUMN is_first_maintenance BOOLEAN DEFAULT 0
            ''')
        
        # 3. 添加维保状态字段（如果不存在）
        if 'maintenance_status' not in columns:
            print("   添加维保状态字段...")
            cursor.execute('''
                ALTER TABLE maintenance_records
                ADD COLUMN maintenance_status TEXT DEFAULT 'completed'
            ''')
        
        # 4. 更新现有记录的状态
        cursor.execute('''
            UPDATE maintenance_records
            SET maintenance_status = 'completed'
            WHERE maintenance_status IS NULL
        ''')
        
        conn.commit()
        print("✅ 维保逻辑优化完成")
        
    except Exception as e:
        print(f"❌ 维保逻辑优化失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()
    
    return True

def create_maintenance_plan_table():
    """创建维保计划表 - 用于首次维保计划管理"""
    print("📋 创建维保计划表...")
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 创建维保计划表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS maintenance_plans (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                spare_part_id INTEGER NOT NULL,
                area_id INTEGER NOT NULL,
                equipment_id INTEGER NOT NULL,
                planned_date DATE NOT NULL,
                plan_status TEXT DEFAULT 'pending',
                created_by TEXT DEFAULT 'system',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (spare_part_id) REFERENCES spare_parts (id),
                FOREIGN KEY (area_id) REFERENCES areas (id),
                FOREIGN KEY (equipment_id) REFERENCES equipment (id)
            )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_maintenance_plans_spare_part ON maintenance_plans(spare_part_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_maintenance_plans_planned_date ON maintenance_plans(planned_date)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_maintenance_plans_status ON maintenance_plans(plan_status)')
        
        conn.commit()
        print("✅ 维保计划表创建完成")
        
    except Exception as e:
        print(f"❌ 维保计划表创建失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()
    
    return True

def verify_optimization():
    """验证优化结果"""
    print("🔍 验证优化结果...")
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 检查表数量
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"   数据库表数量: {len(tables)}")
        print(f"   表列表: {', '.join(tables)}")
        
        # 检查各表数据量
        for table in ['areas', 'equipment', 'spare_parts', 'inventory_global', 'maintenance_records', 'maintenance_plans']:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   {table}: {count} 条记录")
        
        print("✅ 优化结果验证完成")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False
    finally:
        conn.close()
    
    return True

def main():
    """主函数"""
    print("🚀 开始数据库结构优化...")
    print("=" * 50)
    
    # 1. 备份数据库
    if not backup_database():
        print("❌ 数据库备份失败")
        return False
    
    # 2. 优化库存结构
    if not optimize_inventory_structure():
        print("❌ 库存结构优化失败")
        return False
    
    # 3. 清理重复数据
    if not clean_duplicate_spare_parts():
        print("❌ 重复数据清理失败")
        return False
    
    # 4. 优化维保逻辑
    if not optimize_maintenance_logic():
        print("❌ 维保逻辑优化失败")
        return False
    
    # 5. 创建维保计划表
    if not create_maintenance_plan_table():
        print("❌ 维保计划表创建失败")
        return False
    
    # 6. 验证优化结果
    if not verify_optimization():
        print("❌ 优化结果验证失败")
        return False
    
    print("=" * 50)
    print("🎉 数据库结构优化完成！")
    print("主要改进:")
    print("  • 库存管理改为全局统一管理")
    print("  • 清理了重复的零备件数据")
    print("  • 优化了维保逻辑，支持首次维保计划")
    print("  • 创建了维保计划表")
    
    return True

if __name__ == "__main__":
    main()
