<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .data { background: #f5f5f5; padding: 10px; white-space: pre-wrap; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>API响应测试</h1>
    
    <div class="section">
        <h2>维保状态API测试</h2>
        <button onclick="testMaintenanceAPI()">测试维保API</button>
        <div id="maintenanceResult"></div>
    </div>
    
    <div class="section">
        <h2>统计API测试</h2>
        <button onclick="testStatisticsAPI()">测试统计API</button>
        <div id="statisticsResult"></div>
    </div>
    
    <script>
        function testMaintenanceAPI() {
            const resultDiv = document.getElementById('maintenanceResult');
            resultDiv.innerHTML = '<p>正在请求...</p>';
            
            fetch('/api/maintenance/status')
                .then(response => {
                    console.log('维保API状态码:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('维保API响应:', data);
                    
                    let html = '<div class="success">请求成功</div>';
                    html += '<div class="data">';
                    html += `成功: ${data.success}\n`;
                    html += `数据数量: ${data.data ? data.data.length : 0}\n\n`;
                    
                    if (data.data && data.data.length > 0) {
                        html += '第一条记录:\n';
                        html += JSON.stringify(data.data[0], null, 2);
                        
                        html += '\n\n字段检查:\n';
                        const firstRecord = data.data[0];
                        html += `area_name: ${firstRecord.area_name}\n`;
                        html += `equipment_name: ${firstRecord.equipment_name}\n`;
                        html += `spare_part_name: ${firstRecord.spare_part_name}\n`;
                        html += `progress: ${firstRecord.progress}\n`;
                        html += `status: ${firstRecord.status}\n`;
                    }
                    
                    html += '</div>';
                    resultDiv.innerHTML = html;
                })
                .catch(error => {
                    console.error('维保API错误:', error);
                    resultDiv.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
                });
        }
        
        function testStatisticsAPI() {
            const resultDiv = document.getElementById('statisticsResult');
            resultDiv.innerHTML = '<p>正在请求...</p>';
            
            fetch('/api/statistics')
                .then(response => {
                    console.log('统计API状态码:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('统计API响应:', data);
                    
                    let html = '<div class="success">请求成功</div>';
                    html += '<div class="data">';
                    html += JSON.stringify(data, null, 2);
                    html += '</div>';
                    resultDiv.innerHTML = html;
                })
                .catch(error => {
                    console.error('统计API错误:', error);
                    resultDiv.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
                });
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            testMaintenanceAPI();
            testStatisticsAPI();
        };
    </script>
</body>
</html>
