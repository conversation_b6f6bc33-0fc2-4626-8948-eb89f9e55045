#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_inventory_api():
    """测试库存API"""
    try:
        print("=== 测试库存状态API ===")
        response = requests.get('http://127.0.0.1:5000/api/inventory/status')
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"API成功: {data.get('success')}")
            
            if data.get('success'):
                inventory_items = data.get('data', [])
                print(f"库存项目总数: {len(inventory_items)}")
                
                if inventory_items:
                    print("\n=== 库存详情 ===")
                    print(f"{'序号':<4} {'零备件名称':<20} {'描述':<25} {'库存':<6} {'最小':<6} {'状态':<8} {'位置':<12}")
                    print("-" * 90)
                    
                    for i, item in enumerate(inventory_items):
                        name = (item.get('spare_part_name', '未知') or '未知')[:19]
                        desc = (item.get('spare_part_description', '无') or '无')[:24]
                        quantity = item.get('quantity', 0)
                        min_qty = item.get('min_quantity', 0)
                        status = item.get('status_text', '未知')
                        location = (item.get('location', '未知') or '未知')[:11]
                        
                        print(f"{i+1:<4} {name:<20} {desc:<25} {quantity:<6} {min_qty:<6} {status:<8} {location:<12}")
                    
                    # 统计库存状态
                    print("\n=== 库存状态统计 ===")
                    status_count = {}
                    total_items = 0
                    
                    for item in inventory_items:
                        status = item.get('status_text', '未知')
                        status_count[status] = status_count.get(status, 0) + 1
                        total_items += item.get('quantity', 0)
                    
                    for status, count in status_count.items():
                        print(f"  {status}: {count} 种零备件")
                    
                    print(f"\n总库存数量: {total_items} 件")
                    
                else:
                    print("没有库存数据")
            else:
                print(f"API错误: {data.get('error')}")
        else:
            print(f"HTTP错误: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

if __name__ == "__main__":
    test_inventory_api()
