#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理多余设备脚本
只保留指定数量的设备，删除多余的设备及其相关数据
"""

import sqlite3

def clean_excess_equipment():
    """清理多余的设备"""
    print("🧹 开始清理多余的设备...")
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 获取区域ID
        cursor.execute("SELECT id, name FROM areas")
        areas = {name: id for id, name in cursor.fetchall()}
        
        equipment_to_keep = []
        equipment_to_delete = []
        
        # 成品区：只保留堆垛机1、2、3、4号 + 1个入库皮带
        chengpin_area_id = areas.get('成品区')
        if chengpin_area_id:
            # 保留堆垛机1-4号
            cursor.execute("""
                SELECT id FROM equipment 
                WHERE area_id = ? AND type = '堆垛机' AND name IN ('堆垛机1号', '堆垛机2号', '堆垛机3号', '堆垛机4号')
                ORDER BY name
            """, (chengpin_area_id,))
            stackers = cursor.fetchall()
            equipment_to_keep.extend([eq[0] for eq in stackers])
            
            # 保留1个入库皮带
            cursor.execute("""
                SELECT id FROM equipment 
                WHERE area_id = ? AND type = '入库皮带'
                ORDER BY id LIMIT 1
            """, (chengpin_area_id,))
            belt = cursor.fetchone()
            if belt:
                equipment_to_keep.append(belt[0])
            
            # 标记要删除的设备
            cursor.execute("""
                SELECT id, name FROM equipment 
                WHERE area_id = ? AND id NOT IN ({})
            """.format(','.join('?' * len(equipment_to_keep))), 
            [chengpin_area_id] + equipment_to_keep)
            excess = cursor.fetchall()
            equipment_to_delete.extend(excess)
        
        # 辅料区：只保留堆垛机1、2、3号 + 1个AGV小车
        fuliao_area_id = areas.get('辅料区')
        if fuliao_area_id:
            # 保留堆垛机1-3号
            cursor.execute("""
                SELECT id FROM equipment 
                WHERE area_id = ? AND type = '堆垛机' AND name IN ('堆垛机1号', '堆垛机2号', '堆垛机3号')
                ORDER BY name
            """, (fuliao_area_id,))
            stackers = cursor.fetchall()
            equipment_to_keep.extend([eq[0] for eq in stackers])
            
            # 保留1个AGV小车
            cursor.execute("""
                SELECT id FROM equipment 
                WHERE area_id = ? AND type = 'AGV小车'
                ORDER BY id LIMIT 1
            """, (fuliao_area_id,))
            agv = cursor.fetchone()
            if agv:
                equipment_to_keep.append(agv[0])
            
            # 标记要删除的设备
            cursor.execute("""
                SELECT id, name FROM equipment 
                WHERE area_id = ? AND id NOT IN ({})
            """.format(','.join('?' * len([eq for eq in equipment_to_keep if eq not in [eq[0] for eq in cursor.execute("SELECT id FROM equipment WHERE area_id = ?", (chengpin_area_id,)).fetchall()]]))), 
            [fuliao_area_id] + [eq for eq in equipment_to_keep if eq not in [eq[0] for eq in cursor.execute("SELECT id FROM equipment WHERE area_id = ?", (chengpin_area_id,)).fetchall()]])
            
            # 重新查询辅料区要删除的设备
            cursor.execute("SELECT id, name FROM equipment WHERE area_id = ?", (fuliao_area_id,))
            all_fuliao = cursor.fetchall()
            fuliao_keep_ids = [eq[0] for eq in stackers] + ([agv[0]] if agv else [])
            for eq_id, eq_name in all_fuliao:
                if eq_id not in fuliao_keep_ids:
                    equipment_to_delete.append((eq_id, eq_name))
        
        # 片烟区：只保留堆垛机1、2、3号
        pianyan_area_id = areas.get('片烟区')
        if pianyan_area_id:
            # 保留堆垛机1-3号
            cursor.execute("""
                SELECT id FROM equipment 
                WHERE area_id = ? AND type = '堆垛机' AND name IN ('堆垛机1号', '堆垛机2号', '堆垛机3号')
                ORDER BY name
            """, (pianyan_area_id,))
            stackers = cursor.fetchall()
            equipment_to_keep.extend([eq[0] for eq in stackers])
            
            # 标记要删除的设备
            cursor.execute("SELECT id, name FROM equipment WHERE area_id = ?", (pianyan_area_id,))
            all_pianyan = cursor.fetchall()
            pianyan_keep_ids = [eq[0] for eq in stackers]
            for eq_id, eq_name in all_pianyan:
                if eq_id not in pianyan_keep_ids:
                    equipment_to_delete.append((eq_id, eq_name))
        
        print(f"要保留的设备数量: {len(equipment_to_keep)}")
        print(f"要删除的设备数量: {len(equipment_to_delete)}")
        
        if equipment_to_delete:
            print("\n要删除的设备:")
            for eq_id, eq_name in equipment_to_delete:
                print(f"  ID:{eq_id} - {eq_name}")
            
            # 删除相关的零备件数据
            equipment_ids_to_delete = [eq[0] for eq in equipment_to_delete]
            
            # 获取要删除的零备件ID
            cursor.execute(f"""
                SELECT id FROM spare_parts 
                WHERE equipment_id IN ({','.join('?' * len(equipment_ids_to_delete))})
            """, equipment_ids_to_delete)
            spare_part_ids_to_delete = [row[0] for row in cursor.fetchall()]
            
            if spare_part_ids_to_delete:
                print(f"同时删除 {len(spare_part_ids_to_delete)} 个相关零备件")
                
                # 删除维保记录
                cursor.execute(f"""
                    DELETE FROM maintenance_records 
                    WHERE spare_part_id IN ({','.join('?' * len(spare_part_ids_to_delete))})
                """, spare_part_ids_to_delete)
                
                # 删除维保计划
                cursor.execute(f"""
                    DELETE FROM maintenance_plans 
                    WHERE spare_part_id IN ({','.join('?' * len(spare_part_ids_to_delete))})
                """, spare_part_ids_to_delete)
                
                # 删除零备件
                cursor.execute(f"""
                    DELETE FROM spare_parts 
                    WHERE id IN ({','.join('?' * len(spare_part_ids_to_delete))})
                """, spare_part_ids_to_delete)
            
            # 删除设备
            cursor.execute(f"""
                DELETE FROM equipment 
                WHERE id IN ({','.join('?' * len(equipment_ids_to_delete))})
            """, equipment_ids_to_delete)
            
            conn.commit()
            print(f"✅ 成功删除 {len(equipment_to_delete)} 个多余设备")
        else:
            print("✅ 没有多余的设备需要删除")
        
        # 验证最终结果
        print("\n🔍 最终设备列表:")
        cursor.execute("""
            SELECT a.name as area_name, e.name as equipment_name, e.type
            FROM equipment e
            JOIN areas a ON e.area_id = a.id
            ORDER BY a.name, e.type, e.name
        """)
        
        results = cursor.fetchall()
        current_area = None
        
        for area_name, equipment_name, equipment_type in results:
            if current_area != area_name:
                print(f"\n📍 {area_name}:")
                current_area = area_name
            print(f"  - {equipment_name} ({equipment_type})")
        
    except Exception as e:
        print(f"❌ 清理设备失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()
    
    return True

def main():
    """主函数"""
    print("🧹 设备清理工具")
    print("=" * 40)
    print("目标配置:")
    print("  成品区：堆垛机1、2、3、4号 + 入库皮带")
    print("  辅料区：堆垛机1、2、3号 + AGV小车")
    print("  片烟区：堆垛机1、2、3号")
    print("=" * 40)
    
    success = clean_excess_equipment()
    
    if success:
        print("\n🎉 设备清理完成！")
        print("数据库现在只包含指定的设备配置")
    else:
        print("\n❌ 设备清理失败")
    
    return success

if __name__ == "__main__":
    main()
