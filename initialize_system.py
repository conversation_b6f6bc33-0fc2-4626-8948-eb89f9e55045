#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备零备件周期管理系统 - 系统初始化脚本
"""

from datetime import datetime, timedelta
from database import DatabaseManager
from maintenance_manager import MaintenanceManager

def initialize_complete_system():
    """完整初始化系统"""
    print("开始初始化设备零备件周期管理系统...")
    
    # 初始化数据库
    db = DatabaseManager()
    db.insert_initial_data()
    
    # 初始化零备件数据
    initialize_spare_parts(db)
    
    # 初始化库存数据
    initialize_inventory(db)
    
    # 初始化维保记录
    initialize_maintenance_records(db)
    
    print("系统初始化完成！")

def initialize_spare_parts(db: DatabaseManager):
    """初始化零备件数据"""
    conn = db.get_connection()
    cursor = conn.cursor()
    
    try:
        # 获取设备信息
        cursor.execute("""
            SELECT e.id, e.name, e.type, a.name as area_name 
            FROM equipment e 
            JOIN areas a ON e.area_id = a.id
        """)
        equipment_list = cursor.fetchall()
        
        spare_parts_data = []
        
        for eq in equipment_list:
            eq_id, eq_name, eq_type, area_name = eq
            
            if eq_type == "堆垛机":
                # 堆垛机零备件
                spare_parts_data.extend([
                    (eq_id, "主接触器", 6, f"{area_name}-{eq_name}主接触器"),
                    (eq_id, "变频器", 12, f"{area_name}-{eq_name}变频器"),
                    (eq_id, "传感器", 3, f"{area_name}-{eq_name}传感器")
                ])
            elif eq_type == "入库皮带":
                # 入库皮带零备件
                spare_parts_data.extend([
                    (eq_id, "皮带", 3, f"{area_name}-{eq_name}皮带"),
                    (eq_id, "滚筒", 6, f"{area_name}-{eq_name}滚筒")
                ])
            elif eq_type == "AGV小车":
                # AGV小车零备件
                spare_parts_data.extend([
                    (eq_id, "保险丝", 6, f"{area_name}-{eq_name}保险丝"),
                    (eq_id, "电池", 12, f"{area_name}-{eq_name}电池"),
                    (eq_id, "轮子", 9, f"{area_name}-{eq_name}轮子")
                ])
        
        # 插入零备件数据
        for sp_data in spare_parts_data:
            cursor.execute("""
                INSERT INTO spare_parts (equipment_id, name, maintenance_cycle_months, description)
                VALUES (?, ?, ?, ?)
            """, sp_data)
        
        conn.commit()
        print(f"已插入 {len(spare_parts_data)} 个零备件")
        
    except Exception as e:
        print(f"初始化零备件数据错误: {e}")
        conn.rollback()
    finally:
        conn.close()

def initialize_inventory(db: DatabaseManager):
    """初始化库存数据"""
    conn = db.get_connection()
    cursor = conn.cursor()
    
    try:
        # 获取所有零备件
        cursor.execute("SELECT id, name FROM spare_parts")
        spare_parts = cursor.fetchall()
        
        import random
        
        inventory_data = []
        locations = ["A区货架", "B区货架", "C区货架", "仓库1", "仓库2"]
        
        for sp_id, sp_name in spare_parts:
            # 随机生成库存数据
            quantity = random.randint(0, 20)
            min_quantity = random.randint(1, 5)
            location = random.choice(locations)
            
            inventory_data.append((sp_id, quantity, min_quantity, location))
        
        # 插入库存数据
        for inv_data in inventory_data:
            cursor.execute("""
                INSERT INTO inventory (spare_part_id, quantity, min_quantity, location)
                VALUES (?, ?, ?, ?)
            """, inv_data)
        
        conn.commit()
        print(f"已插入 {len(inventory_data)} 个库存记录")
        
    except Exception as e:
        print(f"初始化库存数据错误: {e}")
        conn.rollback()
    finally:
        conn.close()

def initialize_maintenance_records(db: DatabaseManager):
    """初始化维保记录"""
    conn = db.get_connection()
    cursor = conn.cursor()
    
    try:
        # 获取所有零备件
        cursor.execute("SELECT id, maintenance_cycle_months FROM spare_parts")
        spare_parts = cursor.fetchall()
        
        import random
        
        maintenance_data = []
        technicians = ["张师傅", "李师傅", "王师傅", "赵师傅", "刘师傅"]
        
        for sp_id, cycle_months in spare_parts:
            # 随机生成过去的维保记录
            days_ago = random.randint(1, cycle_months * 30)
            maintenance_date = datetime.now() - timedelta(days=days_ago)
            next_maintenance_date = maintenance_date + timedelta(days=cycle_months * 30)
            
            technician = random.choice(technicians)
            notes = f"定期维保，状态良好"
            
            maintenance_data.append((
                sp_id,
                maintenance_date.strftime('%Y-%m-%d'),
                next_maintenance_date.strftime('%Y-%m-%d'),
                technician,
                notes
            ))
        
        # 插入维保记录
        for maint_data in maintenance_data:
            cursor.execute("""
                INSERT INTO maintenance_records 
                (spare_part_id, maintenance_date, next_maintenance_date, technician, notes)
                VALUES (?, ?, ?, ?, ?)
            """, maint_data)
        
        conn.commit()
        print(f"已插入 {len(maintenance_data)} 个维保记录")
        
    except Exception as e:
        print(f"初始化维保记录错误: {e}")
        conn.rollback()
    finally:
        conn.close()

def test_system():
    """测试系统功能"""
    print("\n开始测试系统功能...")
    
    manager = MaintenanceManager()
    
    # 测试系统概览
    overview = manager.get_system_overview()
    print(f"系统概览: {overview}")
    
    # 测试维保状态
    status_list = manager.get_maintenance_status()
    print(f"维保状态记录数: {len(status_list)}")
    
    # 测试维保提醒
    alerts = manager.get_maintenance_alerts()
    print(f"维保提醒记录数: {len(alerts)}")
    
    # 测试库存状态
    inventory = manager.get_inventory_status()
    print(f"库存记录数: {len(inventory)}")
    
    print("系统功能测试完成！")

if __name__ == "__main__":
    initialize_complete_system()
    test_system()
