-- 清理逾期维保记录，只保留2条

-- 首先查看当前逾期记录数量
SELECT 
    COUNT(*) as total_overdue
FROM maintenance_records 
WHERE DATE(next_maintenance_date) < DATE('now');

-- 创建临时表保存要保留的记录ID
CREATE TEMPORARY TABLE keep_records AS
SELECT id 
FROM maintenance_records 
WHERE DATE(next_maintenance_date) < DATE('now')
ORDER BY next_maintenance_date DESC
LIMIT 2;

-- 删除除了要保留的记录之外的所有逾期记录
DELETE FROM maintenance_records 
WHERE DATE(next_maintenance_date) < DATE('now')
AND id NOT IN (SELECT id FROM keep_records);

-- 查看清理后的结果
SELECT 
    COUNT(*) as total_records,
    SUM(CASE WHEN DATE(next_maintenance_date) < DATE('now') THEN 1 ELSE 0 END) as overdue_records,
    SUM(CASE WHEN DATE(next_maintenance_date) <= DATE('now', '+30 days') AND DATE(next_maintenance_date) >= DATE('now') THEN 1 ELSE 0 END) as upcoming_records
FROM maintenance_records;
