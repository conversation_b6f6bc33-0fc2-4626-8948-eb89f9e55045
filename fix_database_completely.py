#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完全修复数据库 - 重新插入所有数据
"""

import sqlite3
import os
from datetime import datetime, timedelta
import random

def fix_database_completely():
    """完全修复数据库"""
    
    db_path = 'equipment_maintenance.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("=== 检查当前数据库状态 ===")
        
        # 检查各表数据量
        cursor.execute("SELECT COUNT(*) FROM areas")
        areas_count = cursor.fetchone()[0]
        print(f"区域数量: {areas_count}")
        
        cursor.execute("SELECT COUNT(*) FROM equipment")
        equipment_count = cursor.fetchone()[0]
        print(f"设备数量: {equipment_count}")
        
        cursor.execute("SELECT COUNT(*) FROM spare_parts")
        parts_count = cursor.fetchone()[0]
        print(f"零备件数量: {parts_count}")
        
        cursor.execute("SELECT COUNT(*) FROM inventory")
        inventory_count = cursor.fetchone()[0]
        print(f"库存记录: {inventory_count}")
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_records")
        maintenance_count = cursor.fetchone()[0]
        print(f"维保记录: {maintenance_count}")
        
        # 如果零备件表为空，重新插入零备件数据
        if parts_count == 0:
            print("\n=== 插入零备件数据 ===")
            spare_parts_data = [
                # 堆垛机零备件
                ('主接触器', 6, '堆垛机主要电气元件，6个月维保周期'),
                ('变频器', 12, '堆垛机变频控制器，12个月维保周期'),
                ('传感器', 3, '堆垛机位置传感器，3个月维保周期'),
                
                # 入库皮带零备件
                ('皮带', 3, '入库皮带传送带，3个月维保周期'),
                ('滚筒', 6, '入库皮带滚筒，6个月维保周期'),
                
                # AGV小车零备件
                ('保险丝', 6, 'AGV小车保险丝，6个月维保周期'),
                ('电池', 12, 'AGV小车动力电池，12个月维保周期'),
                ('轮子', 9, 'AGV小车行走轮子，9个月维保周期')
            ]
            
            cursor.executemany('INSERT INTO spare_parts (name, maintenance_cycle_months, description) VALUES (?, ?, ?)', spare_parts_data)
            print(f"✅ 插入了{len(spare_parts_data)}种零备件")
        
        # 如果库存表为空，重新插入库存数据
        if inventory_count == 0:
            print("\n=== 插入库存数据 ===")
            # 获取零备件ID
            cursor.execute('SELECT id, name FROM spare_parts')
            parts = cursor.fetchall()
            
            inventory_data = []
            locations = ['仓库A-01', '仓库A-02', '仓库B-01', '仓库B-02', '仓库C-01', '仓库C-02', '仓库D-01', '仓库D-02']
            
            for i, (part_id, part_name) in enumerate(parts):
                quantity = random.randint(8, 25)  # 随机库存数量
                min_qty = random.randint(3, 8)    # 最低库存
                location = locations[i % len(locations)]
                inventory_data.append((part_id, quantity, min_qty, location))
            
            cursor.executemany('INSERT INTO inventory (spare_part_id, quantity, min_quantity, location) VALUES (?, ?, ?, ?)', inventory_data)
            print(f"✅ 插入了{len(inventory_data)}条库存记录")
        
        # 如果维保记录表为空，重新插入维保记录
        if maintenance_count == 0:
            print("\n=== 插入维保记录 ===")
            
            # 获取所有设备和零备件的对应关系
            cursor.execute('SELECT id, name, equipment_type, area_id FROM equipment')
            equipment_list = cursor.fetchall()
            
            cursor.execute('SELECT id, name, maintenance_cycle_months FROM spare_parts')
            spare_parts_list = cursor.fetchall()
            
            maintenance_records = []
            technicians = ['张师傅', '李师傅', '王师傅', '赵师傅', '陈师傅']
            today = datetime.now().date()
            
            # 为每台设备创建相应的维保记录
            for eq_id, eq_name, eq_type, area_id in equipment_list:
                # 根据设备类型选择对应的零备件
                if eq_type == '堆垛机':
                    # 堆垛机使用：主接触器、变频器、传感器
                    applicable_parts = [(1, '主接触器', 6), (2, '变频器', 12), (3, '传感器', 3)]
                elif eq_type == '入库皮带':
                    # 入库皮带使用：皮带、滚筒
                    applicable_parts = [(4, '皮带', 3), (5, '滚筒', 6)]
                elif eq_type == 'AGV小车':
                    # AGV小车使用：保险丝、电池、轮子
                    applicable_parts = [(6, '保险丝', 6), (7, '电池', 12), (8, '轮子', 9)]
                else:
                    continue
                
                # 为每个适用的零备件创建维保记录
                for part_id, part_name, cycle_months in applicable_parts:
                    # 创建多条历史维保记录
                    for record_num in range(1, 4):  # 每个零备件创建3条记录
                        # 计算维保日期（过去的日期）
                        days_ago = random.randint(30, 365)
                        maintenance_date = today - timedelta(days=days_ago)
                        next_maintenance_date = maintenance_date + timedelta(days=cycle_months * 30)
                        
                        technician = random.choice(technicians)
                        notes = f'维保记录 - {eq_name} - {part_name}'
                        
                        maintenance_records.append((
                            part_id, area_id, eq_id, 
                            maintenance_date.strftime('%Y-%m-%d'),
                            next_maintenance_date.strftime('%Y-%m-%d'),
                            technician, notes, 1
                        ))
            
            cursor.executemany('''
                INSERT INTO maintenance_records 
                (spare_part_id, area_id, equipment_id, maintenance_date, next_maintenance_date, technician, notes, quantity_used)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', maintenance_records)
            
            print(f"✅ 插入了{len(maintenance_records)}条维保记录")
        
        conn.commit()
        
        # 最终验证
        print("\n=== 最终验证 ===")
        cursor.execute("SELECT COUNT(*) FROM areas")
        areas_count = cursor.fetchone()[0]
        print(f"区域数量: {areas_count}")
        
        cursor.execute("SELECT COUNT(*) FROM equipment")
        equipment_count = cursor.fetchone()[0]
        print(f"设备数量: {equipment_count}")
        
        cursor.execute("SELECT COUNT(*) FROM spare_parts")
        parts_count = cursor.fetchone()[0]
        print(f"零备件数量: {parts_count}")
        
        cursor.execute("SELECT COUNT(*) FROM inventory")
        inventory_count = cursor.fetchone()[0]
        print(f"库存记录: {inventory_count}")
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_records")
        maintenance_count = cursor.fetchone()[0]
        print(f"维保记录: {maintenance_count}")
        
        if maintenance_count > 0:
            # 显示前几条维保记录
            cursor.execute("""
                SELECT mr.id, sp.name, a.name, e.name, mr.maintenance_date, mr.next_maintenance_date
                FROM maintenance_records mr
                JOIN spare_parts sp ON mr.spare_part_id = sp.id
                JOIN areas a ON mr.area_id = a.id
                JOIN equipment e ON mr.equipment_id = e.id
                LIMIT 3
            """)
            records = cursor.fetchall()
            print("\n前3条维保记录:")
            for record in records:
                print(f"  {record[1]} | {record[2]} | {record[3]} | {record[4]} -> {record[5]}")
        
        print("\n✅ 数据库修复完成！")
        
    except Exception as e:
        print(f"❌ 修复数据库时出错: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    fix_database_completely()
