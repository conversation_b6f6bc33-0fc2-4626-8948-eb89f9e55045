#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新维保数据 - 减少即将到期的维保记录到5条
"""

import sqlite3
from datetime import datetime, timedelta
import random

def update_maintenance_data():
    """更新维保数据，只保留5条即将到期的记录"""
    print("开始更新维保数据...")
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 删除所有现有维保记录
        cursor.execute("DELETE FROM maintenance_records")
        print("已清除现有维保记录")
        
        # 获取基础数据
        cursor.execute("SELECT id, name FROM areas")
        areas = cursor.fetchall()
        print(f"区域数量: {len(areas)}")
        
        cursor.execute("SELECT id, name, area_id FROM equipment")
        equipment = cursor.fetchall()
        print(f"设备数量: {len(equipment)}")
        
        cursor.execute("SELECT id, name, maintenance_cycle_months FROM spare_parts")
        spare_parts = cursor.fetchall()
        print(f"零备件数量: {len(spare_parts)}")
        
        if not areas or not equipment or not spare_parts:
            print("缺少基础数据，无法创建维保记录")
            return
        
        # 创建维保记录
        today = datetime.now().date()
        maintenance_records = []
        technicians = ['张师傅', '李师傅', '王师傅', '赵师傅', '刘师傅']
        record_id = 1
        
        # 1. 逾期维保记录（2条）
        print("创建逾期维保记录...")
        for i in range(2):
            area = random.choice(areas)
            eq = random.choice([e for e in equipment if e[2] == area[0]])
            spare_part = random.choice(spare_parts)
            
            overdue_days = random.randint(5, 30)
            maintenance_date = today - timedelta(days=365)
            next_maintenance_date = today - timedelta(days=overdue_days)
            
            maintenance_records.append((
                record_id, spare_part[0], area[0], eq[0], 
                maintenance_date.isoformat(), next_maintenance_date.isoformat(),
                random.choice(technicians), f"逾期{overdue_days}天的维保记录", random.randint(1, 3)
            ))
            record_id += 1
        
        # 2. 即将到期维保记录（5条）
        print("创建即将到期维保记录...")
        for i in range(5):
            area = random.choice(areas)
            eq = random.choice([e for e in equipment if e[2] == area[0]])
            spare_part = random.choice(spare_parts)
            
            days_remaining = random.randint(1, 30)
            maintenance_date = today - timedelta(days=365 - days_remaining)
            next_maintenance_date = today + timedelta(days=days_remaining)
            
            status_desc = ""
            if days_remaining <= 5:
                status_desc = f"紧急！还剩{days_remaining}天"
            elif days_remaining <= 15:
                status_desc = f"警告：还剩{days_remaining}天"
            else:
                status_desc = f"即将到期，还剩{days_remaining}天"
            
            maintenance_records.append((
                record_id, spare_part[0], area[0], eq[0], 
                maintenance_date.isoformat(), next_maintenance_date.isoformat(),
                random.choice(technicians), status_desc, random.randint(1, 2)
            ))
            record_id += 1
        
        # 3. 正常维保记录（10条）
        print("创建正常维保记录...")
        for i in range(10):
            area = random.choice(areas)
            eq = random.choice([e for e in equipment if e[2] == area[0]])
            spare_part = random.choice(spare_parts)
            
            days_remaining = random.randint(31, 180)
            maintenance_date = today - timedelta(days=random.randint(30, 180))
            next_maintenance_date = today + timedelta(days=days_remaining)
            
            maintenance_records.append((
                record_id, spare_part[0], area[0], eq[0], 
                maintenance_date.isoformat(), next_maintenance_date.isoformat(),
                random.choice(technicians), f"正常维保，还剩{days_remaining}天", random.randint(1, 2)
            ))
            record_id += 1
        
        # 插入维保记录
        cursor.executemany('''
            INSERT INTO maintenance_records 
            (id, spare_part_id, area_id, equipment_id, maintenance_date, next_maintenance_date, 
             technician, notes, quantity_used)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', maintenance_records)
        
        conn.commit()
        print(f"成功创建 {len(maintenance_records)} 条维保记录")
        
        # 统计结果
        cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date < ?", (today,))
        overdue_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date BETWEEN ? AND ?", 
                       (today, today + timedelta(days=30)))
        upcoming_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date > ?", 
                       (today + timedelta(days=30),))
        normal_count = cursor.fetchone()[0]
        
        print("\n维保状态统计:")
        print(f"  逾期维保: {overdue_count} 条")
        print(f"  即将到期: {upcoming_count} 条")
        print(f"  正常维保: {normal_count} 条")
        print("维保数据更新完成！")
        
    except Exception as e:
        print(f"更新维保数据时出错: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    update_maintenance_data()
