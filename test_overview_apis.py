#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_overview_apis():
    """测试概览和统计API"""
    
    print("=== 测试系统概览和统计API ===")
    
    try:
        # 测试概览API
        print("\n1. 测试概览API (/api/overview)")
        response = requests.get('http://127.0.0.1:5000/api/overview')
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"成功: {data.get('success')}")
            
            if data.get('success'):
                overview = data.get('data', {})
                print("概览数据:")
                print(f"  零备件总数: {overview.get('total_parts', 0)}")
                print(f"  逾期维保: {overview.get('overdue_maintenance', 0)}")
                print(f"  即将到期: {overview.get('upcoming_maintenance', 0)}")
                print(f"  库存不足: {overview.get('low_stock', 0)}")
            else:
                print(f"API错误: {data.get('error')}")
        else:
            print(f"HTTP错误: {response.text}")
        
        # 测试统计API
        print("\n2. 测试统计API (/api/statistics)")
        response = requests.get('http://127.0.0.1:5000/api/statistics')
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"成功: {data.get('success')}")
            
            if data.get('success'):
                stats = data.get('data', {})
                
                # 区域统计
                area_stats = stats.get('area_stats', [])
                print(f"\n区域统计 ({len(area_stats)} 个区域):")
                for area in area_stats:
                    print(f"  {area.get('area', '未知')}: {area.get('count', 0)} 条维保记录")
                
                # 状态统计
                status_stats = stats.get('status_stats', [])
                print(f"\n维保状态统计 ({len(status_stats)} 种状态):")
                for status in status_stats:
                    status_name = status.get('status', '未知')
                    status_text = {
                        'overdue': '已过期',
                        'warning': '即将到期', 
                        'normal': '正常'
                    }.get(status_name, status_name)
                    print(f"  {status_text}: {status.get('count', 0)} 条记录")
                    
            else:
                print(f"API错误: {data.get('error')}")
        else:
            print(f"HTTP错误: {response.text}")
        
        # 测试区域API
        print("\n3. 测试区域API (/api/areas)")
        response = requests.get('http://127.0.0.1:5000/api/areas')
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"成功: {data.get('success')}")
            
            if data.get('success'):
                areas = data.get('data', [])
                print(f"区域数量: {len(areas)}")
                for area in areas:
                    print(f"  {area.get('name', '未知')}: {area.get('description', '无描述')}")
            else:
                print(f"API错误: {data.get('error')}")
        else:
            print(f"HTTP错误: {response.text}")
            
        print("\n✅ API测试完成！")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    test_overview_apis()
