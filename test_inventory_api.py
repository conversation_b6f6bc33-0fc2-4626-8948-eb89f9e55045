#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_inventory_api():
    """测试库存API"""
    try:
        print("=== 测试库存状态API ===")
        
        # 测试库存状态API
        response = requests.get('http://127.0.0.1:5000/api/inventory/status')
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应成功: {data.get('success', False)}")
            if data.get('success'):
                inventory_data = data.get('data', [])
                print(f"库存记录数量: {len(inventory_data)}")
                if inventory_data:
                    print("前3条记录:")
                    for i, record in enumerate(inventory_data[:3]):
                        print(f"  {i+1}. {record}")
            else:
                print(f"API返回错误: {data.get('error', '未知错误')}")
        else:
            print(f"HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    test_inventory_api()
