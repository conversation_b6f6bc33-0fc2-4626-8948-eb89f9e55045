#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime, timedelta

def fix_area_names():
    """修正区域名称为成品区、辅料区、片烟区"""
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        print("=== 修正区域名称 ===")
        
        # 更新区域名称
        area_updates = [
            (1, '成品区', '成品生产和包装区域'),
            (2, '辅料区', '辅料存储和处理区域'),
            (3, '片烟区', '片烟加工和处理区域')
        ]
        
        for area_id, name, description in area_updates:
            cursor.execute('''
                UPDATE areas 
                SET name = ?, description = ? 
                WHERE id = ?
            ''', (name, description, area_id))
            print(f"更新区域 {area_id}: {name}")
        
        # 更新设备名称，使其更符合烟草行业
        equipment_updates = [
            (1, '包装机001', 'packaging_machine', 1, '自动包装设备'),
            (2, '切丝机002', 'cutting_machine', 3, '片烟切丝设备'),
            (3, '混料机003', 'mixing_machine', 2, '辅料混合设备'),
            (4, '输送带004', 'conveyor', 1, '成品输送系统'),
            (5, '烘干机005', 'drying_machine', 3, '片烟烘干设备'),
            (6, '检测设备006', 'inspection_device', 1, '质量检测设备')
        ]
        
        for equipment_id, name, equipment_type, area_id, description in equipment_updates:
            cursor.execute('''
                UPDATE equipment 
                SET name = ?, type = ?, area_id = ?, description = ? 
                WHERE id = ?
            ''', (name, equipment_type, area_id, description, equipment_id))
            print(f"更新设备 {equipment_id}: {name} (区域: {area_id})")
        
        # 更新零备件名称，使其更符合烟草行业
        spare_parts_updates = [
            (1, '包装膜', '包装机专用包装膜'),
            (2, '切刀片', '切丝机切刀片'),
            (3, '传动皮带', '设备传动系统皮带'),
            (4, '轴承6205', '标准深沟球轴承'),
            (5, '电机碳刷', '电机换向器碳刷'),
            (6, '过滤网', '烘干机过滤网'),
            (7, '密封圈', '设备密封组件'),
            (8, '传感器', '温度湿度传感器'),
            (9, '控制模块', 'PLC控制模块'),
            (10, '减速机齿轮', '减速机内部齿轮'),
            (11, '风扇叶片', '设备散热风扇叶片'),
            (12, '安全开关', '设备安全保护开关')
        ]
        
        for part_id, name, description in spare_parts_updates:
            cursor.execute('''
                UPDATE spare_parts 
                SET name = ?, description = ? 
                WHERE id = ?
            ''', (name, description, part_id))
            print(f"更新零备件 {part_id}: {name}")
        
        # 更新库存位置，使其更符合实际情况
        inventory_location_updates = [
            (1, '成品仓库-A01'),  # 包装膜
            (2, '片烟仓库-B01'),  # 切刀片
            (3, '维修仓库-C01'),  # 传动皮带
            (4, '维修仓库-C02'),  # 轴承6205
            (5, '维修仓库-C03'),  # 电机碳刷
            (6, '片烟仓库-B02'),  # 过滤网
            (7, '维修仓库-C04'),  # 密封圈
            (8, '辅料仓库-D01'),  # 传感器
            (9, '辅料仓库-D02'),  # 控制模块
            (10, '维修仓库-C05'), # 减速机齿轮
            (11, '维修仓库-C06'), # 风扇叶片
            (12, '成品仓库-A02')  # 安全开关
        ]
        
        for inventory_id, location in inventory_location_updates:
            cursor.execute('''
                UPDATE inventory 
                SET location = ? 
                WHERE id = ?
            ''', (location, inventory_id))
            print(f"更新库存位置 {inventory_id}: {location}")
        
        # 更新维保记录描述，使其更符合烟草行业
        maintenance_description_updates = [
            (1, '包装机定期保养', '更换包装膜，检查密封件'),
            (2, '切丝机定期维护', '更换切刀片，校准切丝参数'),
            (3, '混料机月度检查', '检查混料机运行状态'),
            (4, '输送带检查', '检查输送带张紧度'),
            (5, '烘干机维护', '需要更换过滤网'),
            (6, '检测设备校准', '需要重新校准检测参数'),
            (7, '密封系统维修', '密封圈更换'),
            (8, '传感器校准', '温湿度传感器精度校准'),
            (9, '控制系统维修', '控制模块更换'),
            (10, '减速机保养', '减速机齿轮组更换升级')
        ]
        
        for record_id, description, notes in maintenance_description_updates:
            cursor.execute('''
                UPDATE maintenance_records 
                SET description = ?, notes = ? 
                WHERE id = ?
            ''', (description, notes, record_id))
            print(f"更新维保记录 {record_id}: {description}")
        
        conn.commit()
        
        # 验证更新结果
        print("\n=== 验证更新结果 ===")
        
        cursor.execute('SELECT id, name, description FROM areas ORDER BY id')
        areas = cursor.fetchall()
        print("区域列表:")
        for area in areas:
            print(f"  {area[0]}: {area[1]} - {area[2]}")
        
        cursor.execute('SELECT id, name, area_id FROM equipment ORDER BY area_id, id')
        equipment = cursor.fetchall()
        print("\n设备列表:")
        for eq in equipment:
            cursor.execute('SELECT name FROM areas WHERE id = ?', (eq[2],))
            area_name = cursor.fetchone()[0]
            print(f"  {eq[0]}: {eq[1]} (区域: {area_name})")
        
        cursor.execute('SELECT id, name FROM spare_parts ORDER BY id LIMIT 6')
        parts = cursor.fetchall()
        print("\n零备件列表 (前6个):")
        for part in parts:
            print(f"  {part[0]}: {part[1]}")
        
        print("\n✅ 区域名称修正完成！")
        
    except Exception as e:
        print(f"❌ 修正区域名称时出错: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
        
    finally:
        conn.close()

if __name__ == "__main__":
    fix_area_names()
