#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建一些待执行的维保计划
"""

import sqlite3
from datetime import datetime, timedelta
import random

def create_pending_maintenance_plans():
    """创建一些待执行的维保计划"""
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 获取零备件、区域和设备信息
        cursor.execute("SELECT id, name FROM spare_parts")
        spare_parts = cursor.fetchall()
        
        cursor.execute("SELECT id, name FROM areas")
        areas = cursor.fetchall()
        
        cursor.execute("SELECT id, name, area_id FROM equipment")
        equipment = cursor.fetchall()
        
        print(f"找到 {len(spare_parts)} 个零备件")
        print(f"找到 {len(areas)} 个区域")
        print(f"找到 {len(equipment)} 个设备")
        
        # 清除现有的待执行计划
        cursor.execute("DELETE FROM maintenance_plans WHERE plan_status = 'pending'")
        print("清除了现有的待执行计划")
        
        # 创建一些待执行的维保计划
        today = datetime.now().date()
        plans_to_create = []
        
        # 为每个区域创建几个维保计划
        for area_id, area_name in areas:
            # 获取该区域的设备
            area_equipment = [eq for eq in equipment if eq[2] == area_id]
            
            if not area_equipment:
                continue
                
            # 为该区域创建2-3个维保计划
            num_plans = random.randint(2, 3)
            
            for i in range(num_plans):
                # 随机选择设备和零备件
                eq_id, eq_name, _ = random.choice(area_equipment)
                sp_id, sp_name = random.choice(spare_parts)
                
                # 计划日期：未来1-30天内
                days_ahead = random.randint(1, 30)
                planned_date = today + timedelta(days=days_ahead)
                
                # 创建计划
                plans_to_create.append((
                    sp_id,                    # spare_part_id
                    area_id,                  # area_id
                    eq_id,                    # equipment_id
                    planned_date.strftime('%Y-%m-%d'),  # planned_date
                    'pending',                # plan_status
                    f'计划维保 {sp_name} - {eq_name}'  # notes
                ))
        
        # 插入维保计划
        cursor.executemany("""
            INSERT INTO maintenance_plans 
            (spare_part_id, area_id, equipment_id, planned_date, plan_status, notes)
            VALUES (?, ?, ?, ?, ?, ?)
        """, plans_to_create)
        
        conn.commit()
        print(f"成功创建了 {len(plans_to_create)} 个待执行的维保计划")
        
        # 验证创建结果
        cursor.execute("SELECT COUNT(*) FROM maintenance_plans WHERE plan_status = 'pending'")
        pending_count = cursor.fetchone()[0]
        print(f"当前待执行计划数量: {pending_count}")
        
        # 显示前几个计划
        cursor.execute("""
            SELECT 
                mp.id,
                sp.name as spare_part_name,
                a.name as area_name,
                e.name as equipment_name,
                mp.planned_date,
                mp.notes
            FROM maintenance_plans mp
            JOIN spare_parts sp ON mp.spare_part_id = sp.id
            JOIN areas a ON mp.area_id = a.id
            JOIN equipment e ON mp.equipment_id = e.id
            WHERE mp.plan_status = 'pending'
            ORDER BY mp.planned_date ASC
            LIMIT 5
        """)
        
        plans = cursor.fetchall()
        print("\n前5个待执行计划:")
        for plan in plans:
            print(f"ID: {plan[0]}, 零件: {plan[1]}, 区域: {plan[2]}, 设备: {plan[3]}, 计划日期: {plan[4]}")
        
    except Exception as e:
        print(f"创建维保计划时出错: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    create_pending_maintenance_plans()
