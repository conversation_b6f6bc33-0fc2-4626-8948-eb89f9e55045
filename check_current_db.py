#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def check_current_database():
    """检查当前数据库的内容"""
    db_path = 'equipment_maintenance.db'
    
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return
    
    print(f"✅ 数据库文件存在: {db_path}")
    print(f"文件大小: {os.path.getsize(db_path)} 字节")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 检查所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"\n📋 数据库表数量: {len(tables)}")
        for table in tables:
            print(f"  - {table[0]}")
        
        # 检查每个表的数据量
        print("\n📊 各表数据统计:")
        
        # 区域表
        cursor.execute("SELECT COUNT(*) FROM areas")
        areas_count = cursor.fetchone()[0]
        print(f"  areas: {areas_count} 条记录")
        if areas_count > 0:
            cursor.execute("SELECT id, name FROM areas")
            areas = cursor.fetchall()
            for area in areas:
                print(f"    - {area[0]}: {area[1]}")
        
        # 设备表
        cursor.execute("SELECT COUNT(*) FROM equipment")
        equipment_count = cursor.fetchone()[0]
        print(f"  equipment: {equipment_count} 条记录")
        if equipment_count > 0:
            cursor.execute("SELECT id, name, equipment_type, area_id FROM equipment LIMIT 5")
            equipment = cursor.fetchall()
            for eq in equipment:
                print(f"    - {eq[0]}: {eq[1]} ({eq[2]}) - 区域{eq[3]}")
            if equipment_count > 5:
                print(f"    ... 还有 {equipment_count - 5} 条记录")
        
        # 零备件表
        cursor.execute("SELECT COUNT(*) FROM spare_parts")
        parts_count = cursor.fetchone()[0]
        print(f"  spare_parts: {parts_count} 条记录")
        if parts_count > 0:
            cursor.execute("SELECT id, name, maintenance_cycle_months FROM spare_parts")
            parts = cursor.fetchall()
            for part in parts:
                print(f"    - {part[0]}: {part[1]} ({part[2]}个月)")
        
        # 库存表
        cursor.execute("SELECT COUNT(*) FROM inventory")
        inventory_count = cursor.fetchone()[0]
        print(f"  inventory: {inventory_count} 条记录")
        
        # 维保记录表
        cursor.execute("SELECT COUNT(*) FROM maintenance_records")
        maintenance_count = cursor.fetchone()[0]
        print(f"  maintenance_records: {maintenance_count} 条记录")
        
        if maintenance_count > 0:
            cursor.execute("""
                SELECT mr.id, sp.name, a.name, e.name, mr.maintenance_date, mr.next_maintenance_date
                FROM maintenance_records mr
                JOIN spare_parts sp ON mr.spare_part_id = sp.id
                JOIN areas a ON mr.area_id = a.id
                JOIN equipment e ON mr.equipment_id = e.id
                LIMIT 3
            """)
            records = cursor.fetchall()
            print("  前3条维保记录:")
            for record in records:
                print(f"    - {record[1]} | {record[2]} | {record[3]} | {record[4]} -> {record[5]}")
        else:
            print("  ❌ 没有维保记录！")
        
        # 检查表结构
        print("\n🔧 maintenance_records表结构:")
        cursor.execute("PRAGMA table_info(maintenance_records)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else ''}")
        
    except Exception as e:
        print(f"❌ 检查数据库时出错: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    check_current_database()
