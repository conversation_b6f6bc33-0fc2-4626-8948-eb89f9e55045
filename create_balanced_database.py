#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
创建平衡的数据库 - 只有2条逾期记录
"""

import sqlite3
from datetime import datetime, timedelta
import random

def create_balanced_database():
    """创建平衡的数据库"""
    db_path = 'equipment_maintenance.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("=== 创建平衡的数据库 ===")
        
        # 创建表结构
        cursor.execute('''
            CREATE TABLE areas (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE equipment (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                area_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (area_id) REFERENCES areas (id)
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE spare_parts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                maintenance_cycle_months INTEGER NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (equipment_id) REFERENCES equipment (id)
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE inventory (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                spare_part_id INTEGER NOT NULL,
                quantity INTEGER DEFAULT 0,
                min_quantity INTEGER DEFAULT 1,
                location TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (spare_part_id) REFERENCES spare_parts (id)
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE maintenance_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                spare_part_id INTEGER NOT NULL,
                area_id INTEGER NOT NULL,
                equipment_id INTEGER NOT NULL,
                maintenance_date DATE NOT NULL,
                next_maintenance_date DATE NOT NULL,
                technician TEXT,
                notes TEXT,
                quantity_used INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (spare_part_id) REFERENCES spare_parts (id),
                FOREIGN KEY (area_id) REFERENCES areas (id),
                FOREIGN KEY (equipment_id) REFERENCES equipment (id)
            )
        ''')
        
        print("✅ 表结构创建完成")
        
        # 插入区域数据
        areas = [
            ("成品区", "成品区域，包含4台堆垛机和1条入库皮带"),
            ("辅料区", "辅料区域，包含3台堆垛机和1台AGV小车"),
            ("片烟区", "片烟区域，包含3台堆垛机")
        ]
        
        cursor.executemany("INSERT INTO areas (name, description) VALUES (?, ?)", areas)
        print("✅ 区域数据插入完成")
        
        # 插入设备数据
        equipment_data = [
            # 成品区设备
            (1, '堆垛机1', '堆垛机', '成品区第1台堆垛机'),
            (1, '堆垛机2', '堆垛机', '成品区第2台堆垛机'),
            (1, '堆垛机3', '堆垛机', '成品区第3台堆垛机'),
            (1, '堆垛机4', '堆垛机', '成品区第4台堆垛机'),
            (1, '入库皮带1', '入库皮带', '成品区入库皮带'),
            
            # 辅料区设备
            (2, '堆垛机5', '堆垛机', '辅料区第1台堆垛机'),
            (2, '堆垛机6', '堆垛机', '辅料区第2台堆垛机'),
            (2, '堆垛机7', '堆垛机', '辅料区第3台堆垛机'),
            (2, 'AGV小车1', 'AGV小车', '辅料区AGV小车'),
            
            # 片烟区设备
            (3, '堆垛机8', '堆垛机', '片烟区第1台堆垛机'),
            (3, '堆垛机9', '堆垛机', '片烟区第2台堆垛机'),
            (3, '堆垛机10', '堆垛机', '片烟区第3台堆垛机')
        ]
        
        cursor.executemany("INSERT INTO equipment (area_id, name, type, description) VALUES (?, ?, ?, ?)", equipment_data)
        print("✅ 设备数据插入完成")
        
        # 插入零备件数据
        cursor.execute('SELECT id, name, type FROM equipment')
        equipment_list = cursor.fetchall()
        
        spare_parts_data = []
        for eq_id, eq_name, eq_type in equipment_list:
            if eq_type == '堆垛机':
                spare_parts_data.extend([
                    (eq_id, '主接触器', 6, f'{eq_name}主接触器，6个月维保周期'),
                    (eq_id, '变频器', 12, f'{eq_name}变频器，12个月维保周期'),
                    (eq_id, '传感器', 3, f'{eq_name}传感器，3个月维保周期')
                ])
            elif eq_type == '入库皮带':
                spare_parts_data.extend([
                    (eq_id, '皮带', 3, f'{eq_name}皮带，3个月维保周期'),
                    (eq_id, '滚筒', 6, f'{eq_name}滚筒，6个月维保周期')
                ])
            elif eq_type == 'AGV小车':
                spare_parts_data.extend([
                    (eq_id, '保险丝', 6, f'{eq_name}保险丝，6个月维保周期'),
                    (eq_id, '电池', 12, f'{eq_name}电池，12个月维保周期'),
                    (eq_id, '轮子', 9, f'{eq_name}轮子，9个月维保周期')
                ])
        
        cursor.executemany('INSERT INTO spare_parts (equipment_id, name, maintenance_cycle_months, description) VALUES (?, ?, ?, ?)', spare_parts_data)
        print(f"✅ 零备件数据插入完成，共{len(spare_parts_data)}种")
        
        # 插入库存数据
        cursor.execute('SELECT id, name FROM spare_parts')
        parts = cursor.fetchall()
        
        inventory_data = []
        locations = ['仓库A-01', '仓库A-02', '仓库B-01', '仓库B-02', '仓库C-01', '仓库C-02', '仓库D-01', '仓库D-02']
        
        for i, (part_id, part_name) in enumerate(parts):
            quantity = random.randint(8, 25)
            min_qty = random.randint(3, 8)
            location = locations[i % len(locations)]
            inventory_data.append((part_id, quantity, min_qty, location))
        
        cursor.executemany('INSERT INTO inventory (spare_part_id, quantity, min_quantity, location) VALUES (?, ?, ?, ?)', inventory_data)
        print(f"✅ 库存数据插入完成，共{len(inventory_data)}条")
        
        # 插入维保记录 - 精心设计时间分布
        cursor.execute('''
            SELECT sp.id, sp.equipment_id, sp.maintenance_cycle_months, e.area_id
            FROM spare_parts sp
            JOIN equipment e ON sp.equipment_id = e.id
        ''')
        
        spare_parts_list = cursor.fetchall()
        today = datetime.now().date()
        technicians = ['张师傅', '李师傅', '王师傅', '赵师傅', '陈师傅']
        
        maintenance_records = []
        overdue_count = 0
        
        for sp_id, eq_id, cycle_months, area_id in spare_parts_list:
            # 为每个零备件创建1-2条维保记录
            num_records = random.randint(1, 2)
            
            for i in range(num_records):
                # 控制逾期记录数量
                if overdue_count < 2 and random.random() < 0.1:  # 10%概率创建逾期记录
                    # 创建逾期记录
                    days_overdue = random.randint(5, 30)
                    next_maintenance_date = today - timedelta(days=days_overdue)
                    maintenance_date = next_maintenance_date - timedelta(days=cycle_months * 30)
                    overdue_count += 1
                else:
                    # 创建正常记录
                    if random.random() < 0.3:  # 30%概率创建即将到期的记录
                        days_until_due = random.randint(1, 30)
                        next_maintenance_date = today + timedelta(days=days_until_due)
                    else:  # 70%概率创建正常记录
                        days_until_due = random.randint(31, 365)
                        next_maintenance_date = today + timedelta(days=days_until_due)
                    
                    maintenance_date = next_maintenance_date - timedelta(days=cycle_months * 30)
                
                technician = random.choice(technicians)
                notes = f'维保记录 - 设备{eq_id}'
                
                maintenance_records.append((
                    sp_id, area_id, eq_id,
                    maintenance_date.strftime('%Y-%m-%d'),
                    next_maintenance_date.strftime('%Y-%m-%d'),
                    technician, notes, 1
                ))
        
        cursor.executemany('''
            INSERT INTO maintenance_records 
            (spare_part_id, area_id, equipment_id, maintenance_date, next_maintenance_date, technician, notes, quantity_used)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', maintenance_records)
        
        print(f"✅ 维保记录插入完成，共{len(maintenance_records)}条")
        
        conn.commit()
        
        # 验证结果
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN DATE(next_maintenance_date) < DATE('now') THEN 1 ELSE 0 END) as overdue,
                SUM(CASE WHEN DATE(next_maintenance_date) <= DATE('now', '+30 days') AND DATE(next_maintenance_date) >= DATE('now') THEN 1 ELSE 0 END) as upcoming,
                SUM(CASE WHEN DATE(next_maintenance_date) > DATE('now', '+30 days') THEN 1 ELSE 0 END) as normal
            FROM maintenance_records
        """)
        
        result = cursor.fetchone()
        print(f"\n=== 数据库创建完成 ===")
        print(f"总维保记录: {result[0]}")
        print(f"逾期记录: {result[1]}")
        print(f"即将到期: {result[2]}")
        print(f"正常记录: {result[3]}")
        
    except Exception as e:
        print(f"❌ 创建数据库时出错: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    create_balanced_database()
