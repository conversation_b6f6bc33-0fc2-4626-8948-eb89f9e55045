#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本：更新维保记录表结构，添加区域和设备信息
"""

import sqlite3
from datetime import datetime

def migrate_maintenance_records():
    """迁移维保记录表，添加区域和设备信息"""
    print("开始迁移维保记录表...")
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 1. 检查当前表结构
        cursor.execute("PRAGMA table_info(maintenance_records)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'area_id' in columns and 'equipment_id' in columns:
            print("维保记录表已经包含区域和设备信息，无需迁移")
            return
        
        # 2. 备份现有数据
        print("1. 备份现有维保记录...")
        cursor.execute("CREATE TABLE maintenance_records_backup AS SELECT * FROM maintenance_records")
        
        # 3. 删除旧表
        print("2. 删除旧表...")
        cursor.execute("DROP TABLE maintenance_records")
        
        # 4. 创建新表结构
        print("3. 创建新表结构...")
        cursor.execute('''
            CREATE TABLE maintenance_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                spare_part_id INTEGER NOT NULL,
                area_id INTEGER NOT NULL,
                equipment_id INTEGER NOT NULL,
                maintenance_date DATE NOT NULL,
                next_maintenance_date DATE NOT NULL,
                technician TEXT,
                notes TEXT,
                quantity_used INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (spare_part_id) REFERENCES spare_parts (id),
                FOREIGN KEY (area_id) REFERENCES areas (id),
                FOREIGN KEY (equipment_id) REFERENCES equipment (id)
            )
        ''')
        
        # 5. 迁移数据（为现有记录分配默认的区域和设备）
        print("4. 迁移现有数据...")
        
        # 获取第一个区域和设备作为默认值
        cursor.execute("SELECT id FROM areas LIMIT 1")
        default_area_id = cursor.fetchone()
        if default_area_id:
            default_area_id = default_area_id[0]
        else:
            default_area_id = 1
            
        cursor.execute("SELECT id FROM equipment LIMIT 1")
        default_equipment_id = cursor.fetchone()
        if default_equipment_id:
            default_equipment_id = default_equipment_id[0]
        else:
            default_equipment_id = 1
        
        # 迁移备份数据到新表
        cursor.execute('''
            INSERT INTO maintenance_records 
            (spare_part_id, area_id, equipment_id, maintenance_date, next_maintenance_date, technician, notes, quantity_used, created_at)
            SELECT 
                spare_part_id, 
                ? as area_id,
                ? as equipment_id,
                maintenance_date, 
                next_maintenance_date, 
                technician, 
                notes,
                1 as quantity_used,
                created_at
            FROM maintenance_records_backup
        ''', (default_area_id, default_equipment_id))
        
        # 6. 重建索引
        print("5. 重建索引...")
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_maintenance_spare_part ON maintenance_records(spare_part_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_maintenance_area ON maintenance_records(area_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_maintenance_equipment ON maintenance_records(equipment_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_maintenance_next_date ON maintenance_records(next_maintenance_date)')
        
        conn.commit()
        print("维保记录表迁移完成！")
        
        # 7. 显示迁移结果
        cursor.execute("SELECT COUNT(*) FROM maintenance_records")
        records_count = cursor.fetchone()[0]
        print(f"- 迁移的维保记录数量: {records_count}")
        
    except Exception as e:
        print(f"迁移过程中出错: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

def cleanup_backup_table():
    """清理备份表"""
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        cursor.execute("DROP TABLE IF EXISTS maintenance_records_backup")
        conn.commit()
        print("备份表已清理")
    except Exception as e:
        print(f"清理备份表时出错: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    migrate_maintenance_records()
    
    # 询问是否清理备份表
    response = input("\n是否清理备份表？(y/n): ")
    if response.lower() == 'y':
        cleanup_backup_table()
