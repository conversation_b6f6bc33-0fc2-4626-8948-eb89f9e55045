#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime

def verify_data():
    """验证数据库中的数据"""
    
    conn = sqlite3.connect('equipment_maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        print("=== 数据库数据验证 ===")
        
        # 检查各表的记录数量
        tables = ['areas', 'equipment', 'spare_parts', 'inventory', 'maintenance_records']
        for table in tables:
            cursor.execute(f'SELECT COUNT(*) FROM {table}')
            count = cursor.fetchone()[0]
            print(f"{table}: {count} 条记录")
        
        print("\n=== 库存状态分布 ===")
        cursor.execute('''
            SELECT 
                sp.name,
                i.quantity,
                i.min_quantity,
                CASE 
                    WHEN i.quantity = 0 THEN '缺货'
                    WHEN i.quantity <= i.min_quantity THEN '库存不足'
                    ELSE '充足'
                END as status
            FROM inventory i
            JOIN spare_parts sp ON i.spare_part_id = sp.id
            ORDER BY 
                CASE 
                    WHEN i.quantity = 0 THEN 1
                    WHEN i.quantity <= i.min_quantity THEN 2
                    ELSE 3
                END, sp.name
        ''')
        
        status_count = {'缺货': 0, '库存不足': 0, '充足': 0}
        for row in cursor.fetchall():
            status = row['status']
            status_count[status] += 1
            print(f"{row['name']}: {row['quantity']}/{row['min_quantity']} - {status}")
        
        print(f"\n库存状态统计:")
        for status, count in status_count.items():
            print(f"  {status}: {count} 种零备件")
        
        print("\n=== 维保状态分布 ===")
        cursor.execute('''
            SELECT 
                e.name as equipment_name,
                sp.name as spare_part_name,
                mr.next_maintenance_date,
                CASE 
                    WHEN mr.next_maintenance_date < date('now') THEN '已过期'
                    WHEN mr.next_maintenance_date <= date('now', '+7 days') THEN '即将到期'
                    ELSE '正常'
                END as status
            FROM maintenance_records mr
            JOIN equipment e ON mr.equipment_id = e.id
            JOIN spare_parts sp ON mr.spare_part_id = sp.id
            ORDER BY 
                CASE 
                    WHEN mr.next_maintenance_date < date('now') THEN 1
                    WHEN mr.next_maintenance_date <= date('now', '+7 days') THEN 2
                    ELSE 3
                END, mr.next_maintenance_date
        ''')
        
        maintenance_status_count = {'已过期': 0, '即将到期': 0, '正常': 0}
        for row in cursor.fetchall():
            status = row['status']
            maintenance_status_count[status] += 1
            print(f"{row['equipment_name']} - {row['spare_part_name']}: {row['next_maintenance_date']} - {status}")
        
        print(f"\n维保状态统计:")
        for status, count in maintenance_status_count.items():
            print(f"  {status}: {count} 条记录")
            
        print("\n✅ 数据验证完成！")
        
    except Exception as e:
        print(f"❌ 验证数据时出错: {e}")
        
    finally:
        conn.close()

if __name__ == "__main__":
    verify_data()
