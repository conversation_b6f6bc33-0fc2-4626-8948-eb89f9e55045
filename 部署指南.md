# 设备零备件周期管理系统 - 部署指南

## 📋 系统概述

本系统是一个基于Python Flask的设备零备件周期管理系统，实现了以下核心功能：

### 🎯 核心特性
- **全局统一库存管理**：零件库存统一管理，不区分设备或区域
- **首次维保计划**：支持手动创建首次维保计划
- **自动维保计算**：维保完成后自动计算下次维保时间
- **多状态展示**：完整展示所有维保和库存状态
- **智能预警系统**：逾期、即将到期、库存不足等预警

### 🏗️ 技术架构
- **后端**：Python 3.7+ + Flask + SQLite
- **前端**：Bootstrap 5 + Chart.js + jQuery
- **数据库**：SQLite（支持升级到PostgreSQL）

## 🚀 快速部署

### 方式一：一键启动（推荐）
```bash
# 进入项目目录
cd QuanShengMing02

# 运行启动脚本（自动检查环境和依赖）
python start.py
```

### 方式二：手动部署
```bash
# 1. 检查Python版本（需要3.7+）
python --version

# 2. 安装依赖
pip install flask flask-cors

# 3. 初始化数据库（如果需要）
python initialize_system.py

# 4. 启动系统
python app.py
```

### 方式三：Windows批处理
```bash
# 双击运行或命令行执行
deploy.bat
```

## 📊 数据库结构

### 核心表结构
```sql
-- 区域表
areas (id, name, description, created_at)

-- 设备表  
equipment (id, area_id, name, type, description, created_at)

-- 零备件表
spare_parts (id, equipment_id, name, maintenance_cycle_months, description, created_at)

-- 全局库存表（新增）
inventory_global (id, spare_part_name, total_quantity, min_quantity, location, updated_at)

-- 维保记录表（优化）
maintenance_records (id, spare_part_id, area_id, equipment_id, maintenance_date, 
                    next_maintenance_date, technician, notes, quantity_used, 
                    is_first_maintenance, maintenance_status, created_at)

-- 维保计划表（新增）
maintenance_plans (id, spare_part_id, area_id, equipment_id, planned_date, 
                  plan_status, created_by, notes, created_at)
```

## 🔧 系统配置

### 基本配置
- **端口**：5000（可在app.py中修改）
- **数据库**：equipment_maintenance.db
- **调试模式**：开发时启用，生产环境建议关闭

### 环境要求
- Python 3.7 或更高版本
- 现代浏览器（Chrome、Firefox、Safari、Edge）
- 至少100MB磁盘空间

## 📖 使用说明

### 1. 系统概览页面
- 查看零备件总数、逾期维保、即将到期、库存不足等统计
- 维保状态列表显示所有零备件的详细状态
- 可视化图表展示维保进度和状态分布

### 2. 维保管理页面
- **创建维保计划**：为首次维保创建计划
- **维保提醒**：按时间范围筛选即将到期的维保项目
- **添加维保记录**：记录完成的维保工作
- **维保历史**：查看所有维保记录

### 3. 库存管理页面
- **全局库存状态**：统一显示所有零件的库存情况
- **库存更新**：在线修改库存数量和存放位置
- **状态预警**：自动标识缺货和库存不足的零件

### 4. 数据管理页面
- 查看区域、设备、零备件等基础数据
- 系统统计和分析数据

## 🎯 核心业务流程

### 维保管理流程
1. **首次维保**：
   - 在维保管理页面创建维保计划
   - 设置计划维保日期和备注
   - 执行维保后添加维保记录

2. **后续维保**：
   - 系统根据维保周期自动计算下次维保时间
   - 维保提醒功能提前通知即将到期的项目
   - 完成维保后更新记录，系统自动计算下次时间

### 库存管理流程
1. **全局统一管理**：
   - 所有零件库存统一在中央仓库管理
   - 不区分具体用在哪个设备或区域
   - 维保时自动扣减相应数量

2. **预警机制**：
   - 库存=0：缺货状态，红色警告
   - 库存≤最低库存：库存不足，黄色警告
   - 库存>最低库存：充足状态，绿色正常

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   netstat -ano | findstr :5000
   # 修改app.py中的端口号
   app.run(debug=True, host='0.0.0.0', port=5001)
   ```

2. **数据库文件损坏**
   ```bash
   # 重新初始化数据库
   python initialize_system.py
   ```

3. **依赖包缺失**
   ```bash
   # 重新安装依赖
   pip install -r requirements.txt
   ```

4. **浏览器缓存问题**
   - 按Ctrl+F5强制刷新页面
   - 清除浏览器缓存

### 日志查看
- Flask应用日志会在控制台显示
- 数据库操作错误会在控制台输出
- 建议在生产环境配置日志文件

## 🚀 生产环境部署

### 推荐配置
```bash
# 使用Gunicorn作为WSGI服务器
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app

# 使用Nginx作为反向代理
# nginx.conf配置示例
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 安全建议
- 修改Flask的secret_key
- 启用HTTPS加密传输
- 配置防火墙规则
- 定期备份数据库文件

## 📞 技术支持

### 系统信息
- **版本**：v1.0
- **开发时间**：2025年7月
- **技术栈**：Python + Flask + SQLite + Bootstrap + Chart.js

### 联系方式
如有问题或需要功能扩展，请联系开发团队。

---

**部署成功后访问地址**：http://127.0.0.1:5000
