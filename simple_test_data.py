#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime, timedelta

def create_simple_data():
    print("创建精简测试数据...")
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    # 清除现有维保记录
    cursor.execute("DELETE FROM maintenance_records")
    
    # 获取基础数据
    cursor.execute("SELECT id FROM areas LIMIT 3")
    area_ids = [row[0] for row in cursor.fetchall()]
    
    cursor.execute("SELECT id FROM equipment LIMIT 9")
    equipment_ids = [row[0] for row in cursor.fetchall()]
    
    cursor.execute("SELECT id FROM spare_parts LIMIT 9")
    spare_part_ids = [row[0] for row in cursor.fetchall()]
    
    today = datetime.now().date()
    
    # 创建9条记录
    records = [
        # 逾期记录 (3条)
        (1, spare_part_ids[0], area_ids[0], equipment_ids[0], today - timedelta(days=410), today - timedelta(days=45), "张师傅", "严重逾期，需立即处理", 1),
        (2, spare_part_ids[1], area_ids[1], equipment_ids[1], today - timedelta(days=385), today - timedelta(days=20), "李师傅", "中度逾期，尽快安排维保", 1),
        (3, spare_part_ids[2], area_ids[2], equipment_ids[2], today - timedelta(days=370), today - timedelta(days=5), "王师傅", "轻度逾期，请及时维保", 1),
        
        # 即将到期记录 (3条)
        (4, spare_part_ids[3], area_ids[0], equipment_ids[3], today - timedelta(days=362), today + timedelta(days=3), "张师傅", "紧急！还剩3天，请立即安排", 1),
        (5, spare_part_ids[4], area_ids[1], equipment_ids[4], today - timedelta(days=350), today + timedelta(days=15), "李师傅", "警告：还剩15天，需要准备", 1),
        (6, spare_part_ids[5], area_ids[2], equipment_ids[5], today - timedelta(days=337), today + timedelta(days=28), "王师傅", "提醒：还剩28天，可以开始计划", 1),
        
        # 正常状态记录 (3条)
        (7, spare_part_ids[6], area_ids[0], equipment_ids[6], today - timedelta(days=305), today + timedelta(days=60), "张师傅", "正常状态，还剩2个月", 1),
        (8, spare_part_ids[7], area_ids[1], equipment_ids[7], today - timedelta(days=245), today + timedelta(days=120), "李师傅", "状态良好，还剩4个月", 1),
        (9, spare_part_ids[8], area_ids[2], equipment_ids[8], today - timedelta(days=0), today + timedelta(days=365), "王师傅", "长期维保，还剩1年", 1),
    ]
    
    # 插入记录
    cursor.executemany('''
        INSERT INTO maintenance_records 
        (id, spare_part_id, area_id, equipment_id, maintenance_date, next_maintenance_date, technician, notes, quantity_used)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', records)
    
    # 更新库存 - 设置2个库存不足
    cursor.execute("UPDATE inventory SET quantity = 2, min_quantity = 5 WHERE spare_part_id = ?", (spare_part_ids[0],))
    cursor.execute("UPDATE inventory SET quantity = 1, min_quantity = 5 WHERE spare_part_id = ?", (spare_part_ids[1],))
    
    conn.commit()
    conn.close()
    
    print("✅ 精简数据创建完成！")
    print("   总记录: 9条")
    print("   逾期: 3条")
    print("   即将到期: 3条") 
    print("   正常: 3条")
    print("   库存不足: 2条")

if __name__ == "__main__":
    create_simple_data()
