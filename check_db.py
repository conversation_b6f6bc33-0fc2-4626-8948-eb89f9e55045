#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime, timedelta

def check_database():
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 检查维保记录数量
        cursor.execute("SELECT COUNT(*) FROM maintenance_records")
        total = cursor.fetchone()[0]
        print(f"总维保记录数: {total}")
        
        # 检查状态分布
        today = datetime.now().date()
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date < ?", (today,))
        overdue = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date BETWEEN ? AND ?", 
                       (today, today + timedelta(days=30)))
        upcoming = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date > ?", 
                       (today + timedelta(days=30),))
        normal = cursor.fetchone()[0]
        
        print(f"逾期: {overdue}")
        print(f"即将到期: {upcoming}")
        print(f"正常: {normal}")
        
        # 显示即将到期的记录详情
        cursor.execute("""
            SELECT sp.name, e.name, mr.next_maintenance_date, mr.notes
            FROM maintenance_records mr
            JOIN spare_parts sp ON mr.spare_part_id = sp.id
            JOIN equipment e ON mr.equipment_id = e.id
            WHERE mr.next_maintenance_date BETWEEN ? AND ?
            ORDER BY mr.next_maintenance_date
        """, (today, today + timedelta(days=30)))
        
        upcoming_records = cursor.fetchall()
        print(f"\n即将到期的维保记录 ({len(upcoming_records)} 条):")
        for record in upcoming_records:
            print(f"  {record[0]} - {record[1]} - {record[2]} - {record[3]}")
            
    except Exception as e:
        print(f"检查数据库出错: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    check_database()
