#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备零备件周期管理系统 - 维保管理业务逻辑
"""

from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from database import DatabaseManager

class MaintenanceManager:
    """维保管理类"""
    
    def __init__(self, db_manager: DatabaseManager = None):
        """初始化维保管理器"""
        self.db = db_manager or DatabaseManager()
    
    def get_system_overview(self) -> Dict[str, Any]:
        """获取系统概览数据"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        try:
            # 获取零备件总数
            cursor.execute("SELECT COUNT(*) FROM spare_parts")
            total_parts = cursor.fetchone()[0]
            
            # 获取逾期维保数量 - 基于维保记录统计（过滤被替代的记录）
            today = datetime.now().date()
            cursor.execute("""
                SELECT COUNT(*)
                FROM maintenance_records mr
                WHERE mr.next_maintenance_date < ?
                AND (mr.maintenance_status IS NULL OR mr.maintenance_status != 'replaced_by_plan')
            """, (today,))
            overdue_count = cursor.fetchone()[0]

            # 获取即将到期数量（30天内）- 基于维保记录统计（过滤被替代的记录）
            future_date = today + timedelta(days=30)
            cursor.execute("""
                SELECT COUNT(*)
                FROM maintenance_records mr
                WHERE mr.next_maintenance_date BETWEEN ? AND ?
                AND (mr.maintenance_status IS NULL OR mr.maintenance_status != 'replaced_by_plan')
            """, (today, future_date))
            upcoming_count = cursor.fetchone()[0]
            
            # 获取库存不足数量 - 使用全局库存
            cursor.execute("""
                SELECT COUNT(*)
                FROM inventory_global
                WHERE total_quantity <= min_quantity
            """)
            low_stock_count = cursor.fetchone()[0]
            
            return {
                'total_parts': total_parts,
                'overdue_maintenance': overdue_count,
                'upcoming_maintenance': upcoming_count,
                'low_stock': low_stock_count
            }
            
        finally:
            conn.close()
    
    def calculate_maintenance_status(self, next_maintenance_date: str, cycle_months: int) -> str:
        """计算维保状态的统一方法"""
        if not next_maintenance_date:
            return 'no_record'

        today = datetime.now().date()
        next_date = datetime.strptime(next_maintenance_date, '%Y-%m-%d').date()
        days_remaining = (next_date - today).days

        if days_remaining < 0:
            return 'overdue'
        elif days_remaining <= 30:
            return 'warning'
        else:
            return 'normal'

    def get_maintenance_status(self, page: int = 1, per_page: int = 10) -> Dict[str, Any]:
        """获取维保状态列表"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            # 首先检查maintenance_records表是否有area_id和equipment_id列
            cursor.execute("PRAGMA table_info(maintenance_records)")
            columns = [row[1] for row in cursor.fetchall()]

            if 'area_id' in columns and 'equipment_id' in columns and 'quantity_used' in columns:
                # 新版本查询（包含区域和设备信息）
                query = """
                    SELECT
                        mr.id,
                        sp.id as spare_part_id,
                        sp.name as spare_part_name,
                        sp.maintenance_cycle_months,
                        a.name as area_name,
                        e.name as equipment_name,
                        mr.maintenance_date,
                        mr.next_maintenance_date,
                        mr.technician,
                        mr.notes,
                        mr.quantity_used,
                        ig.total_quantity as stock_quantity,
                        ig.min_quantity,
                        ig.location
                    FROM maintenance_records mr
                    JOIN spare_parts sp ON mr.spare_part_id = sp.id
                    JOIN areas a ON mr.area_id = a.id
                    JOIN equipment e ON mr.equipment_id = e.id
                    LEFT JOIN inventory_global ig ON sp.name = ig.spare_part_name
                    WHERE (mr.maintenance_status IS NULL OR mr.maintenance_status != 'replaced_by_plan')
                    ORDER BY
                        CASE
                            WHEN mr.next_maintenance_date < date('now') THEN 0  -- 逾期优先
                            WHEN mr.next_maintenance_date <= date('now', '+30 days') THEN 1  -- 即将到期次之
                            ELSE 2  -- 正常状态最后
                        END,
                        mr.maintenance_date DESC,  -- 同状态内按维保时间降序（最新的在前）
                        mr.next_maintenance_date ASC,  -- 同维保时间按下次维保日期升序
                        mr.id DESC
                """
            else:
                # 旧版本查询（兼容模式）
                query = """
                    SELECT
                        mr.id,
                        sp.id as spare_part_id,
                        sp.name as spare_part_name,
                        sp.maintenance_cycle_months,
                        '未指定' as area_name,
                        '未指定' as equipment_name,
                        mr.maintenance_date,
                        mr.next_maintenance_date,
                        mr.technician,
                        mr.notes,
                        1 as quantity_used,
                        ig.total_quantity as stock_quantity,
                        ig.min_quantity,
                        ig.location
                    FROM maintenance_records mr
                    JOIN spare_parts sp ON mr.spare_part_id = sp.id
                    LEFT JOIN inventory_global ig ON sp.name = ig.spare_part_name
                    WHERE (mr.maintenance_status IS NULL OR mr.maintenance_status != 'replaced_by_plan')
                    ORDER BY
                        CASE
                            WHEN mr.next_maintenance_date < date('now') THEN 0  -- 逾期优先
                            WHEN mr.next_maintenance_date <= date('now', '+30 days') THEN 1  -- 即将到期次之
                            ELSE 2  -- 正常状态最后
                        END,
                        mr.maintenance_date DESC,  -- 同状态内按维保时间降序（最新的在前）
                        mr.next_maintenance_date ASC,  -- 同维保时间按下次维保日期升序
                        mr.id DESC
                """

            cursor.execute(query)
            results = []
            today = datetime.now().date()
            
            for row in cursor.fetchall():
                row_dict = dict(row)
                
                # 计算维保状态
                next_date = row_dict['next_maintenance_date']
                if next_date:
                    next_date = datetime.strptime(next_date, '%Y-%m-%d').date()
                    days_remaining = (next_date - today).days
                    cycle_days = row_dict['maintenance_cycle_months'] * 30

                    # 计算进度：0% = 刚维保完，100% = 到了维保时间，>100% = 逾期
                    # progress = (已过天数 / 周期总天数) * 100
                    days_passed = cycle_days - days_remaining
                    progress = (days_passed / cycle_days) * 100

                    if days_remaining < 0:
                        status = 'overdue'
                        status_text = '逾期'
                        # 逾期时进度超过100%
                        progress = max(100, progress)
                    elif days_remaining <= 30:
                        status = 'warning'
                        status_text = '即将到期'
                        # 确保进度不小于0%
                        progress = max(0, progress)
                    else:
                        status = 'normal'
                        status_text = '正常'
                        # 确保进度不小于0%
                        progress = max(0, progress)
                else:
                    status = 'no_record'
                    status_text = '无记录'
                    days_remaining = None
                    progress = 0
                
                # 计算库存状态
                quantity = row_dict['stock_quantity'] or 0
                min_quantity = row_dict['min_quantity'] or 1
                
                if quantity == 0:
                    stock_status = 'out_of_stock'
                    stock_text = '缺货'
                elif quantity <= min_quantity:
                    stock_status = 'low_stock'
                    stock_text = '库存不足'
                else:
                    stock_status = 'sufficient'
                    stock_text = '充足'
                
                row_dict.update({
                    'status': status,
                    'status_text': status_text,
                    'days_remaining': days_remaining,
                    'progress': round(progress, 1),
                    'stock_status': stock_status,
                    'stock_text': stock_text
                })
                
                results.append(row_dict)

            # 计算分页信息
            total_records = len(results)
            total_pages = (total_records + per_page - 1) // per_page  # 向上取整

            # 计算分页范围
            start_index = (page - 1) * per_page
            end_index = start_index + per_page

            # 获取当前页的数据
            paginated_results = results[start_index:end_index]

            return {
                'data': paginated_results,
                'pagination': {
                    'current_page': page,
                    'per_page': per_page,
                    'total_records': total_records,
                    'total_pages': total_pages,
                    'has_prev': page > 1,
                    'has_next': page < total_pages
                }
            }

        finally:
            conn.close()

    def get_maintenance_records_list(self, page: int = 1, per_page: int = 10) -> Dict[str, Any]:
        """获取维保记录列表（按维保时间降序排序）"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            # 首先检查maintenance_records表是否有area_id和equipment_id列
            cursor.execute("PRAGMA table_info(maintenance_records)")
            columns = [row[1] for row in cursor.fetchall()]

            if 'area_id' in columns and 'equipment_id' in columns and 'quantity_used' in columns:
                # 新版本查询（包含区域和设备信息）
                query = """
                    SELECT
                        mr.id,
                        sp.id as spare_part_id,
                        sp.name as spare_part_name,
                        sp.maintenance_cycle_months,
                        a.name as area_name,
                        e.name as equipment_name,
                        mr.maintenance_date,
                        mr.next_maintenance_date,
                        mr.technician,
                        mr.notes,
                        mr.quantity_used,
                        ig.total_quantity as stock_quantity,
                        ig.min_quantity,
                        ig.location
                    FROM maintenance_records mr
                    JOIN spare_parts sp ON mr.spare_part_id = sp.id
                    JOIN areas a ON mr.area_id = a.id
                    JOIN equipment e ON mr.equipment_id = e.id
                    LEFT JOIN inventory_global ig ON sp.name = ig.spare_part_name
                    WHERE (mr.maintenance_status IS NULL OR mr.maintenance_status != 'replaced_by_plan')
                    ORDER BY mr.maintenance_date DESC, mr.id DESC
                """
            else:
                # 旧版本查询（兼容模式）
                query = """
                    SELECT
                        mr.id,
                        sp.id as spare_part_id,
                        sp.name as spare_part_name,
                        sp.maintenance_cycle_months,
                        '未指定' as area_name,
                        '未指定' as equipment_name,
                        mr.maintenance_date,
                        mr.next_maintenance_date,
                        mr.technician,
                        mr.notes,
                        1 as quantity_used,
                        ig.total_quantity as stock_quantity,
                        ig.min_quantity,
                        ig.location
                    FROM maintenance_records mr
                    JOIN spare_parts sp ON mr.spare_part_id = sp.id
                    LEFT JOIN inventory_global ig ON sp.name = ig.spare_part_name
                    WHERE (mr.maintenance_status IS NULL OR mr.maintenance_status != 'replaced_by_plan')
                    ORDER BY mr.maintenance_date DESC, mr.id DESC
                """

            cursor.execute(query)
            results = []
            today = datetime.now().date()

            for row in cursor.fetchall():
                row_dict = dict(row)

                # 计算维保状态
                next_date = row_dict['next_maintenance_date']
                if next_date:
                    next_date = datetime.strptime(next_date, '%Y-%m-%d').date()
                    days_remaining = (next_date - today).days
                    cycle_days = row_dict['maintenance_cycle_months'] * 30

                    # 计算进度：0% = 刚维保完，100% = 到了维保时间，>100% = 逾期
                    # progress = (已过天数 / 周期总天数) * 100
                    days_passed = cycle_days - days_remaining
                    progress = (days_passed / cycle_days) * 100

                    if days_remaining < 0:
                        status = 'overdue'
                        status_text = '逾期'
                        status_class = 'danger'
                        progress = min(progress, 300)  # 限制最大显示为300%
                    elif days_remaining <= 30:
                        status = 'warning'
                        status_text = '即将到期'
                        status_class = 'warning'
                    else:
                        status = 'normal'
                        status_text = '正常'
                        status_class = 'success'
                else:
                    days_remaining = None
                    status = 'no_record'
                    status_text = '无记录'
                    status_class = 'secondary'
                    progress = 0

                # 计算剩余天数显示
                if days_remaining is not None:
                    if days_remaining < 0:
                        days_text = f'逾期{abs(days_remaining)}天'
                    else:
                        days_text = f'{days_remaining}天'
                else:
                    days_text = '无记录'

                row_dict.update({
                    'days_remaining': days_remaining,
                    'days_text': days_text,
                    'status': status,
                    'status_text': status_text,
                    'status_class': status_class,
                    'progress': max(0, min(progress, 300))  # 确保进度在0-300%之间
                })

                results.append(row_dict)

            # 分页处理
            total_count = len(results)
            start_index = (page - 1) * per_page
            end_index = start_index + per_page
            paginated_results = results[start_index:end_index]

            # 计算分页信息
            total_pages = (total_count + per_page - 1) // per_page

            return {
                'data': paginated_results,
                'pagination': {
                    'current_page': page,
                    'per_page': per_page,
                    'total_count': total_count,
                    'total_pages': total_pages,
                    'has_prev': page > 1,
                    'has_next': page < total_pages
                }
            }

        finally:
            conn.close()

    def get_maintenance_alerts(self, days_ahead: int = 30) -> List[Dict[str, Any]]:
        """获取维保提醒"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        try:
            today = datetime.now().date()
            future_date = today + timedelta(days=days_ahead)
            
            query = """
                SELECT
                    sp.id,
                    sp.name as spare_part_name,
                    sp.maintenance_cycle_months,
                    e.name as equipment_name,
                    a.name as area_name,
                    mr.next_maintenance_date,
                    mr.technician,
                    mr.notes,
                    ig.total_quantity as quantity
                FROM maintenance_records mr
                JOIN spare_parts sp ON mr.spare_part_id = sp.id
                JOIN areas a ON mr.area_id = a.id
                JOIN equipment e ON mr.equipment_id = e.id
                LEFT JOIN inventory_global ig ON sp.name = ig.spare_part_name
                WHERE (mr.next_maintenance_date <= ? OR mr.next_maintenance_date < ?)
                AND (mr.maintenance_status IS NULL OR mr.maintenance_status != 'replaced_by_plan')
                ORDER BY mr.next_maintenance_date ASC
            """
            
            cursor.execute(query, (future_date, today))
            results = []
            
            for row in cursor.fetchall():
                row_dict = dict(row)
                
                next_date = row_dict['next_maintenance_date']
                if next_date:
                    next_date = datetime.strptime(next_date, '%Y-%m-%d').date()
                    days_remaining = (next_date - today).days
                    
                    if days_remaining < 0:
                        priority = 'high'
                        priority_text = '逾期'
                    elif days_remaining <= 30:
                        priority = 'high'
                        priority_text = '紧急'
                    else:
                        priority = 'low'
                        priority_text = '一般'
                else:
                    days_remaining = None
                    priority = 'high'
                    priority_text = '紧急'
                
                row_dict.update({
                    'days_remaining': days_remaining,
                    'priority': priority,
                    'priority_text': priority_text
                })
                
                results.append(row_dict)
            
            return results
            
        finally:
            conn.close()
    
    def add_maintenance_record(self, spare_part_id: int, area_id: int, equipment_id: int,
                             maintenance_date: str, technician: str = "", notes: str = "",
                             quantity_used: int = 1, is_first_maintenance: bool = False) -> bool:
        """添加维保记录 - 支持首次维保和后续维保"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            # 获取零备件信息
            cursor.execute("SELECT name, maintenance_cycle_months FROM spare_parts WHERE id = ?",
                         (spare_part_id,))
            result = cursor.fetchone()
            if not result:
                print("零备件不存在")
                return False

            spare_part_name, cycle_months = result

            # 计算下次维保日期
            maintenance_dt = datetime.strptime(maintenance_date, '%Y-%m-%d')
            next_maintenance_dt = maintenance_dt + timedelta(days=cycle_months * 30)
            next_maintenance_date = next_maintenance_dt.strftime('%Y-%m-%d')

            # 检查全局库存是否足够
            cursor.execute("SELECT total_quantity FROM inventory_global WHERE spare_part_name = ?",
                         (spare_part_name,))
            inventory_result = cursor.fetchone()

            if not inventory_result:
                error_msg = f"零备件 '{spare_part_name}' 没有库存记录，请先添加库存"
                print(error_msg)
                raise Exception(error_msg)

            current_stock = inventory_result[0]
            if current_stock < quantity_used:
                error_msg = f"库存不足！零备件 '{spare_part_name}' 当前库存: {current_stock}，需要: {quantity_used}"
                print(f"库存不足，当前库存: {current_stock}, 需要: {quantity_used}")
                raise Exception(error_msg)

            print(f"库存检查通过，当前库存: {current_stock}, 需要: {quantity_used}")

            # 插入维保记录
            cursor.execute("""
                INSERT INTO maintenance_records
                (spare_part_id, area_id, equipment_id, maintenance_date, next_maintenance_date,
                 technician, notes, quantity_used, is_first_maintenance, maintenance_status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'completed')
            """, (spare_part_id, area_id, equipment_id, maintenance_date,
                  next_maintenance_date, technician, notes, quantity_used, is_first_maintenance))

            # 扣减全局库存
            if inventory_result:
                new_quantity = inventory_result[0] - quantity_used
                cursor.execute("""
                    UPDATE inventory_global
                    SET total_quantity = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE spare_part_name = ?
                """, (new_quantity, spare_part_name))

            # 如果是从维保计划执行的，更新计划状态
            if is_first_maintenance:
                cursor.execute("""
                    UPDATE maintenance_plans
                    SET plan_status = 'completed'
                    WHERE spare_part_id = ? AND area_id = ? AND equipment_id = ?
                    AND plan_status = 'pending'
                """, (spare_part_id, area_id, equipment_id))

            conn.commit()
            print(f"维保记录添加成功: {spare_part_name}, 下次维保: {next_maintenance_date}")
            return True

        except Exception as e:
            print(f"添加维保记录错误: {e}")
            conn.rollback()
            # 重新抛出异常，让API层处理
            raise e
        finally:
            conn.close()
    
    def get_inventory_status(self) -> List[Dict[str, Any]]:
        """获取库存状态 - 使用全局统一库存管理"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            # 使用新的全局库存表
            query = """
                SELECT
                    id,
                    spare_part_name,
                    total_quantity,
                    min_quantity,
                    location,
                    updated_at
                FROM inventory_global
                ORDER BY spare_part_name
            """

            cursor.execute(query)
            results = []

            for row in cursor.fetchall():
                row_dict = dict(row)

                quantity = row_dict['total_quantity']
                min_quantity = row_dict['min_quantity']

                if quantity == 0:
                    status = 'out_of_stock'
                    status_text = '缺货'
                elif quantity <= min_quantity:
                    status = 'low_stock'
                    status_text = '库存不足'
                else:
                    status = 'sufficient'
                    status_text = '充足'

                row_dict.update({
                    'status': status,
                    'status_text': status_text
                })

                results.append(row_dict)

            return results

        finally:
            conn.close()

    def update_global_inventory(self, spare_part_name: str, quantity: int, location: str = None) -> bool:
        """更新全局库存"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            if location:
                cursor.execute("""
                    UPDATE inventory_global
                    SET total_quantity = ?, location = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE spare_part_name = ?
                """, (quantity, location, spare_part_name))
            else:
                cursor.execute("""
                    UPDATE inventory_global
                    SET total_quantity = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE spare_part_name = ?
                """, (quantity, spare_part_name))

            conn.commit()
            return cursor.rowcount > 0

        except Exception as e:
            print(f"更新全局库存错误: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def create_maintenance_plan(self, spare_part_id: int, area_id: int, equipment_id: int,
                               planned_date: str, notes: str = "", from_alert: bool = False, alert_source: dict = None) -> bool:
        """创建首次维保计划"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            # 首先检查是否需要添加新字段
            cursor.execute("PRAGMA table_info(maintenance_plans)")
            columns = [col[1] for col in cursor.fetchall()]

            # 如果没有from_alert字段，添加它
            if 'from_alert' not in columns:
                cursor.execute("ALTER TABLE maintenance_plans ADD COLUMN from_alert INTEGER DEFAULT 0")
                print("添加了from_alert字段到maintenance_plans表")

            # 如果没有alert_source字段，添加它
            if 'alert_source' not in columns:
                cursor.execute("ALTER TABLE maintenance_plans ADD COLUMN alert_source TEXT")
                print("添加了alert_source字段到maintenance_plans表")

            # 准备插入数据
            alert_source_json = None
            if alert_source:
                import json
                alert_source_json = json.dumps(alert_source)

            cursor.execute("""
                INSERT INTO maintenance_plans
                (spare_part_id, area_id, equipment_id, planned_date, notes, from_alert, alert_source)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (spare_part_id, area_id, equipment_id, planned_date, notes, 1 if from_alert else 0, alert_source_json))

            conn.commit()
            return True

        except Exception as e:
            print(f"创建维保计划错误: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def get_maintenance_plans(self, status: str = 'pending') -> List[Dict[str, Any]]:
        """获取维保计划列表"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            query = """
                SELECT
                    mp.id,
                    sp.name as spare_part_name,
                    a.name as area_name,
                    e.name as equipment_name,
                    mp.planned_date,
                    mp.plan_status,
                    mp.notes,
                    mp.created_at
                FROM maintenance_plans mp
                JOIN spare_parts sp ON mp.spare_part_id = sp.id
                JOIN areas a ON mp.area_id = a.id
                JOIN equipment e ON mp.equipment_id = e.id
                WHERE mp.plan_status = ?
                ORDER BY mp.planned_date ASC
            """

            cursor.execute(query, (status,))
            results = []

            for row in cursor.fetchall():
                row_dict = dict(row)
                results.append(row_dict)

            return results

        finally:
            conn.close()

    def get_maintenance_plan_by_id(self, plan_id: int) -> Dict[str, Any]:
        """根据ID获取维保计划详情"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            query = """
                SELECT
                    mp.id,
                    mp.spare_part_id,
                    sp.name as spare_part_name,
                    mp.area_id,
                    a.name as area_name,
                    mp.equipment_id,
                    e.name as equipment_name,
                    mp.planned_date,
                    mp.plan_status,
                    mp.notes,
                    mp.created_at,
                    sp.maintenance_cycle_months
                FROM maintenance_plans mp
                JOIN spare_parts sp ON mp.spare_part_id = sp.id
                JOIN areas a ON mp.area_id = a.id
                JOIN equipment e ON mp.equipment_id = e.id
                WHERE mp.id = ?
            """

            cursor.execute(query, (plan_id,))
            row = cursor.fetchone()

            if row:
                return dict(row)
            else:
                return None

        finally:
            conn.close()

    def execute_maintenance_plan(self, plan_id: int, maintenance_date: str,
                                technician: str = "", notes: str = "",
                                quantity_used: int = 1) -> bool:
        """执行维保计划 - 将计划转为维保记录"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            # 1. 获取维保计划详情
            plan = self.get_maintenance_plan_by_id(plan_id)
            if not plan or plan['plan_status'] != 'pending':
                print("维保计划不存在或已完成")
                return False

            # 2. 检查全局库存是否足够
            cursor.execute("SELECT total_quantity FROM inventory_global WHERE spare_part_name = ?",
                         (plan['spare_part_name'],))
            inventory_result = cursor.fetchone()

            if not inventory_result:
                print(f"零备件 '{plan['spare_part_name']}' 没有库存记录")
                raise Exception(f"零备件 '{plan['spare_part_name']}' 没有库存记录，请先添加库存")

            current_stock = inventory_result[0]
            if current_stock < quantity_used:
                print(f"库存不足，当前库存: {current_stock}, 需要: {quantity_used}")
                raise Exception(f"库存不足！零备件 '{plan['spare_part_name']}' 当前库存: {current_stock}，需要: {quantity_used}")

            print(f"库存检查通过，当前库存: {current_stock}, 需要: {quantity_used}")

            # 3. 计算下次维保日期
            from datetime import datetime, timedelta
            maintenance_dt = datetime.strptime(maintenance_date, '%Y-%m-%d')
            next_maintenance_dt = maintenance_dt + timedelta(days=plan['maintenance_cycle_months'] * 30)
            next_maintenance_date = next_maintenance_dt.strftime('%Y-%m-%d')

            # 4. 创建维保记录
            cursor.execute("""
                INSERT INTO maintenance_records
                (spare_part_id, area_id, equipment_id, maintenance_date, next_maintenance_date,
                 technician, notes, quantity_used, is_first_maintenance, maintenance_status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, 'completed')
            """, (plan['spare_part_id'], plan['area_id'], plan['equipment_id'],
                  maintenance_date, next_maintenance_date, technician,
                  f"从维保计划执行: {notes}" if notes else f"从维保计划执行: {plan['notes'] or ''}",
                  quantity_used))

            # 5. 扣减全局库存
            if inventory_result:
                new_quantity = inventory_result[0] - quantity_used
                cursor.execute("""
                    UPDATE inventory_global
                    SET total_quantity = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE spare_part_name = ?
                """, (new_quantity, plan['spare_part_name']))

            # 6. 更新维保计划状态为已完成
            cursor.execute("""
                UPDATE maintenance_plans
                SET plan_status = 'completed'
                WHERE id = ?
            """, (plan_id,))

            # 7. 如果这个计划是从维保提醒创建的，更新相关的维保记录
            cursor.execute("""
                SELECT from_alert, alert_source FROM maintenance_plans WHERE id = ?
            """, (plan_id,))
            plan_source = cursor.fetchone()

            if plan_source and plan_source[0]:  # from_alert = 1
                print("这个维保计划来自维保提醒，正在更新相关的旧维保记录...")

                # 查找并更新相关的旧维保记录，将其标记为已处理
                # 找到同一零备件、区域、设备的所有旧维保记录（除了刚创建的）
                cursor.execute("""
                    UPDATE maintenance_records
                    SET maintenance_status = 'replaced_by_plan',
                        notes = COALESCE(notes, '') || ' [已通过维保计划更新]'
                    WHERE spare_part_id = ? AND area_id = ? AND equipment_id = ?
                    AND maintenance_date < ?
                    AND maintenance_status != 'replaced_by_plan'
                    AND id != (SELECT MAX(id) FROM maintenance_records
                              WHERE spare_part_id = ? AND area_id = ? AND equipment_id = ?)
                """, (plan['spare_part_id'], plan['area_id'], plan['equipment_id'], maintenance_date,
                      plan['spare_part_id'], plan['area_id'], plan['equipment_id']))

                updated_records = cursor.rowcount
                if updated_records > 0:
                    print(f"已更新 {updated_records} 条相关的维保记录状态")

            conn.commit()
            print(f"维保计划执行成功: {plan['spare_part_name']}, 下次维保: {next_maintenance_date}")
            return True

        except Exception as e:
            print(f"执行维保计划错误: {e}")
            conn.rollback()
            # 重新抛出异常，让API层处理
            raise e
        finally:
            conn.close()

    def check_inventory_for_maintenance(self, spare_part_name: str, quantity_needed: int) -> dict:
        """检查维保所需的库存是否充足"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute("SELECT total_quantity FROM inventory_global WHERE spare_part_name = ?",
                         (spare_part_name,))
            inventory_result = cursor.fetchone()

            if not inventory_result:
                return {
                    'sufficient': False,
                    'message': f"零备件 '{spare_part_name}' 没有库存记录，请先添加库存"
                }

            current_stock = inventory_result[0]
            if current_stock < quantity_needed:
                return {
                    'sufficient': False,
                    'message': f"库存不足！零备件 '{spare_part_name}' 当前库存: {current_stock}，需要: {quantity_needed}"
                }

            return {
                'sufficient': True,
                'message': f"库存充足，当前库存: {current_stock}，需要: {quantity_needed}",
                'current_stock': current_stock,
                'needed': quantity_needed
            }

        finally:
            conn.close()

    def get_spare_part_by_id(self, spare_part_id: int) -> dict:
        """根据ID获取零备件信息"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute("SELECT id, name, maintenance_cycle_months FROM spare_parts WHERE id = ?",
                         (spare_part_id,))
            result = cursor.fetchone()

            if result:
                return {
                    'id': result[0],
                    'name': result[1],
                    'maintenance_cycle_months': result[2]
                }
            else:
                return None

        finally:
            conn.close()

if __name__ == "__main__":
    # 测试维保管理器
    manager = MaintenanceManager()
    overview = manager.get_system_overview()
    print("系统概览:", overview)
