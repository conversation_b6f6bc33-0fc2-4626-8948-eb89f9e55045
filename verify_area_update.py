#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def verify_area_update():
    """验证区域名称是否已更新"""
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        print("=== 验证区域名称更新 ===")
        
        # 检查区域表
        cursor.execute('SELECT id, name, description FROM areas ORDER BY id')
        areas = cursor.fetchall()
        
        expected_areas = [
            (1, '成品区', '成品生产和包装区域'),
            (2, '辅料区', '辅料存储和处理区域'),
            (3, '片烟区', '片烟加工和处理区域')
        ]
        
        print("当前区域数据:")
        for area in areas:
            print(f"  ID: {area[0]}, 名称: {area[1]}, 描述: {area[2]}")
        
        print("\n期望的区域数据:")
        for area in expected_areas:
            print(f"  ID: {area[0]}, 名称: {area[1]}, 描述: {area[2]}")
        
        # 验证是否匹配
        if areas == expected_areas:
            print("\n✅ 区域名称更新成功！")
        else:
            print("\n❌ 区域名称未正确更新")
            
            # 如果不匹配，执行更新
            print("\n正在更新区域名称...")
            for area_id, name, description in expected_areas:
                cursor.execute('''
                    UPDATE areas 
                    SET name = ?, description = ? 
                    WHERE id = ?
                ''', (name, description, area_id))
                print(f"更新区域 {area_id}: {name}")
            
            conn.commit()
            print("✅ 区域名称更新完成！")
        
        # 检查设备分布
        print("\n=== 设备分布 ===")
        cursor.execute('''
            SELECT a.name as area_name, e.name as equipment_name
            FROM areas a
            LEFT JOIN equipment e ON a.id = e.area_id
            ORDER BY a.id, e.id
        ''')
        equipment = cursor.fetchall()
        
        current_area = None
        for eq in equipment:
            if eq[0] != current_area:
                current_area = eq[0]
                print(f"\n{current_area}:")
            if eq[1]:
                print(f"  - {eq[1]}")
        
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        conn.close()

if __name__ == "__main__":
    verify_area_update()
