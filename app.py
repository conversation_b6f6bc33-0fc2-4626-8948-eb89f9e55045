#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备零备件周期管理系统 - Flask Web应用
"""

from flask import Flask, render_template, jsonify, request, make_response, send_from_directory
from flask_cors import CORS
from datetime import datetime
import os

from database import DatabaseManager
from maintenance_manager import MaintenanceManager

# 创建Flask应用
app = Flask(__name__)
app.secret_key = 'maintenance_system_secret_key_2025'
CORS(app)  # 允许跨域请求

# 初始化管理器
db_manager = DatabaseManager()
maintenance_manager = MaintenanceManager(db_manager)

# 调试：检查数据库状态
try:
    import sqlite3
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()

    cursor.execute("SELECT COUNT(*) FROM maintenance_records")
    maintenance_count = cursor.fetchone()[0]
    print(f"🔍 数据库调试: 维保记录数量 = {maintenance_count}")

    if maintenance_count == 0:
        print("⚠️  警告: 没有维保记录，需要创建测试数据")
        # 尝试创建一些基本的测试数据
        from datetime import datetime, timedelta
        import random

        # 检查基础数据
        cursor.execute("SELECT COUNT(*) FROM areas")
        areas_count = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM equipment")
        equipment_count = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM spare_parts")
        parts_count = cursor.fetchone()[0]

        print(f"基础数据: 区域{areas_count}, 设备{equipment_count}, 零备件{parts_count}")

        if areas_count > 0 and equipment_count > 0 and parts_count > 0:
            print("🔧 创建基本维保测试数据...")
            today = datetime.now().date()

            # 创建一些测试维保记录
            cursor.execute("SELECT id FROM areas LIMIT 1")
            area_id = cursor.fetchone()[0]
            cursor.execute("SELECT id FROM equipment LIMIT 1")
            equipment_id = cursor.fetchone()[0]
            cursor.execute("SELECT id FROM spare_parts LIMIT 5")
            spare_parts = cursor.fetchall()

            test_records = []
            for i, (sp_id,) in enumerate(spare_parts):
                if i < 2:  # 逾期
                    next_date = today - timedelta(days=random.randint(5, 20))
                elif i < 4:  # 即将到期
                    next_date = today + timedelta(days=random.randint(1, 30))
                else:  # 正常
                    next_date = today + timedelta(days=random.randint(31, 180))

                maint_date = next_date - timedelta(days=365)

                test_records.append((
                    sp_id, area_id, equipment_id,
                    maint_date.isoformat(), next_date.isoformat(),
                    '系统管理员', f'自动生成测试记录{i+1}', 1
                ))

            cursor.executemany("""
                INSERT INTO maintenance_records
                (spare_part_id, area_id, equipment_id, maintenance_date, next_maintenance_date,
                 technician, notes, quantity_used)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, test_records)

            conn.commit()
            print(f"✅ 创建了 {len(test_records)} 条测试维保记录")

    conn.close()
except Exception as e:
    print(f"❌ 数据库调试出错: {e}")

# 主页路由
@app.route('/')
def index():
    """系统概览页面"""
    response = make_response(render_template('index.html'))
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response

@app.route('/maintenance')
def maintenance():
    """维保管理页面"""
    return render_template('maintenance.html')

@app.route('/inventory')
def inventory():
    """库存管理页面"""
    return render_template('inventory.html')

@app.route('/data')
def data():
    """数据管理页面"""
    return render_template('data.html')

@app.route('/test_api_response.html')
def test_api_response():
    """API测试页面"""
    return send_from_directory('.', 'test_api_response.html')

@app.route('/test')
def test():
    """测试页面"""
    return """
    <html>
    <head><title>测试页面</title></head>
    <body>
        <h1>测试页面 - 当前时间: """ + str(datetime.now()) + """</h1>
        <p>如果您能看到这个页面，说明服务器正在运行最新版本。</p>
        <p><a href="/">返回主页</a></p>
    </body>
    </html>
    """

# API路由
@app.route('/api/overview')
def api_overview():
    """获取系统概览数据"""
    try:
        overview = maintenance_manager.get_system_overview()
        return jsonify({
            'success': True,
            'data': overview
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/maintenance/status')
def api_maintenance_status():
    """获取维保状态列表（支持分页）"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)

        # 限制每页最大记录数
        per_page = min(per_page, 50)

        result = maintenance_manager.get_maintenance_status(page, per_page)
        return jsonify({
            'success': True,
            'data': result['data'],
            'pagination': result['pagination']
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/maintenance/records')
def api_maintenance_records():
    """获取维保记录列表（按维保时间降序排序）"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)

        # 限制每页最大记录数
        per_page = min(per_page, 50)

        result = maintenance_manager.get_maintenance_records_list(page, per_page)
        return jsonify({
            'success': True,
            'data': result['data'],
            'pagination': result['pagination']
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/maintenance/alerts')
def api_maintenance_alerts():
    """获取维保提醒"""
    try:
        days_ahead = request.args.get('days', 30, type=int)
        alerts = maintenance_manager.get_maintenance_alerts(days_ahead)
        return jsonify({
            'success': True,
            'data': alerts
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/maintenance/record', methods=['POST'])
def api_add_maintenance_record():
    """添加维保记录"""
    try:
        data = request.get_json()
        spare_part_id = data.get('spare_part_id')
        area_id = data.get('area_id')
        equipment_id = data.get('equipment_id')
        maintenance_date = data.get('maintenance_date')
        technician = data.get('technician', '')
        notes = data.get('notes', '')
        quantity_used = data.get('quantity_used', 1)

        if not spare_part_id or not area_id or not equipment_id or not maintenance_date:
            return jsonify({
                'success': False,
                'error': '缺少必要参数'
            }), 400

        # 先检查库存状态，提供更详细的错误信息
        try:
            # 获取零备件名称
            spare_part = maintenance_manager.get_spare_part_by_id(spare_part_id)
            if not spare_part:
                return jsonify({
                    'success': False,
                    'error': '零备件不存在'
                }), 400

            # 检查库存
            inventory_status = maintenance_manager.check_inventory_for_maintenance(
                spare_part['name'], quantity_used
            )

            if not inventory_status['sufficient']:
                return jsonify({
                    'success': False,
                    'error': inventory_status['message']
                }), 400

            success = maintenance_manager.add_maintenance_record(
                spare_part_id, area_id, equipment_id, maintenance_date,
                technician, notes, quantity_used
            )

            if success:
                return jsonify({
                    'success': True,
                    'message': '维保记录添加成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '添加维保记录失败'
                }), 500

        except Exception as maintenance_error:
            error_message = str(maintenance_error)
            return jsonify({
                'success': False,
                'error': error_message
            }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'请求处理失败: {str(e)}'
        }), 500

@app.route('/api/inventory/status')
def api_inventory_status():
    """获取库存状态 - 使用全局统一库存"""
    try:
        inventory_list = maintenance_manager.get_inventory_status()
        return jsonify({
            'success': True,
            'data': inventory_list
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/inventory/update', methods=['POST'])
def api_update_inventory():
    """更新全局库存"""
    try:
        data = request.get_json()
        spare_part_name = data.get('spare_part_name')
        quantity = data.get('quantity')
        location = data.get('location', '')

        if not spare_part_name or quantity is None:
            return jsonify({
                'success': False,
                'error': '缺少必要参数'
            }), 400

        success = maintenance_manager.update_global_inventory(spare_part_name, quantity, location)

        if success:
            return jsonify({
                'success': True,
                'message': '全局库存更新成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '库存更新失败'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/maintenance/plans')
def api_maintenance_plans():
    """获取维保计划列表"""
    try:
        status = request.args.get('status', 'pending')
        plans = maintenance_manager.get_maintenance_plans(status)
        return jsonify({
            'success': True,
            'data': plans
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/maintenance/plan', methods=['POST'])
def api_create_maintenance_plan():
    """创建维保计划"""
    try:
        data = request.get_json()
        spare_part_id = data.get('spare_part_id')
        area_id = data.get('area_id')
        equipment_id = data.get('equipment_id')
        planned_date = data.get('planned_date')
        notes = data.get('notes', '')
        from_alert = data.get('from_alert', False)
        alert_source = data.get('alert_source', None)

        if not spare_part_id or not area_id or not equipment_id or not planned_date:
            return jsonify({
                'success': False,
                'error': '缺少必要参数'
            }), 400

        success = maintenance_manager.create_maintenance_plan(
            spare_part_id, area_id, equipment_id, planned_date, notes, from_alert, alert_source
        )

        if success:
            return jsonify({
                'success': True,
                'message': '维保计划创建成功',
                'from_alert': from_alert
            })
        else:
            return jsonify({
                'success': False,
                'error': '维保计划创建失败'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/maintenance/plan/<int:plan_id>/execute', methods=['POST'])
def api_execute_maintenance_plan(plan_id):
    """执行维保计划 - 将计划转为维保记录"""
    try:
        data = request.get_json()
        maintenance_date = data.get('maintenance_date')
        technician = data.get('technician', '')
        notes = data.get('notes', '')
        quantity_used = data.get('quantity_used', 1)

        if not maintenance_date:
            return jsonify({
                'success': False,
                'error': '缺少维保日期'
            }), 400

        try:
            success = maintenance_manager.execute_maintenance_plan(
                plan_id, maintenance_date, technician, notes, quantity_used
            )

            if success:
                return jsonify({
                    'success': True,
                    'message': '维保计划执行成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '维保计划执行失败'
                }), 500

        except Exception as maintenance_error:
            # 捕获维保执行过程中的具体错误（如库存不足）
            error_message = str(maintenance_error)
            return jsonify({
                'success': False,
                'error': error_message
            }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'请求处理失败: {str(e)}'
        }), 500

@app.route('/api/maintenance/plan/<int:plan_id>')
def api_get_maintenance_plan(plan_id):
    """获取单个维保计划详情"""
    try:
        plan = maintenance_manager.get_maintenance_plan_by_id(plan_id)

        if plan:
            return jsonify({
                'success': True,
                'data': plan
            })
        else:
            return jsonify({
                'success': False,
                'error': '维保计划不存在'
            }), 404

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500



# ==================== 区域管理 CRUD API ====================
@app.route('/api/areas', methods=['POST'])
def api_create_area():
    """创建新区域"""
    try:
        data = request.get_json()
        name = data.get('name')
        description = data.get('description', '')

        if not name:
            return jsonify({
                'success': False,
                'error': '区域名称不能为空'
            }), 400

        conn = db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute("INSERT INTO areas (name, description) VALUES (?, ?)", (name, description))
        conn.commit()
        area_id = cursor.lastrowid
        conn.close()

        return jsonify({
            'success': True,
            'message': '区域创建成功',
            'data': {'id': area_id, 'name': name, 'description': description}
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/areas/<int:area_id>', methods=['PUT'])
def api_update_area(area_id):
    """更新区域信息"""
    try:
        data = request.get_json()
        name = data.get('name')
        description = data.get('description', '')

        if not name:
            return jsonify({
                'success': False,
                'error': '区域名称不能为空'
            }), 400

        conn = db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute("UPDATE areas SET name = ?, description = ? WHERE id = ?",
                      (name, description, area_id))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({
                'success': False,
                'error': '区域不存在'
            }), 404

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': '区域更新成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/areas/<int:area_id>', methods=['DELETE'])
def api_delete_area(area_id):
    """删除区域"""
    try:
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        # 检查是否有关联的设备
        cursor.execute("SELECT COUNT(*) FROM equipment WHERE area_id = ?", (area_id,))
        equipment_count = cursor.fetchone()[0]

        if equipment_count > 0:
            conn.close()
            return jsonify({
                'success': False,
                'error': f'无法删除区域，该区域下还有 {equipment_count} 个设备'
            }), 400

        cursor.execute("DELETE FROM areas WHERE id = ?", (area_id,))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({
                'success': False,
                'error': '区域不存在'
            }), 404

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': '区域删除成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ==================== 设备管理 CRUD API ====================
@app.route('/api/equipment', methods=['POST'])
def api_create_equipment():
    """创建新设备"""
    try:
        data = request.get_json()
        area_id = data.get('area_id')
        name = data.get('name')
        equipment_type = data.get('type')
        description = data.get('description', '')

        if not all([area_id, name, equipment_type]):
            return jsonify({
                'success': False,
                'error': '区域、设备名称和类型不能为空'
            }), 400

        conn = db_manager.get_connection()
        cursor = conn.cursor()

        # 检查区域是否存在
        cursor.execute("SELECT id FROM areas WHERE id = ?", (area_id,))
        if not cursor.fetchone():
            conn.close()
            return jsonify({
                'success': False,
                'error': '指定的区域不存在'
            }), 400

        cursor.execute("INSERT INTO equipment (area_id, name, type, description) VALUES (?, ?, ?, ?)",
                      (area_id, name, equipment_type, description))
        conn.commit()
        equipment_id = cursor.lastrowid
        conn.close()

        return jsonify({
            'success': True,
            'message': '设备创建成功',
            'data': {'id': equipment_id, 'area_id': area_id, 'name': name, 'type': equipment_type, 'description': description}
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/equipment/<int:equipment_id>', methods=['PUT'])
def api_update_equipment(equipment_id):
    """更新设备信息"""
    try:
        data = request.get_json()
        area_id = data.get('area_id')
        name = data.get('name')
        equipment_type = data.get('type')
        description = data.get('description', '')

        if not all([area_id, name, equipment_type]):
            return jsonify({
                'success': False,
                'error': '区域、设备名称和类型不能为空'
            }), 400

        conn = db_manager.get_connection()
        cursor = conn.cursor()

        # 检查区域是否存在
        cursor.execute("SELECT id FROM areas WHERE id = ?", (area_id,))
        if not cursor.fetchone():
            conn.close()
            return jsonify({
                'success': False,
                'error': '指定的区域不存在'
            }), 400

        cursor.execute("UPDATE equipment SET area_id = ?, name = ?, type = ?, description = ? WHERE id = ?",
                      (area_id, name, equipment_type, description, equipment_id))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({
                'success': False,
                'error': '设备不存在'
            }), 404

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': '设备更新成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/equipment/<int:equipment_id>', methods=['DELETE'])
def api_delete_equipment(equipment_id):
    """删除设备"""
    try:
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        # 检查是否有关联的维保记录
        cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE equipment_id = ?", (equipment_id,))
        maintenance_count = cursor.fetchone()[0]

        if maintenance_count > 0:
            conn.close()
            return jsonify({
                'success': False,
                'error': f'无法删除设备，该设备还有 {maintenance_count} 条维保记录'
            }), 400

        cursor.execute("DELETE FROM equipment WHERE id = ?", (equipment_id,))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({
                'success': False,
                'error': '设备不存在'
            }), 404

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': '设备删除成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ==================== 零备件管理 CRUD API ====================
@app.route('/api/spare-parts', methods=['POST'])
def api_create_spare_part():
    """创建新零备件"""
    try:
        data = request.get_json()
        name = data.get('name')
        maintenance_cycle_months = data.get('maintenance_cycle_months')
        description = data.get('description', '')

        if not all([name, maintenance_cycle_months]):
            return jsonify({
                'success': False,
                'error': '零备件名称和维保周期不能为空'
            }), 400

        try:
            maintenance_cycle_months = int(maintenance_cycle_months)
            if maintenance_cycle_months <= 0:
                raise ValueError()
        except ValueError:
            return jsonify({
                'success': False,
                'error': '维保周期必须是正整数'
            }), 400

        conn = db_manager.get_connection()
        cursor = conn.cursor()

        # 检查是否已有同名零备件
        cursor.execute("SELECT id FROM spare_parts WHERE name = ?", (name,))
        if cursor.fetchone():
            conn.close()
            return jsonify({
                'success': False,
                'error': '已存在同名零备件'
            }), 400

        cursor.execute("INSERT INTO spare_parts (name, maintenance_cycle_months, description) VALUES (?, ?, ?)",
                      (name, maintenance_cycle_months, description))
        conn.commit()
        spare_part_id = cursor.lastrowid

        # 同时在全局库存中创建记录
        cursor.execute("INSERT OR IGNORE INTO inventory_global (spare_part_name, total_quantity, min_quantity, location) VALUES (?, 0, 1, '中央仓库')",
                      (name,))
        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': '零备件创建成功',
            'data': {'id': spare_part_id, 'name': name,
                    'maintenance_cycle_months': maintenance_cycle_months, 'description': description}
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/spare-parts/<int:spare_part_id>', methods=['PUT'])
def api_update_spare_part(spare_part_id):
    """更新零备件信息"""
    try:
        data = request.get_json()
        name = data.get('name')
        maintenance_cycle_months = data.get('maintenance_cycle_months')
        description = data.get('description', '')

        if not all([name, maintenance_cycle_months]):
            return jsonify({
                'success': False,
                'error': '零备件名称和维保周期不能为空'
            }), 400

        try:
            maintenance_cycle_months = int(maintenance_cycle_months)
            if maintenance_cycle_months <= 0:
                raise ValueError()
        except ValueError:
            return jsonify({
                'success': False,
                'error': '维保周期必须是正整数'
            }), 400

        conn = db_manager.get_connection()
        cursor = conn.cursor()

        # 获取原来的零备件名称
        cursor.execute("SELECT name FROM spare_parts WHERE id = ?", (spare_part_id,))
        old_result = cursor.fetchone()
        if not old_result:
            conn.close()
            return jsonify({
                'success': False,
                'error': '零备件不存在'
            }), 404

        old_name = old_result[0]

        # 检查是否有其他零备件使用相同名称
        cursor.execute("SELECT id FROM spare_parts WHERE name = ? AND id != ?", (name, spare_part_id))
        if cursor.fetchone():
            conn.close()
            return jsonify({
                'success': False,
                'error': '已存在同名零备件'
            }), 400

        cursor.execute("UPDATE spare_parts SET name = ?, maintenance_cycle_months = ?, description = ? WHERE id = ?",
                      (name, maintenance_cycle_months, description, spare_part_id))

        # 如果名称改变了，更新全局库存表
        if old_name != name:
            cursor.execute("UPDATE inventory_global SET spare_part_name = ? WHERE spare_part_name = ?",
                          (name, old_name))

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': '零备件更新成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/spare-parts/<int:spare_part_id>', methods=['DELETE'])
def api_delete_spare_part(spare_part_id):
    """删除零备件"""
    try:
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        # 获取零备件名称
        cursor.execute("SELECT name FROM spare_parts WHERE id = ?", (spare_part_id,))
        result = cursor.fetchone()
        if not result:
            conn.close()
            return jsonify({
                'success': False,
                'error': '零备件不存在'
            }), 404

        spare_part_name = result[0]

        # 检查是否有关联的维保记录
        cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE spare_part_id = ?", (spare_part_id,))
        maintenance_count = cursor.fetchone()[0]

        # 检查是否有关联的维保计划
        cursor.execute("SELECT COUNT(*) FROM maintenance_plans WHERE spare_part_id = ?", (spare_part_id,))
        plan_count = cursor.fetchone()[0]

        if maintenance_count > 0 or plan_count > 0:
            conn.close()
            return jsonify({
                'success': False,
                'error': f'无法删除零备件，该零备件有 {maintenance_count} 条维保记录和 {plan_count} 个维保计划'
            }), 400

        # 删除零备件
        cursor.execute("DELETE FROM spare_parts WHERE id = ?", (spare_part_id,))

        # 删除对应的全局库存记录
        cursor.execute("DELETE FROM inventory_global WHERE spare_part_name = ?", (spare_part_name,))

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': '零备件删除成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/debug/db-status')
def api_debug_db_status():
    """调试：检查数据库状态"""
    try:
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        # 检查各表数据量
        tables = ['areas', 'equipment', 'spare_parts', 'inventory', 'maintenance_records']
        table_counts = {}

        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            table_counts[table] = cursor.fetchone()[0]

        # 检查维保记录状态分布
        cursor.execute("""
            SELECT
                COUNT(*) as total,
                SUM(CASE WHEN DATE(next_maintenance_date) < DATE('now') THEN 1 ELSE 0 END) as overdue,
                SUM(CASE WHEN DATE(next_maintenance_date) <= DATE('now', '+30 days') AND DATE(next_maintenance_date) >= DATE('now') THEN 1 ELSE 0 END) as upcoming,
                SUM(CASE WHEN DATE(next_maintenance_date) > DATE('now', '+30 days') THEN 1 ELSE 0 END) as normal
            FROM maintenance_records
        """)

        maintenance_stats = cursor.fetchone()

        conn.close()

        return jsonify({
            'success': True,
            'data': {
                'table_counts': table_counts,
                'maintenance_stats': {
                    'total': maintenance_stats[0] if maintenance_stats else 0,
                    'overdue': maintenance_stats[1] if maintenance_stats else 0,
                    'upcoming': maintenance_stats[2] if maintenance_stats else 0,
                    'normal': maintenance_stats[3] if maintenance_stats else 0
                }
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500




@app.route('/api/areas')
def api_areas():
    """获取区域信息"""
    try:
        areas = db_manager.get_areas()
        return jsonify({
            'success': True,
            'data': areas
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/equipment/<int:area_id>')
def api_equipment(area_id):
    """获取指定区域的设备"""
    try:
        equipment = db_manager.get_equipment_by_area(area_id)
        return jsonify({
            'success': True,
            'data': equipment
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/spare-parts')
def api_spare_parts():
    """获取所有零备件"""
    try:
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT id, name, maintenance_cycle_months, description FROM spare_parts ORDER BY name")
        spare_parts = [dict(row) for row in cursor.fetchall()]
        conn.close()

        return jsonify({
            'success': True,
            'data': spare_parts
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/statistics')
def api_statistics():
    """获取统计数据"""
    try:
        # 获取区域统计
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        # 按区域统计维保记录数量
        cursor.execute("""
            SELECT a.name, COUNT(mr.id) as maintenance_count
            FROM areas a
            LEFT JOIN equipment e ON a.id = e.area_id
            LEFT JOIN maintenance_records mr ON e.id = mr.equipment_id
            GROUP BY a.id, a.name
            ORDER BY a.name
        """)
        area_stats = [{'area': row[0], 'count': row[1]} for row in cursor.fetchall()]

        # 使用统一的状态计算逻辑
        # 获取所有维保记录并计算状态（过滤被替代的记录）
        cursor.execute("""
            SELECT mr.id, mr.next_maintenance_date, sp.maintenance_cycle_months
            FROM maintenance_records mr
            JOIN spare_parts sp ON mr.spare_part_id = sp.id
            WHERE mr.next_maintenance_date IS NOT NULL
            AND (mr.maintenance_status IS NULL OR mr.maintenance_status != 'replaced_by_plan')
        """)

        records = cursor.fetchall()
        status_count = {'overdue': 0, 'warning': 0, 'normal': 0}

        for record in records:
            _, next_date, cycle_months = record
            status = maintenance_manager.calculate_maintenance_status(next_date, cycle_months)
            status_count[status] = status_count.get(status, 0) + 1

        # 转换为API格式
        status_stats = [{'status': status, 'count': count} for status, count in status_count.items() if count > 0]

        conn.close()

        return jsonify({
            'success': True,
            'data': {
                'area_stats': area_stats,
                'status_stats': status_stats
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 错误处理
@app.errorhandler(404)
def not_found(_error):
    """处理404错误"""
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(_error):
    """处理500错误"""
    return render_template('500.html'), 500

if __name__ == '__main__':
    # 确保模板目录存在
    if not os.path.exists('templates'):
        os.makedirs('templates')
    if not os.path.exists('static'):
        os.makedirs('static')
        os.makedirs('static/css')
        os.makedirs('static/js')
        os.makedirs('static/images')
    
    print("设备零备件周期管理系统启动中...")
    print("访问地址: http://127.0.0.1:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
