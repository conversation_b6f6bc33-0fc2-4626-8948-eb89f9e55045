{% extends "base.html" %}

{% block title %}系统概览 - 设备零备件周期管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-tachometer-alt me-2"></i>系统概览</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary" onclick="refreshData()">
            <i class="fas fa-sync-alt me-1"></i>刷新数据
        </button>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="stat-card primary">
            <i class="fas fa-cogs fa-2x mb-3"></i>
            <h3 id="total-parts">-</h3>
            <p>零备件总数</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="stat-card danger">
            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
            <h3 id="overdue-maintenance">-</h3>
            <p>逾期维保</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="stat-card warning">
            <i class="fas fa-clock fa-2x mb-3"></i>
            <h3 id="upcoming-maintenance">-</h3>
            <p>即将到期</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="stat-card info">
            <i class="fas fa-boxes fa-2x mb-3"></i>
            <h3 id="low-stock">-</h3>
            <p>库存不足</p>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>维保进度概览</h5>
                <div class="d-flex gap-2">
                    <select class="form-select form-select-sm" id="areaFilterChart" onchange="filterChartByArea()" style="width: 150px;">
                        <option value="">请选择区域</option>
                    </select>
                    <select class="form-select form-select-sm" id="equipmentFilterChart" onchange="filterChartByEquipment()" style="width: 150px;">
                        <option value="">请选择设备</option>
                    </select>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="maintenanceProgressChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-pie-chart me-2"></i>区域分布</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="areaDistributionChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 维保状态表格 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i>维保状态详情</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="maintenanceTable">
                <thead>
                    <tr>
                        <th>区域</th>
                        <th>设备</th>
                        <th>零备件</th>
                        <th>维保周期</th>
                        <th>维保进度</th>
                        <th>剩余天数</th>
                        <th>库存状态</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody id="maintenanceTableBody">
                    <tr>
                        <td colspan="8" class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分页控件 -->
        <div class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
                显示第 <span id="maintenancePageStart">0</span> - <span id="maintenancePageEnd">0</span> 条，共 <span id="maintenanceTotal">0</span> 条记录
            </div>
            <nav aria-label="维保状态分页">
                <ul class="pagination pagination-sm mb-0" id="maintenancePagination">
                    <!-- 分页按钮将通过JavaScript生成 -->
                </ul>
            </nav>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let maintenanceProgressChart = null;
let areaDistributionChart = null;
let allMaintenanceData = [];
let areasData = [];

// 维保状态表格分页变量
let maintenanceCurrentPage = 1;
let maintenanceItemsPerPage = 15;
let maintenanceSortedData = [];

// 区域颜色配置
const areaColors = {
    '成品区': '#3498db',  // 蓝色
    '辅料区': '#27ae60',  // 绿色
    '片烟区': '#f39c12'   // 橙色（不用红色）
};

// 页面加载完成后初始化
$(document).ready(function() {
    loadOverviewData();
    loadMaintenanceStatus();
    loadStatistics();
    loadAreasForFilter();

    // 设置定期刷新（每30秒刷新一次统计数据）
    setInterval(function() {
        loadOverviewData();
    }, 30000);

    // 监听localStorage变化，当维保数据更新时自动刷新
    let lastUpdateTime = localStorage.getItem('maintenanceDataUpdated');
    setInterval(function() {
        const currentUpdateTime = localStorage.getItem('maintenanceDataUpdated');
        if (currentUpdateTime && currentUpdateTime !== lastUpdateTime) {
            lastUpdateTime = currentUpdateTime;
            console.log('检测到维保数据更新，自动刷新页面数据');
            loadOverviewData();
            loadMaintenanceStatus();
        }
    }, 1000); // 每秒检查一次
});

// 加载概览数据
function loadOverviewData() {
    $.get('/api/overview')
        .done(function(response) {
            if (response.success) {
                const data = response.data;
                $('#total-parts').text(data.total_parts || 0);
                $('#overdue-maintenance').text(data.overdue_maintenance || 0);
                $('#upcoming-maintenance').text(data.upcoming_maintenance || 0);
                $('#low-stock').text(data.low_stock || 0);
            }
        })
        .fail(function() {
            console.error('加载概览数据失败');
        });
}

// 加载维保状态
function loadMaintenanceStatus() {
    $.get('/api/maintenance/status?per_page=50')
        .done(function(response) {
            if (response.success) {
                allMaintenanceData = response.data;
                updateMaintenanceTable(response.data);
                updateMaintenanceProgressChart(response.data);
            }
        })
        .fail(function() {
            console.error('加载维保状态失败');
            $('#maintenanceTableBody').html('<tr><td colspan="8" class="text-center text-danger">加载失败</td></tr>');
        });
}

// 加载区域数据用于筛选
function loadAreasForFilter() {
    $.get('/api/areas')
        .done(function(response) {
            if (response.success) {
                areasData = response.data;
                updateAreaFilterOptions(response.data);
            }
        })
        .fail(function() {
            console.error('加载区域数据失败');
        });
}

// 更新区域筛选选项
function updateAreaFilterOptions(areas) {
    const select = $('#areaFilterChart');
    select.empty().append('<option value="">请选择区域</option>');

    areas.forEach(function(area) {
        select.append(`<option value="${area.name}">${area.name}</option>`);
    });
}

// 根据区域筛选图表
function filterChartByArea() {
    const selectedArea = $('#areaFilterChart').val();

    // 更新设备筛选选项
    updateEquipmentFilterOptions(selectedArea);

    // 筛选数据并更新图表
    filterAndUpdateChart();
}

// 更新设备筛选选项
function updateEquipmentFilterOptions(selectedArea) {
    const select = $('#equipmentFilterChart');
    select.empty().append('<option value="">请选择设备</option>');

    if (!selectedArea) {
        return;
    }

    // 获取选中区域的所有设备
    const equipmentSet = new Set();
    allMaintenanceData.forEach(function(item) {
        if (item.area_name === selectedArea) {
            equipmentSet.add(item.equipment_name);
        }
    });

    Array.from(equipmentSet).sort().forEach(function(equipment) {
        select.append(`<option value="${equipment}">${equipment}</option>`);
    });
}

// 根据设备筛选图表
function filterChartByEquipment() {
    filterAndUpdateChart();
}

// 筛选数据并更新图表和表格
function filterAndUpdateChart() {
    const selectedArea = $('#areaFilterChart').val();
    const selectedEquipment = $('#equipmentFilterChart').val();

    let filteredData = allMaintenanceData;

    // 按区域筛选
    if (selectedArea) {
        filteredData = filteredData.filter(item => item.area_name === selectedArea);
    }

    // 按设备筛选
    if (selectedEquipment) {
        filteredData = filteredData.filter(item => item.equipment_name === selectedEquipment);
    }

    // 更新图表
    updateMaintenanceProgressChart(filteredData);

    // 更新表格（重置到第一页）
    maintenanceCurrentPage = 1;
    updateMaintenanceTable(filteredData);
}

// 更新维保状态表格
function updateMaintenanceTable(data) {
    if (data.length === 0) {
        $('#maintenanceTableBody').html('<tr><td colspan="8" class="text-center">暂无数据</td></tr>');
        updateMaintenancePaginationInfo(0, 0, 0);
        $('#maintenancePagination').empty();
        return;
    }

    // 按紧急程度排序：逾期越久越优先（保持原有排序规则）
    maintenanceSortedData = data.slice().sort(function(a, b) {
        // 直接按剩余天数排序：负数越小越优先（-60 > -53），正数越小越优先（20 > 30）
        const aDays = a.days_remaining !== null ? a.days_remaining : 999;
        const bDays = b.days_remaining !== null ? b.days_remaining : 999;

        return aDays - bDays;
    });

    // 渲染当前页数据
    renderMaintenanceTablePage();

    // 更新分页控件
    updateMaintenancePagination();
}

// 渲染维保状态表格当前页
function renderMaintenanceTablePage() {
    const tbody = $('#maintenanceTableBody');
    tbody.empty();

    const totalItems = maintenanceSortedData.length;
    const startIndex = (maintenanceCurrentPage - 1) * maintenanceItemsPerPage;
    const endIndex = Math.min(startIndex + maintenanceItemsPerPage, totalItems);

    const pageData = maintenanceSortedData.slice(startIndex, endIndex);

    pageData.forEach(function(item) {
        const statusBadge = getStatusBadge(item.status, item.status_text);
        const stockBadge = getStockBadge(item.stock_status, item.stock_text);
        const progressBar = getProgressBar(item.progress, item.status);
        const daysRemaining = item.days_remaining !== null ? item.days_remaining + '天' : '-';

        const row = `
            <tr>
                <td>${item.area_name || '-'}</td>
                <td>${item.equipment_name || '-'}</td>
                <td>${item.spare_part_name || '-'}</td>
                <td>${item.maintenance_cycle_months || '-'}个月</td>
                <td>${progressBar}</td>
                <td>${daysRemaining}</td>
                <td>${stockBadge}</td>
                <td>${statusBadge}</td>
            </tr>
        `;
        tbody.append(row);
    });

    // 更新分页信息
    updateMaintenancePaginationInfo(startIndex + 1, endIndex, totalItems);
}

// 获取状态徽章
function getStatusBadge(status, text) {
    const badgeClass = {
        'overdue': 'bg-danger',
        'warning': 'bg-warning',
        'normal': 'bg-success',
        'no_record': 'bg-secondary'
    }[status] || 'bg-secondary';
    
    return `<span class="badge ${badgeClass}">${text}</span>`;
}

// 获取库存徽章
function getStockBadge(status, text) {
    const badgeClass = {
        'out_of_stock': 'bg-danger',
        'low_stock': 'bg-warning',
        'sufficient': 'bg-success'
    }[status] || 'bg-secondary';
    
    return `<span class="badge ${badgeClass}">${text}</span>`;
}

// 获取进度条
function getProgressBar(progress, status) {
    const progressClass = {
        'overdue': 'bg-danger',
        'warning': 'bg-warning',
        'normal': 'bg-success',
        'no_record': 'bg-secondary'
    }[status] || 'bg-secondary';

    // 当进度小于12%时，将百分比显示在进度条外部
    const showTextInside = progress >= 12;

    if (showTextInside) {
        return `
            <div class="progress" style="height: 22px; position: relative;">
                <div class="progress-bar ${progressClass}" role="progressbar"
                     style="width: ${progress}%" aria-valuenow="${progress}"
                     aria-valuemin="0" aria-valuemax="100">
                    <span style="position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%);
                                 font-size: 11px; font-weight: bold; color: white; text-shadow: 0 1px 2px rgba(0,0,0,0.3);">
                        ${progress}%
                    </span>
                </div>
            </div>
        `;
    } else {
        return `
            <div class="progress-container" style="display: flex; align-items: center; gap: 8px;">
                <div class="progress" style="height: 22px; flex: 1; min-width: 80px;">
                    <div class="progress-bar ${progressClass}" role="progressbar"
                         style="width: ${Math.max(progress, 3)}%" aria-valuenow="${progress}"
                         aria-valuemin="0" aria-valuemax="100">
                    </div>
                </div>
                <span class="progress-text" style="font-size: 11px; font-weight: bold; color: #495057;
                                                   min-width: 30px; text-align: left;">
                    ${progress}%
                </span>
            </div>
        `;
    }
}

// 更新维保进度图表
function updateMaintenanceProgressChart(data) {
    const canvas = document.getElementById('maintenanceProgressChart');
    if (!canvas) {
        return;
    }

    const ctx = canvas.getContext('2d');
    if (!ctx) {
        return;
    }

    if (maintenanceProgressChart) {
        maintenanceProgressChart.destroy();
    }

    // 检查数据有效性
    if (!data || !Array.isArray(data) || data.length === 0) {
        // 显示空数据图表
        maintenanceProgressChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['暂无数据'],
                datasets: [{
                    label: '维保进度 (%)',
                    data: [0],
                    backgroundColor: ['#e0e0e0'],
                    borderColor: ['#bdbdbd'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false }
                }
            }
        });
        return;
    }

    // 调试：打印数据结构
    console.log('第一条记录:', data[0]);
    console.log('第一条记录的字段:');
    if (data[0]) {
        Object.keys(data[0]).forEach(key => {
            console.log(`  ${key}: ${data[0][key]}`);
        });
    }

    // 按紧急程度排序：逾期越久越优先（与表格排序逻辑一致）
    const sortedData = data.slice().sort(function(a, b) {
        // 直接按剩余天数排序：负数越小越优先（-60 > -53），正数越小越优先（20 > 30）
        const aDays = a.days_remaining !== null ? a.days_remaining : 999;
        const bDays = b.days_remaining !== null ? b.days_remaining : 999;

        return aDays - bDays;
    });

    // 准备图表数据 - 显示格式：区域-设备-零件，取前10条最紧急的
    const chartData = sortedData.slice(0, 10);
    const labels = chartData.map(item => `${item.area_name || '未知区域'}-${item.equipment_name || '未知设备'}-${item.spare_part_name || '未知零件'}`);
    const progressData = chartData.map(item => {
        const progress = item.progress || 0;
        console.log(`进度数据: ${item.spare_part_name} = ${progress}`);
        return progress;
    });

    // 根据维保状态分配颜色
    const backgroundColors = chartData.map(item => {
        const progress = item.progress;
        const daysRemaining = item.days_remaining;
        let color;

        if (progress > 100) {
            color = '#e74c3c'; // 红色 - 逾期
        } else if (daysRemaining !== null && daysRemaining <= 30) {
            color = '#f39c12'; // 橙色 - 即将到期（剩余天数≤30天）
        } else {
            color = '#27ae60'; // 绿色 - 正常（剩余天数>30天）
        }

        console.log(`维保状态颜色: ${item.spare_part_name} 进度${progress}% 剩余${daysRemaining}天 = ${color}`);
        return color;
    });

    console.log('创建图表，标签:', labels);
    console.log('创建图表，数据:', progressData);
    console.log('创建图表，颜色:', backgroundColors);

    try {
        maintenanceProgressChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: '维保进度 (%)',
                data: progressData,
                backgroundColor: backgroundColors,
                borderColor: backgroundColors,
                borderWidth: 1,
                borderRadius: 8,
                borderSkipped: false,
            }]
        },

        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            layout: {
                padding: {
                    right: 60,
                    left: 5
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        title: function(context) {
                            const item = chartData[context[0].dataIndex];
                            return `${item.area_name} - ${item.equipment_name}`;
                        },
                        label: function(context) {
                            const item = chartData[context.dataIndex];
                            return [
                                `零备件: ${item.spare_part_name}`,
                                `维保进度: ${context.parsed.x}%`,
                                `剩余天数: ${item.days_remaining !== null ? item.days_remaining + '天' : '无记录'}`
                            ];
                        }
                    }
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    max: function(context) {
                        // 动态计算最大值，基于数据的最大值加上一些余量
                        const maxValue = Math.max(...context.chart.data.datasets[0].data);
                        return Math.max(maxValue + 15, 100); // 至少显示到100%，如果有超过100%的数据则显示更多
                    },
                    grid: {
                        display: true,
                        color: '#e9ecef'
                    },
                    ticks: {
                        font: {
                            size: 12,
                            weight: 'bold'
                        },
                        color: '#495057',
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                },
                y: {
                    ticks: {
                        maxRotation: 0,
                        font: {
                            size: 13,
                            weight: 'bold'
                        },
                        color: '#2c3e50',
                        callback: function(value, index) {
                            const label = this.getLabelForValue(value);
                            // 如果标签太长，截断显示
                            if (label.length > 28) {
                                return label.substring(0, 25) + '...';
                            }
                            return label;
                        }
                    }
                }
            },
            animation: {
                onComplete: function() {
                    const chart = this;
                    const ctx = chart.ctx;

                    chart.data.datasets.forEach((dataset, i) => {
                        const meta = chart.getDatasetMeta(i);
                        meta.data.forEach((bar, index) => {
                            const data = chartData[index];
                            const progress = data.progress;
                            const daysRemaining = data.days_remaining;

                            // 计算文本位置
                            const barEnd = bar.x;
                            const barY = bar.y;
                            const chartArea = chart.chartArea;

                            // 计算文本位置，更紧凑的布局
                            const textX = barEnd + 5;

                            // 获取当前条形图的颜色
                            const barColor = chart.data.datasets[0].backgroundColor[index];

                            // 显示百分比
                            ctx.font = 'bold 14px Arial';
                            ctx.fillStyle = barColor; // 使用与条形图相同的颜色
                            ctx.textAlign = 'left';
                            ctx.textBaseline = 'middle';
                            ctx.fillText(`${progress}%`, textX, barY - 8);

                            // 显示剩余天数
                            ctx.font = 'bold 11px Arial';
                            let daysText;

                            if (daysRemaining === null) {
                                daysText = '无记录';
                            } else if (daysRemaining < 0) {
                                daysText = `逾期${Math.abs(daysRemaining)}天`;
                            } else {
                                daysText = `剩余${daysRemaining}天`;
                            }

                            ctx.fillStyle = barColor; // 使用与条形图相同的颜色
                            ctx.fillText(daysText, textX, barY + 8);
                        });
                    });
                }
            }
        }
    });

        console.log('维保图表创建成功');
    } catch (error) {
        console.error('创建维保图表时出错:', error);
        console.error('错误堆栈:', error.stack);
    }

    console.log('=== 维保图表更新结束 ===');
}

// 加载统计数据
function loadStatistics() {
    $.get('/api/statistics')
        .done(function(response) {
            if (response.success) {
                updateAreaDistributionChart(response.data.area_stats);
            }
        })
        .fail(function() {
            console.error('加载统计数据失败');
        });
}

// 更新图例样式函数
function updateLegendStyles(chart) {
    const meta = chart.getDatasetMeta(0);
    const legendElement = document.querySelector(`#areaDistributionChart`).parentNode.querySelector('.chartjs-legend');

    if (legendElement) {
        const legendItems = legendElement.querySelectorAll('li');
        legendItems.forEach((item, i) => {
            const isHidden = meta.data[i] && meta.data[i].hidden;

            if (isHidden) {
                item.classList.add('hidden-legend-item');
                item.style.opacity = '0.5';
                item.style.textDecoration = 'line-through';
                item.style.color = '#999999';

                const span = item.querySelector('span');
                if (span) span.style.color = '#999999';
            } else {
                item.classList.remove('hidden-legend-item');
                item.style.opacity = '1';
                item.style.textDecoration = 'none';
                item.style.color = '#333333';

                const span = item.querySelector('span');
                if (span) span.style.color = '#333333';
            }
        });
    }
}

// 更新区域分布图表
function updateAreaDistributionChart(data) {
    const ctx = document.getElementById('areaDistributionChart').getContext('2d');

    if (areaDistributionChart) {
        areaDistributionChart.destroy();
    }

    const labels = data.map(item => item.area);
    const counts = data.map(item => item.count);

    // 使用区域颜色配置
    const colors = labels.map(area => areaColors[area] || '#95a5a6');

    areaDistributionChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: counts,
                backgroundColor: colors,
                borderWidth: 3,
                borderColor: '#fff',
                hoverBorderWidth: 5,
                hoverBorderColor: '#333'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '35%',
            radius: '90%',
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 25,
                        usePointStyle: true,
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        color: '#333',
                        generateLabels: function(chart) {
                            const data = chart.data;
                            if (data.labels.length && data.datasets.length) {
                                const total = data.datasets[0].data.reduce((a, b) => a + b, 0);
                                return data.labels.map((label, i) => {
                                    const value = data.datasets[0].data[i];
                                    const percentage = ((value / total) * 100).toFixed(1);
                                    const meta = chart.getDatasetMeta(0);
                                    const isHidden = meta.data[i].hidden;

                                    return {
                                        text: `${label}: ${value}个 (${percentage}%)`,
                                        fillStyle: isHidden ? '#cccccc' : data.datasets[0].backgroundColor[i],
                                        strokeStyle: data.datasets[0].borderColor,
                                        lineWidth: data.datasets[0].borderWidth,
                                        pointStyle: 'circle',
                                        hidden: isHidden,
                                        index: i,
                                        fontColor: isHidden ? '#999999' : '#333333'
                                    };
                                });
                            }
                            return [];
                        }
                    },
                    onClick: function(e, legendItem, legend) {
                        // 默认的图例点击行为
                        const index = legendItem.index;
                        const chart = legend.chart;
                        const meta = chart.getDatasetMeta(0);

                        // 切换数据可见性
                        meta.data[index].hidden = !meta.data[index].hidden;

                        // 重新生成图例以应用新样式
                        chart.update('none');

                        // 手动更新图例样式
                        setTimeout(() => {
                            updateLegendStyles(chart);
                        }, 50);
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label;
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ${value}个 (${percentage}%)`;
                        }
                    }
                }
            },
            onHover: function(event, elements) {
                event.native.target.style.cursor = elements.length > 0 ? 'pointer' : 'default';
            },
            onClick: function(event, elements) {
                if (elements.length > 0) {
                    const index = elements[0].index;
                    const label = this.data.labels[index];
                    console.log(`点击了区域: ${label}`);
                    // 这里可以添加点击区域后的逻辑，比如筛选数据
                }
            }
        }
    });

    // 添加图例悬停效果
    setTimeout(() => {
        const legendContainer = document.querySelector('#areaDistributionChart').parentElement.querySelector('.chartjs-legend');
        if (legendContainer) {
            const legendItems = legendContainer.querySelectorAll('li');
            legendItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    if (!this.style.textDecoration.includes('line-through')) {
                        this.style.textDecoration = 'underline';
                        this.style.color = '#666';
                    }
                });

                item.addEventListener('mouseleave', function() {
                    if (!this.style.textDecoration.includes('line-through')) {
                        this.style.textDecoration = 'none';
                        this.style.color = '#333';
                    }
                });
            });
        }
    }, 500);
}

// 更新维保状态分页信息
function updateMaintenancePaginationInfo(start, end, total) {
    $('#maintenancePageStart').text(start);
    $('#maintenancePageEnd').text(end);
    $('#maintenanceTotal').text(total);
}

// 更新维保状态分页控件
function updateMaintenancePagination() {
    const totalItems = maintenanceSortedData.length;
    const totalPages = Math.ceil(totalItems / maintenanceItemsPerPage);
    const pagination = $('#maintenancePagination');

    pagination.empty();

    if (totalPages <= 1) {
        return;
    }

    // 上一页按钮
    const prevDisabled = maintenanceCurrentPage === 1 ? 'disabled' : '';
    pagination.append(`
        <li class="page-item ${prevDisabled}">
            <a class="page-link" href="#" onclick="changeMaintenancePage(${maintenanceCurrentPage - 1}); return false;">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `);

    // 页码按钮
    const startPage = Math.max(1, maintenanceCurrentPage - 2);
    const endPage = Math.min(totalPages, maintenanceCurrentPage + 2);

    if (startPage > 1) {
        pagination.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="changeMaintenancePage(1); return false;">1</a>
            </li>
        `);
        if (startPage > 2) {
            pagination.append('<li class="page-item disabled"><span class="page-link">...</span></li>');
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === maintenanceCurrentPage ? 'active' : '';
        pagination.append(`
            <li class="page-item ${activeClass}">
                <a class="page-link" href="#" onclick="changeMaintenancePage(${i}); return false;">${i}</a>
            </li>
        `);
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            pagination.append('<li class="page-item disabled"><span class="page-link">...</span></li>');
        }
        pagination.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="changeMaintenancePage(${totalPages}); return false;">${totalPages}</a>
            </li>
        `);
    }

    // 下一页按钮
    const nextDisabled = maintenanceCurrentPage === totalPages ? 'disabled' : '';
    pagination.append(`
        <li class="page-item ${nextDisabled}">
            <a class="page-link" href="#" onclick="changeMaintenancePage(${maintenanceCurrentPage + 1}); return false;">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `);
}

// 切换维保状态表格页面
function changeMaintenancePage(page) {
    const totalPages = Math.ceil(maintenanceSortedData.length / maintenanceItemsPerPage);

    if (page < 1 || page > totalPages) {
        return;
    }

    maintenanceCurrentPage = page;
    renderMaintenanceTablePage();
    updateMaintenancePagination();
}

// 刷新数据
function refreshData() {
    loadOverviewData();
    loadMaintenanceStatus();
    loadStatistics();
    loadAreasForFilter();

    // 重置筛选器和分页
    $('#areaFilterChart').val('');
    $('#equipmentFilterChart').empty().append('<option value="">请选择设备</option>');
    maintenanceCurrentPage = 1;
}
</script>
{% endblock %}
