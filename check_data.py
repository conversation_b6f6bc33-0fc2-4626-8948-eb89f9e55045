import requests

try:
    # 测试维保状态API
    response = requests.get('http://127.0.0.1:5000/api/maintenance/status')
    if response.status_code == 200:
        data = response.json()
        print(f"维保记录数: {len(data['data'])}")
        
        print("\n前5条记录:")
        for i, item in enumerate(data['data'][:5]):
            notes = item.get('notes', '无备注')
            print(f"{i+1}. {item['spare_part_name']} - {item['area_name']} - {item['status']} - {notes}")
    else:
        print(f"维保API错误: {response.status_code}")
    
    # 测试统计API
    response = requests.get('http://127.0.0.1:5000/api/statistics')
    if response.status_code == 200:
        data = response.json()
        print(f"\n区域分布:")
        for area in data['data']['area_stats']:
            print(f"  {area['area']}: {area['count']} 条")
    else:
        print(f"统计API错误: {response.status_code}")
        
except Exception as e:
    print(f"错误: {e}")
