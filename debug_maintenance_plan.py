#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试维保计划执行功能
"""

import requests
import json
from datetime import datetime

def debug_maintenance_plan():
    """调试维保计划执行功能"""
    print("🔧 调试维保计划执行功能...")
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 1. 获取待执行的维保计划
        print("\n1️⃣ 获取待执行的维保计划...")
        response = requests.get(f"{base_url}/api/maintenance/plans?status=pending")
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code != 200:
            print(f"❌ 获取维保计划失败: {response.status_code}")
            return False
        
        plans_data = response.json()
        if not plans_data.get('success'):
            print(f"❌ 获取维保计划失败: {plans_data.get('error')}")
            return False
        
        plans = plans_data['data']
        if not plans:
            print("⚠️  没有待执行的维保计划")
            return True
        
        print(f"✅ 找到 {len(plans)} 个待执行的维保计划")
        for i, plan in enumerate(plans[:3]):  # 只显示前3个
            print(f"   {i+1}. ID:{plan['id']} - {plan['spare_part_name']} - {plan['area_name']} - {plan['equipment_name']}")
        
        # 选择第一个计划进行测试
        plan_id = plans[0]['id']
        print(f"\n选择计划ID: {plan_id} 进行测试")
        
        # 2. 获取维保计划详情
        print(f"\n2️⃣ 获取维保计划详情...")
        response = requests.get(f"{base_url}/api/maintenance/plan/{plan_id}")
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code != 200:
            print(f"❌ 获取维保计划详情失败: {response.status_code}")
            return False
        
        plan_data = response.json()
        if not plan_data.get('success'):
            print(f"❌ 获取维保计划详情失败: {plan_data.get('error')}")
            return False
        
        plan_detail = plan_data['data']
        print(f"✅ 维保计划详情:")
        print(f"   零备件: {plan_detail['spare_part_name']}")
        print(f"   设备: {plan_detail['area_name']} - {plan_detail['equipment_name']}")
        print(f"   计划日期: {plan_detail['planned_date']}")
        print(f"   维保周期: {plan_detail['maintenance_cycle_months']}个月")
        
        # 3. 检查库存状态
        print(f"\n3️⃣ 检查库存状态...")
        response = requests.get(f"{base_url}/api/inventory/status")
        
        if response.status_code == 200:
            inventory_data = response.json()
            if inventory_data.get('success'):
                inventory_list = inventory_data['data']
                # 查找对应的零备件库存
                spare_part_inventory = None
                for item in inventory_list:
                    if item['spare_part_name'] == plan_detail['spare_part_name']:
                        spare_part_inventory = item
                        break
                
                if spare_part_inventory:
                    print(f"✅ 找到库存信息:")
                    print(f"   零备件: {spare_part_inventory['spare_part_name']}")
                    print(f"   当前库存: {spare_part_inventory['total_quantity']}")
                    print(f"   最小库存: {spare_part_inventory['min_quantity']}")
                    print(f"   库存状态: {spare_part_inventory['status_text']}")
                else:
                    print(f"⚠️  未找到零备件 '{plan_detail['spare_part_name']}' 的库存信息")
        
        # 4. 测试执行维保计划
        print(f"\n4️⃣ 测试执行维保计划...")
        execute_data = {
            "maintenance_date": datetime.now().strftime('%Y-%m-%d'),
            "technician": "调试测试员",
            "quantity_used": 1,
            "notes": "调试维保计划执行功能"
        }
        
        print(f"执行数据: {json.dumps(execute_data, indent=2, ensure_ascii=False)}")
        
        response = requests.post(
            f"{base_url}/api/maintenance/plan/{plan_id}/execute",
            headers={'Content-Type': 'application/json'},
            data=json.dumps(execute_data)
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 维保计划执行成功!")
                print(f"   消息: {result.get('message')}")
            else:
                print(f"❌ 维保计划执行失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   错误信息: {error_data.get('error')}")
            except:
                print(f"   响应内容: {response.text}")
            return False
        
        # 5. 验证结果
        print(f"\n5️⃣ 验证执行结果...")
        
        # 检查维保记录是否创建
        response = requests.get(f"{base_url}/api/maintenance/status")
        if response.status_code == 200:
            records_data = response.json()
            if records_data.get('success'):
                records = records_data['data']
                # 查找刚创建的维保记录
                new_record = None
                for record in records:
                    if (record['spare_part_name'] == plan_detail['spare_part_name'] and 
                        record['technician'] == execute_data['technician']):
                        new_record = record
                        break
                
                if new_record:
                    print("✅ 维保记录已创建")
                    print(f"   维保日期: {new_record['maintenance_date']}")
                    print(f"   下次维保: {new_record['next_maintenance_date']}")
                    print(f"   技术员: {new_record['technician']}")
                else:
                    print("⚠️  未找到对应的维保记录")
        
        # 检查计划状态是否更新
        response = requests.get(f"{base_url}/api/maintenance/plan/{plan_id}")
        if response.status_code == 200:
            updated_plan_data = response.json()
            if updated_plan_data.get('success'):
                updated_plan = updated_plan_data['data']
                print(f"✅ 计划状态已更新: {updated_plan['plan_status']}")
            else:
                print("⚠️  无法获取更新后的计划状态")
        
        print(f"\n🎉 维保计划执行功能调试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 维保计划执行功能调试工具")
    print("=" * 50)
    
    success = debug_maintenance_plan()
    
    if success:
        print("\n✅ 调试完成!")
    else:
        print("\n❌ 调试发现问题")

if __name__ == "__main__":
    main()
