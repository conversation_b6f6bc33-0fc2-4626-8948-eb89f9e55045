#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def check_areas():
    """检查区域数据"""
    
    conn = sqlite3.connect('equipment_maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        print("=== 检查区域数据 ===")
        
        cursor.execute('SELECT id, name, description FROM areas ORDER BY id')
        areas = cursor.fetchall()
        print("区域列表:")
        for area in areas:
            print(f"  {area['id']}: {area['name']} - {area['description']}")
        
        print("\n=== 检查设备分布 ===")
        cursor.execute('''
            SELECT a.name as area_name, e.name as equipment_name, e.description
            FROM areas a
            LEFT JOIN equipment e ON a.id = e.area_id
            ORDER BY a.id, e.id
        ''')
        equipment = cursor.fetchall()
        
        current_area = None
        for eq in equipment:
            if eq['area_name'] != current_area:
                current_area = eq['area_name']
                print(f"\n{current_area}:")
            if eq['equipment_name']:
                print(f"  - {eq['equipment_name']}: {eq['description']}")
        
        print("\n=== 检查零备件 ===")
        cursor.execute('SELECT id, name, description FROM spare_parts ORDER BY id LIMIT 6')
        parts = cursor.fetchall()
        for part in parts:
            print(f"  {part['id']}: {part['name']} - {part['description']}")
        
        print("\n✅ 区域数据检查完成！")
        
    except Exception as e:
        print(f"❌ 检查区域数据时出错: {e}")
        
    finally:
        conn.close()

if __name__ == "__main__":
    check_areas()
