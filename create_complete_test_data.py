#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
根据README.MD设计创建完整的测试数据
包含所有类型的维保状态：正常、即将到期、逾期、库存不足等
"""

import sqlite3
import random
from datetime import datetime, timed<PERSON><PERSON>

def create_complete_test_data():
    """创建完整的测试数据"""
    print("=== 根据README.MD设计创建完整测试数据 ===")
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 清空现有数据
        cursor.execute("DELETE FROM maintenance_records")
        cursor.execute("DELETE FROM inventory")
        cursor.execute("DELETE FROM spare_parts")
        cursor.execute("DELETE FROM equipment")
        cursor.execute("DELETE FROM areas")
        
        # 1. 创建区域数据（根据README.MD）
        print("1. 创建区域数据...")
        areas = [
            ("成品区", "成品区域，包含4台堆垛机和1条入库皮带"),
            ("辅料区", "辅料区域，包含3台堆垛机和1台AGV小车"),
            ("片烟区", "片烟区域，包含3台堆垛机")
        ]
        
        for area_name, area_desc in areas:
            cursor.execute("INSERT INTO areas (name, description) VALUES (?, ?)", 
                         (area_name, area_desc))
        
        # 获取区域ID
        cursor.execute("SELECT id, name FROM areas")
        area_map = {row[1]: row[0] for row in cursor.fetchall()}
        
        # 2. 创建设备数据（根据README.MD）
        print("2. 创建设备数据...")
        equipment_data = [
            # 成品区：4台堆垛机 + 1条入库皮带
            (area_map["成品区"], "堆垛机1", "堆垛机", "成品区第1台堆垛机"),
            (area_map["成品区"], "堆垛机2", "堆垛机", "成品区第2台堆垛机"),
            (area_map["成品区"], "堆垛机3", "堆垛机", "成品区第3台堆垛机"),
            (area_map["成品区"], "堆垛机4", "堆垛机", "成品区第4台堆垛机"),
            (area_map["成品区"], "入库皮带1", "入库皮带", "成品区入库皮带"),
            # 辅料区：3台堆垛机 + 1台AGV小车
            (area_map["辅料区"], "堆垛机1", "堆垛机", "辅料区第1台堆垛机"),
            (area_map["辅料区"], "堆垛机2", "堆垛机", "辅料区第2台堆垛机"),
            (area_map["辅料区"], "堆垛机3", "堆垛机", "辅料区第3台堆垛机"),
            (area_map["辅料区"], "AGV小车1", "AGV小车", "辅料区AGV小车"),
            # 片烟区：3台堆垛机
            (area_map["片烟区"], "堆垛机1", "堆垛机", "片烟区第1台堆垛机"),
            (area_map["片烟区"], "堆垛机2", "堆垛机", "片烟区第2台堆垛机"),
            (area_map["片烟区"], "堆垛机3", "堆垛机", "片烟区第3台堆垛机")
        ]
        
        for eq_data in equipment_data:
            cursor.execute("INSERT INTO equipment (area_id, name, type, description) VALUES (?, ?, ?, ?)", eq_data)
        
        # 获取设备ID
        cursor.execute("SELECT id, name, type, area_id FROM equipment")
        equipment_list = cursor.fetchall()
        
        # 3. 创建零备件数据（根据README.MD设计）
        print("3. 创建零备件数据...")
        spare_parts_data = []
        
        for eq_id, eq_name, eq_type, area_id in equipment_list:
            if eq_type == "堆垛机":
                # 堆垛机：主接触器(6个月)、变频器(12个月)、传感器(3个月)
                spare_parts_data.extend([
                    (eq_id, "主接触器", 6, f"{eq_name}主接触器，6个月维保周期"),
                    (eq_id, "变频器", 12, f"{eq_name}变频器，12个月维保周期"),
                    (eq_id, "传感器", 3, f"{eq_name}传感器，3个月维保周期")
                ])
            elif eq_type == "入库皮带":
                # 入库皮带：皮带(3个月)、滚筒(6个月)
                spare_parts_data.extend([
                    (eq_id, "皮带", 3, f"{eq_name}皮带，3个月维保周期"),
                    (eq_id, "滚筒", 6, f"{eq_name}滚筒，6个月维保周期")
                ])
            elif eq_type == "AGV小车":
                # AGV小车：保险丝(6个月)、电池(12个月)、轮子(9个月)
                spare_parts_data.extend([
                    (eq_id, "保险丝", 6, f"{eq_name}保险丝，6个月维保周期"),
                    (eq_id, "电池", 12, f"{eq_name}电池，12个月维保周期"),
                    (eq_id, "轮子", 9, f"{eq_name}轮子，9个月维保周期")
                ])
        
        for sp_data in spare_parts_data:
            cursor.execute("INSERT INTO spare_parts (equipment_id, name, maintenance_cycle_months, description) VALUES (?, ?, ?, ?)", sp_data)
        
        # 获取零备件ID
        cursor.execute("SELECT id, name, maintenance_cycle_months, equipment_id FROM spare_parts")
        spare_parts_list = cursor.fetchall()
        
        # 4. 创建库存数据
        print("4. 创建库存数据...")
        locations = ["仓库A-01", "仓库A-02", "仓库B-01", "仓库B-02", "仓库C-01"]
        
        for sp_id, sp_name, cycle, eq_id in spare_parts_list:
            # 创建不同的库存状态
            if "传感器" in sp_name or "保险丝" in sp_name:
                # 一些零备件库存不足或缺货
                quantity = random.choice([0, 1, 2])  # 缺货或库存不足
                min_quantity = 3
            else:
                # 大部分零备件库存充足
                quantity = random.randint(5, 20)
                min_quantity = random.randint(2, 5)
            
            location = random.choice(locations)
            
            cursor.execute("""
                INSERT INTO inventory (spare_part_id, quantity, min_quantity, location)
                VALUES (?, ?, ?, ?)
            """, (sp_id, quantity, min_quantity, location))
        
        # 5. 创建维保记录（包含各种状态）
        print("5. 创建维保记录...")
        today = datetime.now().date()
        technicians = ["张师傅", "李师傅", "王师傅", "赵师傅", "刘师傅"]
        
        maintenance_records = []
        
        # 选择一些零备件创建维保记录，确保包含各种状态
        selected_parts = random.sample(spare_parts_list, min(15, len(spare_parts_list)))
        
        for i, (sp_id, sp_name, cycle_months, eq_id) in enumerate(selected_parts):
            # 获取设备和区域信息
            cursor.execute("""
                SELECT e.area_id FROM equipment e WHERE e.id = ?
            """, (eq_id,))
            area_id = cursor.fetchone()[0]
            
            # 创建不同状态的维保记录
            if i < 3:
                # 逾期维保（红色警告）
                days_overdue = random.randint(5, 30)
                maintenance_date = today - timedelta(days=cycle_months * 30 + days_overdue)
                next_maintenance_date = today - timedelta(days=days_overdue)
                notes = f"逾期{days_overdue}天，需要立即维保"
            elif i < 8:
                # 即将到期（黄色提醒，1个月内）
                days_remaining = random.randint(1, 30)
                maintenance_date = today - timedelta(days=cycle_months * 30 - days_remaining)
                next_maintenance_date = today + timedelta(days=days_remaining)
                notes = f"即将到期，还剩{days_remaining}天"
            else:
                # 正常状态（绿色）
                days_remaining = random.randint(31, 180)
                maintenance_date = today - timedelta(days=cycle_months * 30 - days_remaining)
                next_maintenance_date = today + timedelta(days=days_remaining)
                notes = f"状态正常，还剩{days_remaining}天"
            
            maintenance_records.append((
                sp_id, area_id, eq_id,
                maintenance_date.isoformat(),
                next_maintenance_date.isoformat(),
                random.choice(technicians),
                notes,
                random.randint(1, 3)
            ))
        
        # 插入维保记录
        cursor.executemany("""
            INSERT INTO maintenance_records 
            (spare_part_id, area_id, equipment_id, maintenance_date, next_maintenance_date, 
             technician, notes, quantity_used)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, maintenance_records)
        
        conn.commit()
        
        # 6. 统计结果
        print("\n=== 数据创建完成 ===")
        cursor.execute("SELECT COUNT(*) FROM areas")
        areas_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM equipment")
        equipment_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM spare_parts")
        parts_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM inventory")
        inventory_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_records")
        maintenance_count = cursor.fetchone()[0]
        
        print(f"区域数量: {areas_count}")
        print(f"设备数量: {equipment_count}")
        print(f"零备件数量: {parts_count}")
        print(f"库存记录: {inventory_count}")
        print(f"维保记录: {maintenance_count}")
        
        # 统计维保状态
        cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date < ?", (today,))
        overdue = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date BETWEEN ? AND ?", 
                       (today, today + timedelta(days=30)))
        upcoming = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date > ?", 
                       (today + timedelta(days=30),))
        normal = cursor.fetchone()[0]
        
        print(f"\n维保状态分布:")
        print(f"  🔴 逾期维保: {overdue} 条")
        print(f"  🟡 即将到期: {upcoming} 条")
        print(f"  🟢 正常状态: {normal} 条")
        
        # 统计库存状态
        cursor.execute("SELECT COUNT(*) FROM inventory WHERE quantity = 0")
        out_of_stock = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM inventory WHERE quantity > 0 AND quantity <= min_quantity")
        low_stock = cursor.fetchone()[0]
        
        print(f"\n库存状态分布:")
        print(f"  📦 缺货: {out_of_stock} 个零备件")
        print(f"  ⚠️  库存不足: {low_stock} 个零备件")
        
        print("\n✅ 完整测试数据创建成功！")
        print("💡 数据包含了所有类型的状态，适合测试各种功能")
        
    except Exception as e:
        print(f"❌ 创建测试数据时出错: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    create_complete_test_data()
