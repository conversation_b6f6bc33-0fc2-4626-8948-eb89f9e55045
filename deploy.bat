@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 设备零备件周期管理系统 - Windows部署脚本
echo ========================================
echo.

echo [1/5] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

echo.
echo [2/5] 安装依赖包...
pip install flask flask-cors
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)
echo ✅ 依赖安装完成

echo.
echo [3/5] 初始化数据库...
python initialize_system.py
if errorlevel 1 (
    echo ❌ 数据库初始化失败
    pause
    exit /b 1
)
echo ✅ 数据库初始化完成

echo.
echo [4/5] 检查系统文件...
if not exist "app.py" (
    echo ❌ 错误: 缺少核心文件 app.py
    pause
    exit /b 1
)
if not exist "templates" (
    echo ❌ 错误: 缺少模板目录
    pause
    exit /b 1
)
echo ✅ 系统文件检查通过

echo.
echo [5/5] 启动系统...
echo ========================================
echo 🚀 设备零备件周期管理系统启动中...
echo 📍 访问地址: http://127.0.0.1:5000
echo 🔧 按 Ctrl+C 停止服务器
echo ========================================
echo.

python start.py

echo.
echo 👋 系统已停止运行
pause
