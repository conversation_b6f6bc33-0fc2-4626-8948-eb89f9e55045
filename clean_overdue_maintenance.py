#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
清理逾期维保记录，只保留2条逾期记录
"""

import sqlite3
from datetime import datetime, timedelta
import random

def clean_overdue_maintenance():
    """清理逾期维保记录"""
    db_path = 'equipment_maintenance.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("=== 检查当前维保记录状态 ===")
        
        # 检查所有维保记录的状态
        today = datetime.now().date()
        cursor.execute("""
            SELECT 
                mr.id,
                sp.name as spare_part_name,
                e.name as equipment_name,
                a.name as area_name,
                mr.next_maintenance_date,
                CASE 
                    WHEN DATE(mr.next_maintenance_date) < DATE('now') THEN 'overdue'
                    WHEN DATE(mr.next_maintenance_date) <= DATE('now', '+30 days') THEN 'upcoming'
                    ELSE 'normal'
                END as status
            FROM maintenance_records mr
            JOIN spare_parts sp ON mr.spare_part_id = sp.id
            JOIN equipment e ON mr.equipment_id = e.id
            JOIN areas a ON mr.area_id = a.id
            ORDER BY mr.next_maintenance_date ASC
        """)
        
        all_records = cursor.fetchall()
        print(f"总维保记录数: {len(all_records)}")
        
        # 统计各状态的记录数
        overdue_records = [r for r in all_records if r[5] == 'overdue']
        upcoming_records = [r for r in all_records if r[5] == 'upcoming']
        normal_records = [r for r in all_records if r[5] == 'normal']
        
        print(f"逾期记录数: {len(overdue_records)}")
        print(f"即将到期记录数: {len(upcoming_records)}")
        print(f"正常记录数: {len(normal_records)}")
        
        if len(overdue_records) <= 2:
            print("逾期记录数量已经符合要求，无需清理")
            return
        
        print(f"\n前10条逾期记录:")
        for i, record in enumerate(overdue_records[:10]):
            print(f"  {i+1}. {record[1]} - {record[2]} - {record[3]} - {record[4]}")
        
        # 选择要保留的2条逾期记录（选择最近的2条）
        records_to_keep = sorted(overdue_records, key=lambda x: x[4], reverse=True)[:2]
        keep_ids = [r[0] for r in records_to_keep]
        
        print(f"\n将保留的2条逾期记录:")
        for record in records_to_keep:
            print(f"  - {record[1]} - {record[2]} - {record[3]} - {record[4]}")
        
        # 删除其他逾期记录
        records_to_delete = [r for r in overdue_records if r[0] not in keep_ids]
        delete_ids = [r[0] for r in records_to_delete]
        
        print(f"\n将删除 {len(delete_ids)} 条逾期记录")
        
        if delete_ids:
            # 执行删除
            placeholders = ','.join(['?' for _ in delete_ids])
            cursor.execute(f"DELETE FROM maintenance_records WHERE id IN ({placeholders})", delete_ids)
            
            print(f"✅ 成功删除 {len(delete_ids)} 条逾期维保记录")
        
        # 为了让数据更合理，我们还需要调整一些记录的时间，让它们分布更均匀
        print("\n=== 调整维保记录时间分布 ===")
        
        # 获取剩余的记录
        cursor.execute("SELECT COUNT(*) FROM maintenance_records")
        remaining_count = cursor.fetchone()[0]
        print(f"剩余维保记录数: {remaining_count}")
        
        # 重新生成一些维保记录，让时间分布更合理
        cursor.execute("""
            SELECT mr.id, sp.maintenance_cycle_months
            FROM maintenance_records mr
            JOIN spare_parts sp ON mr.spare_part_id = sp.id
            WHERE mr.id NOT IN ({})
        """.format(','.join(map(str, keep_ids)) if keep_ids else '0'))
        
        records_to_update = cursor.fetchall()
        
        # 更新这些记录的时间，让它们有合理的分布
        today = datetime.now().date()
        updates = []
        
        for record_id, cycle_months in records_to_update:
            # 随机生成过去的维保日期（最近6个月内）
            days_ago = random.randint(1, 180)
            maintenance_date = today - timedelta(days=days_ago)
            next_maintenance_date = maintenance_date + timedelta(days=cycle_months * 30)
            
            updates.append((
                maintenance_date.strftime('%Y-%m-%d'),
                next_maintenance_date.strftime('%Y-%m-%d'),
                record_id
            ))
        
        # 批量更新
        cursor.executemany("""
            UPDATE maintenance_records 
            SET maintenance_date = ?, next_maintenance_date = ?
            WHERE id = ?
        """, updates)
        
        print(f"✅ 更新了 {len(updates)} 条记录的时间")
        
        conn.commit()
        
        # 最终验证
        print("\n=== 清理后的状态 ===")
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN DATE(next_maintenance_date) < DATE('now') THEN 1 ELSE 0 END) as overdue,
                SUM(CASE WHEN DATE(next_maintenance_date) <= DATE('now', '+30 days') AND DATE(next_maintenance_date) >= DATE('now') THEN 1 ELSE 0 END) as upcoming,
                SUM(CASE WHEN DATE(next_maintenance_date) > DATE('now', '+30 days') THEN 1 ELSE 0 END) as normal
            FROM maintenance_records
        """)
        
        final_stats = cursor.fetchone()
        print(f"总记录数: {final_stats[0]}")
        print(f"逾期记录: {final_stats[1]}")
        print(f"即将到期: {final_stats[2]}")
        print(f"正常记录: {final_stats[3]}")
        
        print("\n✅ 维保记录清理完成！")
        
    except Exception as e:
        print(f"❌ 清理维保记录时出错: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    clean_overdue_maintenance()
