#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建丰富的测试数据
"""

import sqlite3
import random
from datetime import datetime, timedelta

def create_rich_test_data():
    """创建丰富的测试数据"""
    print("开始创建丰富的测试数据...")
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 清除现有的维保记录
        cursor.execute("DELETE FROM maintenance_records")
        
        # 获取基础数据
        cursor.execute("SELECT id, name FROM areas")
        areas = cursor.fetchall()
        
        cursor.execute("SELECT id, name, area_id FROM equipment")
        equipment = cursor.fetchall()
        
        cursor.execute("SELECT id, name, maintenance_cycle_months FROM spare_parts")
        spare_parts = cursor.fetchall()
        
        # 创建不同状态的维保记录
        today = datetime.now().date()
        maintenance_records = []
        
        # 技师名单
        technicians = ["张师傅", "李师傅", "王师傅", "刘师傅", "陈师傅"]
        
        record_id = 1
        
        # 1. 逾期维保记录（已过期5-60天）
        print("创建逾期维保记录...")
        for i in range(15):
            area = random.choice(areas)
            eq = random.choice([e for e in equipment if e[2] == area[0]])  # 同区域设备
            spare_part = random.choice(spare_parts)
            
            # 过期5-60天
            overdue_days = random.randint(5, 60)
            maintenance_date = today - timedelta(days=365 + overdue_days)  # 一年前维保，现在过期
            next_maintenance_date = today - timedelta(days=overdue_days)
            
            maintenance_records.append((
                record_id, spare_part[0], area[0], eq[0], maintenance_date, next_maintenance_date,
                random.choice(technicians), f"逾期{overdue_days}天的维保记录", random.randint(1, 3)
            ))
            record_id += 1
        
        # 2. 即将到期维保记录（1-30天内到期）
        print("创建即将到期维保记录...")
        for i in range(5):
            area = random.choice(areas)
            eq = random.choice([e for e in equipment if e[2] == area[0]])
            spare_part = random.choice(spare_parts)
            
            # 1-30天内到期
            days_remaining = random.randint(1, 30)
            cycle_months = spare_part[2]
            maintenance_date = today - timedelta(days=365 - days_remaining)
            next_maintenance_date = today + timedelta(days=days_remaining)
            
            status_desc = ""
            if days_remaining <= 5:
                status_desc = f"紧急！还剩{days_remaining}天"
            elif days_remaining <= 15:
                status_desc = f"警告：还剩{days_remaining}天"
            else:
                status_desc = f"即将到期，还剩{days_remaining}天"
            
            maintenance_records.append((
                record_id, spare_part[0], area[0], eq[0], maintenance_date, next_maintenance_date,
                random.choice(technicians), status_desc, random.randint(1, 2)
            ))
            record_id += 1
        
        # 3. 正常状态维保记录（31天-6个月内到期）
        print("创建正常状态维保记录...")
        for i in range(30):
            area = random.choice(areas)
            eq = random.choice([e for e in equipment if e[2] == area[0]])
            spare_part = random.choice(spare_parts)
            
            # 31天-6个月内到期
            days_remaining = random.randint(31, 180)
            maintenance_date = today - timedelta(days=365 - days_remaining)
            next_maintenance_date = today + timedelta(days=days_remaining)
            
            if days_remaining <= 60:
                status_desc = f"正常，还剩{days_remaining}天"
            elif days_remaining <= 120:
                months = days_remaining // 30
                status_desc = f"正常，还剩约{months}个月"
            else:
                months = days_remaining // 30
                status_desc = f"状态良好，还剩约{months}个月"
            
            maintenance_records.append((
                record_id, spare_part[0], area[0], eq[0], maintenance_date, next_maintenance_date,
                random.choice(technicians), status_desc, random.randint(1, 2)
            ))
            record_id += 1
        
        # 4. 长期维保记录（6个月以上）
        print("创建长期维保记录...")
        for i in range(20):
            area = random.choice(areas)
            eq = random.choice([e for e in equipment if e[2] == area[0]])
            spare_part = random.choice(spare_parts)
            
            # 6个月-2年内到期
            days_remaining = random.randint(180, 730)
            maintenance_date = today - timedelta(days=365 - days_remaining)
            next_maintenance_date = today + timedelta(days=days_remaining)
            
            months = days_remaining // 30
            if months <= 12:
                status_desc = f"长期维保，还剩{months}个月"
            else:
                years = months // 12
                remaining_months = months % 12
                if remaining_months > 0:
                    status_desc = f"长期维保，还剩{years}年{remaining_months}个月"
                else:
                    status_desc = f"长期维保，还剩{years}年"
            
            maintenance_records.append((
                record_id, spare_part[0], area[0], eq[0], maintenance_date, next_maintenance_date,
                random.choice(technicians), status_desc, random.randint(1, 2)
            ))
            record_id += 1
        
        # 插入维保记录
        cursor.executemany('''
            INSERT INTO maintenance_records 
            (id, spare_part_id, area_id, equipment_id, maintenance_date, next_maintenance_date, technician, notes, quantity_used)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', maintenance_records)
        
        # 更新库存数据，让一些零备件库存不足
        print("更新库存数据...")
        cursor.execute("SELECT id FROM spare_parts")
        spare_part_ids = [row[0] for row in cursor.fetchall()]
        
        # 随机设置一些零备件库存不足
        low_stock_parts = random.sample(spare_part_ids, len(spare_part_ids) // 3)
        
        for sp_id in spare_part_ids:
            if sp_id in low_stock_parts:
                # 库存不足
                quantity = random.randint(0, 4)
                min_quantity = random.randint(5, 10)
            else:
                # 库存正常
                quantity = random.randint(10, 50)
                min_quantity = random.randint(5, 8)
            
            cursor.execute('''
                UPDATE inventory 
                SET quantity = ?, min_quantity = ?, last_updated = ?
                WHERE spare_part_id = ?
            ''', (quantity, min_quantity, datetime.now(), sp_id))
        
        conn.commit()
        
        # 统计创建的数据
        cursor.execute("SELECT COUNT(*) FROM maintenance_records")
        total_records = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date < ?", (today,))
        overdue_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date BETWEEN ? AND ?", 
                      (today, today + timedelta(days=30)))
        upcoming_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM inventory WHERE quantity < min_quantity")
        low_stock_count = cursor.fetchone()[0]
        
        print(f"✅ 数据创建完成！")
        print(f"   总维保记录: {total_records}")
        print(f"   逾期维保: {overdue_count}")
        print(f"   即将到期: {upcoming_count}")
        print(f"   库存不足: {low_stock_count}")
        
    except Exception as e:
        print(f"创建测试数据时出错: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    create_rich_test_data()
