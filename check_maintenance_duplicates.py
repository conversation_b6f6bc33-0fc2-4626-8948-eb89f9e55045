#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查维保记录中的重复情况
"""

import sqlite3
import os

def check_maintenance_duplicates():
    """检查维保记录中的重复情况"""
    print("=== 检查维保记录重复情况 ===")
    
    db_path = 'equipment_maintenance.db'
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查维保记录表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='maintenance_records'")
        if not cursor.fetchone():
            print("❌ maintenance_records表不存在")
            return
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(maintenance_records)")
        columns = cursor.fetchall()
        print("维保记录表结构:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 统计总的维保记录数
        cursor.execute("SELECT COUNT(*) FROM maintenance_records")
        total_count = cursor.fetchone()[0]
        print(f"\n总维保记录数: {total_count}")
        
        if total_count == 0:
            print("❌ 没有维保记录")
            return
        
        # 检查是否存在相同区域、设备、零件的重复维保记录
        cursor.execute("""
            SELECT 
                area_id,
                equipment_id, 
                spare_part_id,
                COUNT(*) as count
            FROM maintenance_records
            GROUP BY area_id, equipment_id, spare_part_id
            HAVING COUNT(*) > 1
            ORDER BY count DESC
        """)
        
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"\n❌ 发现 {len(duplicates)} 组重复的维保记录:")
            for i, dup in enumerate(duplicates, 1):
                area_id, equipment_id, spare_part_id, count = dup
                print(f"\n{i}. 区域ID: {area_id}, 设备ID: {equipment_id}, 零件ID: {spare_part_id}, 重复次数: {count}")
                
                # 获取区域、设备、零件名称
                cursor.execute("SELECT name FROM areas WHERE id = ?", (area_id,))
                area_name = cursor.fetchone()
                area_name = area_name[0] if area_name else f"未知区域({area_id})"
                
                cursor.execute("SELECT name FROM equipment WHERE id = ?", (equipment_id,))
                equipment_name = cursor.fetchone()
                equipment_name = equipment_name[0] if equipment_name else f"未知设备({equipment_id})"
                
                cursor.execute("SELECT name FROM spare_parts WHERE id = ?", (spare_part_id,))
                spare_part_name = cursor.fetchone()
                spare_part_name = spare_part_name[0] if spare_part_name else f"未知零件({spare_part_id})"
                
                print(f"   区域: {area_name}")
                print(f"   设备: {equipment_name}")
                print(f"   零件: {spare_part_name}")
                
                # 显示具体的重复记录
                cursor.execute("""
                    SELECT id, maintenance_date, next_maintenance_date, technician, notes
                    FROM maintenance_records
                    WHERE area_id = ? AND equipment_id = ? AND spare_part_id = ?
                    ORDER BY maintenance_date DESC
                """, (area_id, equipment_id, spare_part_id))
                
                records = cursor.fetchall()
                print("   具体记录:")
                for record in records:
                    print(f"     ID: {record[0]}, 维保日期: {record[1]}, 下次维保: {record[2]}, 技师: {record[3]}")
                    if record[4]:
                        print(f"       备注: {record[4]}")
        else:
            print("✅ 没有发现重复的维保记录")
        
        # 统计不同组合的数量
        cursor.execute("""
            SELECT COUNT(DISTINCT area_id, equipment_id, spare_part_id) 
            FROM maintenance_records
        """)
        unique_combinations = cursor.fetchone()[0]
        print(f"\n唯一的区域-设备-零件组合数: {unique_combinations}")
        
        # 显示一些示例记录
        cursor.execute("""
            SELECT 
                mr.id,
                a.name as area_name,
                e.name as equipment_name,
                sp.name as spare_part_name,
                mr.maintenance_date,
                mr.next_maintenance_date
            FROM maintenance_records mr
            JOIN areas a ON mr.area_id = a.id
            JOIN equipment e ON mr.equipment_id = e.id
            JOIN spare_parts sp ON mr.spare_part_id = sp.id
            ORDER BY mr.maintenance_date DESC
            LIMIT 5
        """)
        
        sample_records = cursor.fetchall()
        if sample_records:
            print("\n最近的5条维保记录:")
            for record in sample_records:
                print(f"  ID: {record[0]}, {record[1]} - {record[2]} - {record[3]}, 维保: {record[4]}, 下次: {record[5]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_maintenance_duplicates()
