#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def check_database():
    """直接检查数据库"""
    try:
        conn = sqlite3.connect('equipment_maintenance.db')
        cursor = conn.cursor()
        
        print("=== 检查数据库 ===")
        
        # 检查inventory表结构
        cursor.execute("PRAGMA table_info(inventory)")
        columns = cursor.fetchall()
        print("inventory表结构:")
        for col in columns:
            print(f"  {col[1]} {col[2]}")
        
        # 检查inventory数据
        cursor.execute("SELECT COUNT(*) FROM inventory")
        count = cursor.fetchone()[0]
        print(f"\ninventory记录数: {count}")
        
        if count > 0:
            cursor.execute("SELECT * FROM inventory LIMIT 3")
            records = cursor.fetchall()
            print("前3条inventory记录:")
            for record in records:
                print(f"  {record}")
        
        # 检查spare_parts数据
        cursor.execute("SELECT COUNT(*) FROM spare_parts")
        sp_count = cursor.fetchone()[0]
        print(f"\nspare_parts记录数: {sp_count}")
        
        if sp_count > 0:
            cursor.execute("SELECT id, name FROM spare_parts LIMIT 3")
            sp_records = cursor.fetchall()
            print("前3条spare_parts记录:")
            for record in sp_records:
                print(f"  {record}")
        
        # 测试JOIN查询
        if count > 0 and sp_count > 0:
            print("\n=== 测试JOIN查询 ===")
            try:
                cursor.execute("""
                    SELECT i.id, sp.name, i.quantity, i.min_quantity, i.location
                    FROM inventory i
                    JOIN spare_parts sp ON i.spare_part_id = sp.id
                    LIMIT 3
                """)
                join_results = cursor.fetchall()
                print(f"JOIN查询成功，返回{len(join_results)}条记录:")
                for result in join_results:
                    print(f"  {result}")
            except Exception as e:
                print(f"JOIN查询失败: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据库失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database()
