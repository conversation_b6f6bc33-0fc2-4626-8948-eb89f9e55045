#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def check_maintenance_records():
    """专门检查维保记录表"""
    db_path = 'equipment_maintenance.db'
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("=== 检查维保记录表 ===")
        
        # 检查维保记录数量
        cursor.execute("SELECT COUNT(*) FROM maintenance_records")
        count = cursor.fetchone()[0]
        print(f"维保记录总数: {count}")
        
        if count == 0:
            print("❌ 维保记录表为空！")
            
            # 检查其他表的数据
            cursor.execute("SELECT COUNT(*) FROM areas")
            areas_count = cursor.fetchone()[0]
            print(f"区域数量: {areas_count}")
            
            cursor.execute("SELECT COUNT(*) FROM equipment")
            equipment_count = cursor.fetchone()[0]
            print(f"设备数量: {equipment_count}")
            
            cursor.execute("SELECT COUNT(*) FROM spare_parts")
            parts_count = cursor.fetchone()[0]
            print(f"零备件数量: {parts_count}")
            
            if areas_count > 0 and equipment_count > 0 and parts_count > 0:
                print("✅ 基础数据存在，需要重新创建维保记录")
                return True
            else:
                print("❌ 基础数据不完整")
                return False
        else:
            print(f"✅ 维保记录存在: {count} 条")
            
            # 显示前几条记录
            cursor.execute("""
                SELECT mr.id, sp.name, a.name, e.name, mr.maintenance_date, mr.next_maintenance_date
                FROM maintenance_records mr
                JOIN spare_parts sp ON mr.spare_part_id = sp.id
                JOIN areas a ON mr.area_id = a.id
                JOIN equipment e ON mr.equipment_id = e.id
                LIMIT 5
            """)
            records = cursor.fetchall()
            print("前5条维保记录:")
            for record in records:
                print(f"  {record[0]}: {record[1]} | {record[2]} | {record[3]} | {record[4]} -> {record[5]}")
            
            return False
            
    except Exception as e:
        print(f"❌ 检查维保记录时出错: {e}")
        return False
    finally:
        conn.close()

def create_maintenance_records_only():
    """只创建维保记录"""
    db_path = 'equipment_maintenance.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("\n=== 创建维保记录 ===")
        
        # 获取基础数据
        cursor.execute('SELECT id, name, equipment_type, area_id FROM equipment')
        equipment_list = cursor.fetchall()
        print(f"设备数量: {len(equipment_list)}")
        
        cursor.execute('SELECT id, name, maintenance_cycle_months FROM spare_parts')
        spare_parts_list = cursor.fetchall()
        print(f"零备件数量: {len(spare_parts_list)}")
        
        from datetime import datetime, timedelta
        import random
        
        maintenance_records = []
        technicians = ['张师傅', '李师傅', '王师傅', '赵师傅', '陈师傅']
        today = datetime.now().date()
        
        # 为每台设备创建相应的维保记录
        for eq_id, eq_name, eq_type, area_id in equipment_list:
            # 根据设备类型选择对应的零备件
            if eq_type == '堆垛机':
                # 堆垛机使用：主接触器、变频器、传感器
                applicable_parts = [(1, '主接触器', 6), (2, '变频器', 12), (3, '传感器', 3)]
            elif eq_type == '入库皮带':
                # 入库皮带使用：皮带、滚筒
                applicable_parts = [(4, '皮带', 3), (5, '滚筒', 6)]
            elif eq_type == 'AGV小车':
                # AGV小车使用：保险丝、电池、轮子
                applicable_parts = [(6, '保险丝', 6), (7, '电池', 12), (8, '轮子', 9)]
            else:
                continue
            
            # 为每个适用的零备件创建维保记录
            for part_id, part_name, cycle_months in applicable_parts:
                # 创建多条历史维保记录
                for record_num in range(1, 4):  # 每个零备件创建3条记录
                    # 计算维保日期（过去的日期）
                    days_ago = random.randint(30, 365)
                    maintenance_date = today - timedelta(days=days_ago)
                    next_maintenance_date = maintenance_date + timedelta(days=cycle_months * 30)
                    
                    technician = random.choice(technicians)
                    notes = f'维保记录 - {eq_name} - {part_name}'
                    
                    maintenance_records.append((
                        part_id, area_id, eq_id, 
                        maintenance_date.strftime('%Y-%m-%d'),
                        next_maintenance_date.strftime('%Y-%m-%d'),
                        technician, notes, 1
                    ))
        
        # 插入维保记录
        cursor.executemany('''
            INSERT INTO maintenance_records 
            (spare_part_id, area_id, equipment_id, maintenance_date, next_maintenance_date, technician, notes, quantity_used)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', maintenance_records)
        
        conn.commit()
        print(f"✅ 成功插入 {len(maintenance_records)} 条维保记录")
        
        # 验证插入结果
        cursor.execute("SELECT COUNT(*) FROM maintenance_records")
        count = cursor.fetchone()[0]
        print(f"验证: 维保记录总数 = {count}")
        
    except Exception as e:
        print(f"❌ 创建维保记录时出错: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    need_create = check_maintenance_records()
    if need_create:
        create_maintenance_records_only()
        print("\n=== 重新检查维保记录 ===")
        check_maintenance_records()
