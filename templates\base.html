<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}设备零备件周期管理系统{% endblock %}</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            font-size: 14px;
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .sidebar {
            min-height: calc(100vh - 56px);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 5px 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.2);
            transform: translateX(5px);
        }
        
        .sidebar .nav-link i {
            width: 20px;
            margin-right: 10px;
        }
        
        .main-content {
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 12px 12px 0 0 !important;
            padding: 15px 20px;
        }
        
        .stat-card {
            text-align: center;
            padding: 25px;
            border-radius: 12px;
            color: white;
            margin-bottom: 20px;
        }
        
        .stat-card.primary { background: linear-gradient(135deg, var(--primary-color), #34495e); }
        .stat-card.success { background: linear-gradient(135deg, var(--success-color), #2ecc71); }
        .stat-card.warning { background: linear-gradient(135deg, var(--warning-color), #e67e22); }
        .stat-card.danger { background: linear-gradient(135deg, var(--danger-color), #c0392b); }
        .stat-card.info { background: linear-gradient(135deg, var(--info-color), #3498db); }
        
        .stat-card h3 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stat-card p {
            font-size: 1.1rem;
            margin-bottom: 0;
            opacity: 0.9;
        }
        
        .progress-bar-custom {
            height: 25px;
            border-radius: 12px;
            background: linear-gradient(90deg, var(--success-color), var(--warning-color), var(--danger-color));
            position: relative;
            overflow: hidden;
        }
        
        .progress-bar-custom::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 25%, rgba(255,255,255,0.1) 25%, rgba(255,255,255,0.1) 50%, transparent 50%, transparent 75%, rgba(255,255,255,0.1) 75%);
            background-size: 20px 20px;
            animation: progress-animation 1s linear infinite;
        }
        
        @keyframes progress-animation {
            0% { background-position: 0 0; }
            100% { background-position: 20px 0; }
        }
        
        .table {
            border-radius: 8px;
            overflow: hidden;
        }
        
        .table thead th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            font-weight: 600;
            padding: 15px;
        }
        
        .table tbody tr {
            transition: background-color 0.3s ease;
        }
        
        .table tbody tr:hover {
            background-color: rgba(52, 152, 219, 0.1);
        }
        
        .badge {
            font-size: 0.8rem;
            padding: 6px 12px;
            border-radius: 20px;
        }
        
        .btn {
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }

        /* 维保进度图表特殊样式 */
        #maintenanceProgressChart {
            padding-right: 50px;
        }

        /* 维保进度图表容器优化 */
        .maintenance-progress-container {
            width: 100%;
            overflow: hidden;
            padding: 0 10px;
        }

        /* 图例隐藏状态样式 */
        .chartjs-legend li.hidden-legend-item {
            opacity: 0.5 !important;
            text-decoration: line-through !important;
            color: #999999 !important;
        }

        .chartjs-legend li.hidden-legend-item span {
            color: #999999 !important;
        }

        /* 图例项悬停效果 */
        .chartjs-legend li {
            cursor: pointer;
            transition: opacity 0.2s ease, color 0.2s ease;
        }

        .chartjs-legend li:hover {
            opacity: 0.8;
        }

        /* 进度条容器样式优化 */
        .progress-container {
            min-width: 130px;
            max-width: 160px;
        }

        .progress-text {
            min-width: 32px;
            text-align: left;
            flex-shrink: 0;
        }

        /* 确保表格列宽度合适 */
        #maintenanceTable th:nth-child(5),
        #maintenanceTable td:nth-child(5) {
            min-width: 150px;
            width: 150px;
        }

        /* 进度条样式增强 */
        .progress {
            background-color: #e9ecef;
            border-radius: 4px;
            box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
        }

        .progress-bar {
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* 小进度条的最小宽度确保可见性 */
        .progress-bar[style*="width: 0%"],
        .progress-bar[style*="width: 1%"],
        .progress-bar[style*="width: 2%"] {
            min-width: 3px !important;
        }

        /* 图表图例样式 */
        .chartjs-legend {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .chartjs-legend:hover {
            text-decoration: underline !important;
            color: #666 !important;
            transform: translateY(-1px);
        }

        .chartjs-legend.hidden {
            text-decoration: line-through !important;
            color: #999 !important;
            opacity: 0.6;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                min-height: auto;
            }
            
            .main-content {
                padding: 10px;
            }
            
            .stat-card h3 {
                font-size: 2rem;
            }
        }
        
        /* 大屏优化 */
        @media (min-width: 1400px) {
            .container-fluid {
                max-width: 1600px;
            }
            
            .stat-card h3 {
                font-size: 3rem;
            }
            
            .chart-container {
                height: 500px;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-cogs me-2"></i>设备零备件周期管理系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <span class="navbar-text">
                            <i class="fas fa-calendar-alt me-1"></i>
                            <span id="current-time"></span>
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'index' %}active{% endif %}" href="{{ url_for('index') }}">
                                <i class="fas fa-tachometer-alt"></i>系统概览
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'maintenance' %}active{% endif %}" href="{{ url_for('maintenance') }}">
                                <i class="fas fa-wrench"></i>维保管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'inventory' %}active{% endif %}" href="{{ url_for('inventory') }}">
                                <i class="fas fa-boxes"></i>库存管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'data' %}active{% endif %}" href="{{ url_for('data') }}">
                                <i class="fas fa-database"></i>数据管理
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-10 ms-sm-auto main-content">
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <script>
        // 更新当前时间
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('current-time').textContent = timeString;
        }
        
        // 每秒更新时间
        setInterval(updateTime, 1000);
        updateTime();
        
        // 通用AJAX错误处理
        $(document).ajaxError(function(event, xhr, settings, error) {
            console.error('AJAX错误:', error);
            alert('请求失败，请稍后重试');
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
