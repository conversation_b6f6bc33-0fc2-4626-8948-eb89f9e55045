#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试库存检查功能
"""

import requests
import json
from datetime import datetime

def test_inventory_check():
    """测试库存检查功能"""
    print("🧪 测试库存检查功能...")
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 1. 获取待执行的维保计划
        print("\n1️⃣ 获取待执行的维保计划...")
        response = requests.get(f"{base_url}/api/maintenance/plans?status=pending")
        
        if response.status_code != 200:
            print(f"❌ 获取维保计划失败: {response.status_code}")
            return False
        
        plans_data = response.json()
        if not plans_data.get('success'):
            print(f"❌ 获取维保计划失败: {plans_data.get('error')}")
            return False
        
        plans = plans_data['data']
        if not plans:
            print("⚠️  没有待执行的维保计划")
            return True
        
        print(f"✅ 找到 {len(plans)} 个待执行的维保计划")
        for i, plan in enumerate(plans[:3]):  # 只显示前3个
            print(f"   {i+1}. ID:{plan['id']} - {plan['spare_part_name']} - {plan['area_name']} - {plan['equipment_name']}")
        
        # 选择第一个计划进行测试
        plan_id = plans[0]['id']
        plan_name = plans[0]['spare_part_name']
        print(f"\n选择计划ID: {plan_id} ({plan_name}) 进行测试")
        
        # 2. 检查当前库存状态
        print(f"\n2️⃣ 检查当前库存状态...")
        response = requests.get(f"{base_url}/api/inventory/status")
        
        if response.status_code == 200:
            inventory_data = response.json()
            if inventory_data.get('success'):
                inventory_list = inventory_data['data']
                # 查找对应的零备件库存
                spare_part_inventory = None
                for item in inventory_list:
                    if item['spare_part_name'] == plan_name:
                        spare_part_inventory = item
                        break
                
                if spare_part_inventory:
                    print(f"✅ 找到库存信息:")
                    print(f"   零备件: {spare_part_inventory['spare_part_name']}")
                    print(f"   当前库存: {spare_part_inventory['total_quantity']}")
                    print(f"   最小库存: {spare_part_inventory['min_quantity']}")
                    print(f"   库存状态: {spare_part_inventory['status_text']}")
                    
                    current_stock = spare_part_inventory['total_quantity']
                else:
                    print(f"⚠️  未找到零备件 '{plan_name}' 的库存信息")
                    current_stock = 0
        
        # 3. 测试执行维保计划（应该显示库存不足的错误）
        print(f"\n3️⃣ 测试执行维保计划（预期：库存不足错误）...")
        execute_data = {
            "maintenance_date": datetime.now().strftime('%Y-%m-%d'),
            "technician": "测试维保员",
            "quantity_used": 1,
            "notes": "测试库存检查功能"
        }
        
        print(f"执行数据: {json.dumps(execute_data, indent=2, ensure_ascii=False)}")
        
        response = requests.post(
            f"{base_url}/api/maintenance/plan/{plan_id}/execute",
            headers={'Content-Type': 'application/json'},
            data=json.dumps(execute_data)
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 400:
            result = response.json()
            if not result.get('success'):
                error_message = result.get('error', '')
                if '库存不足' in error_message or '没有库存记录' in error_message:
                    print("✅ 库存检查功能正常工作!")
                    print(f"   错误信息: {error_message}")
                else:
                    print(f"⚠️  错误信息不符合预期: {error_message}")
            else:
                print("⚠️  预期失败但返回成功")
        elif response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("⚠️  预期库存不足但执行成功了")
            else:
                print(f"❌ 执行失败: {result.get('error')}")
        else:
            print(f"❌ 意外的HTTP状态码: {response.status_code}")
        
        # 4. 测试添加维保记录（也应该显示库存不足的错误）
        print(f"\n4️⃣ 测试添加维保记录（预期：库存不足错误）...")
        
        # 获取计划详情
        response = requests.get(f"{base_url}/api/maintenance/plan/{plan_id}")
        if response.status_code == 200:
            plan_data = response.json()
            if plan_data.get('success'):
                plan_detail = plan_data['data']
                
                maintenance_record_data = {
                    "spare_part_id": plan_detail['spare_part_id'],
                    "area_id": plan_detail['area_id'],
                    "equipment_id": plan_detail['equipment_id'],
                    "maintenance_date": datetime.now().strftime('%Y-%m-%d'),
                    "technician": "测试维保员",
                    "quantity_used": 1,
                    "notes": "测试库存检查功能"
                }
                
                print(f"维保记录数据: {json.dumps(maintenance_record_data, indent=2, ensure_ascii=False)}")
                
                response = requests.post(
                    f"{base_url}/api/maintenance/record",
                    headers={'Content-Type': 'application/json'},
                    data=json.dumps(maintenance_record_data)
                )
                
                print(f"状态码: {response.status_code}")
                print(f"响应: {response.text}")
                
                if response.status_code == 400:
                    result = response.json()
                    if not result.get('success'):
                        error_message = result.get('error', '')
                        if '库存不足' in error_message or '没有库存记录' in error_message:
                            print("✅ 维保记录添加的库存检查功能正常工作!")
                            print(f"   错误信息: {error_message}")
                        else:
                            print(f"⚠️  错误信息不符合预期: {error_message}")
                    else:
                        print("⚠️  预期失败但返回成功")
                elif response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        print("⚠️  预期库存不足但添加成功了")
                    else:
                        print(f"❌ 添加失败: {result.get('error')}")
                else:
                    print(f"❌ 意外的HTTP状态码: {response.status_code}")
        
        print(f"\n🎉 库存检查功能测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 库存检查功能测试工具")
    print("=" * 50)
    
    success = test_inventory_check()
    
    if success:
        print("\n✅ 测试完成!")
    else:
        print("\n❌ 测试发现问题")

if __name__ == "__main__":
    main()
