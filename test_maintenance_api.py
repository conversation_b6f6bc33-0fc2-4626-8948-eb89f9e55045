#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import sqlite3
from datetime import datetime, timedelta

def test_maintenance_api():
    """测试维保API"""
    
    print("=== 测试维保API ===")
    
    try:
        # 1. 先直接检查数据库
        print("\n1. 检查数据库维保记录:")
        conn = sqlite3.connect('equipment_maintenance.db')
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM maintenance_records')
        count = cursor.fetchone()[0]
        print(f"   维保记录总数: {count}")
        
        if count == 0:
            print("   数据库中没有维保记录，创建测试数据...")
            
            # 获取设备和零备件
            cursor.execute('SELECT id FROM equipment LIMIT 3')
            equipment_ids = [row[0] for row in cursor.fetchall()]
            
            cursor.execute('SELECT id FROM spare_parts LIMIT 3')
            part_ids = [row[0] for row in cursor.fetchall()]
            
            if equipment_ids and part_ids:
                today = datetime.now().date()
                
                # 创建测试维保记录
                test_records = [
                    (1, equipment_ids[0], part_ids[0], 
                     (today - timedelta(days=20)).isoformat(),
                     (today - timedelta(days=5)).isoformat(),  # 已过期
                     "设备1定期维护", "需要更换零件", datetime.now().isoformat()),
                    
                    (2, equipment_ids[1], part_ids[1],
                     (today - timedelta(days=15)).isoformat(), 
                     (today + timedelta(days=5)).isoformat(),  # 即将到期
                     "设备2月度检查", "检查零件状态", datetime.now().isoformat()),
                     
                    (3, equipment_ids[2], part_ids[2],
                     (today - timedelta(days=10)).isoformat(),
                     (today + timedelta(days=45)).isoformat(),  # 正常
                     "设备3常规保养", "零件状态良好", datetime.now().isoformat())
                ]
                
                cursor.executemany('''
                    INSERT INTO maintenance_records 
                    (id, equipment_id, spare_part_id, maintenance_date, next_maintenance_date, 
                     description, notes, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', test_records)
                
                conn.commit()
                print(f"   创建了 {len(test_records)} 条测试维保记录")
        
        conn.close()
        
        # 2. 测试维保状态API
        print("\n2. 测试维保状态API:")
        response = requests.get('http://127.0.0.1:5000/api/maintenance/status')
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   成功: {data.get('success')}")
            if data.get('success'):
                records = data.get('data', [])
                print(f"   维保记录数量: {len(records)}")
                for i, record in enumerate(records[:3]):
                    print(f"   记录{i+1}: {record}")
            else:
                print(f"   错误: {data.get('error')}")
        else:
            print(f"   HTTP错误: {response.text}")
        
        # 3. 测试维保提醒API
        print("\n3. 测试维保提醒API:")
        response = requests.get('http://127.0.0.1:5000/api/maintenance/alerts?days=30')
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   成功: {data.get('success')}")
            if data.get('success'):
                alerts = data.get('data', [])
                print(f"   维保提醒数量: {len(alerts)}")
                for i, alert in enumerate(alerts[:3]):
                    print(f"   提醒{i+1}: {alert}")
            else:
                print(f"   错误: {data.get('error')}")
        else:
            print(f"   HTTP错误: {response.text}")
        
        # 4. 测试统计API
        print("\n4. 测试统计API:")
        response = requests.get('http://127.0.0.1:5000/api/statistics')
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   成功: {data.get('success')}")
            if data.get('success'):
                stats = data.get('data', {})
                print(f"   统计数据: {stats}")
            else:
                print(f"   错误: {data.get('error')}")
        else:
            print(f"   HTTP错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_maintenance_api()
