<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据管理CRUD功能演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .demo-container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .demo-title {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .feature-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            transition: transform 0.2s;
        }
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .feature-title {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .feature-desc {
            color: #6c757d;
            line-height: 1.6;
        }
        .demo-steps {
            background: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 20px;
            margin: 20px 0;
        }
        .step {
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 5px;
        }
        .step-number {
            background: #3498db;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        .api-example {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .success {
            color: #27ae60;
            font-weight: bold;
        }
        .warning {
            color: #f39c12;
            font-weight: bold;
        }
        .error {
            color: #e74c3c;
            font-weight: bold;
        }
        .btn {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            transition: background 0.2s;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #229954;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">🎯 数据管理CRUD功能演示</h1>
        
        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon">🏢</div>
                <h3 class="feature-title">区域管理</h3>
                <div class="feature-desc">
                    完整的区域增删改查功能，支持区域信息的全生命周期管理。
                    <br><br>
                    <span class="success">✅ 新增区域</span><br>
                    <span class="success">✅ 编辑区域</span><br>
                    <span class="success">✅ 删除区域</span><br>
                    <span class="success">✅ 级联保护</span>
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">⚙️</div>
                <h3 class="feature-title">设备管理</h3>
                <div class="feature-desc">
                    设备信息的完整管理，支持多种设备类型和区域关联。
                    <br><br>
                    <span class="success">✅ 新增设备</span><br>
                    <span class="success">✅ 编辑设备</span><br>
                    <span class="success">✅ 删除设备</span><br>
                    <span class="success">✅ 区域关联</span>
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔧</div>
                <h3 class="feature-title">零备件管理</h3>
                <div class="feature-desc">
                    零备件的全面管理，包括维保周期设置和设备关联。
                    <br><br>
                    <span class="success">✅ 新增零备件</span><br>
                    <span class="success">✅ 编辑零备件</span><br>
                    <span class="success">✅ 删除零备件</span><br>
                    <span class="success">✅ 维保周期</span>
                </div>
            </div>
        </div>
        
        <div class="demo-steps">
            <h3>📋 功能使用步骤</h3>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>访问数据管理页面</strong>
                <div class="api-example">http://127.0.0.1:5000/data</div>
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>区域管理操作</strong>
                <ul>
                    <li>点击"区域管理"展开面板</li>
                    <li>点击"新增区域"按钮创建新区域</li>
                    <li>点击表格中的"编辑"按钮修改区域信息</li>
                    <li>点击"删除"按钮删除区域（需要先删除关联设备）</li>
                </ul>
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>设备管理操作</strong>
                <ul>
                    <li>点击"设备管理"展开面板</li>
                    <li>选择区域查看该区域的设备</li>
                    <li>点击"新增设备"按钮创建新设备</li>
                    <li>使用编辑和删除按钮管理设备</li>
                </ul>
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <strong>零备件管理操作</strong>
                <ul>
                    <li>点击"零备件管理"展开面板</li>
                    <li>选择设备查看该设备的零备件</li>
                    <li>点击"新增零备件"按钮创建新零备件</li>
                    <li>设置维保周期和相关信息</li>
                </ul>
            </div>
        </div>
        
        <div class="demo-container">
            <h3>🔌 API接口示例</h3>
            
            <h4>创建区域</h4>
            <div class="api-example">
POST /api/areas
Content-Type: application/json

{
  "name": "新区域",
  "description": "区域描述"
}
            </div>
            
            <h4>创建设备</h4>
            <div class="api-example">
POST /api/equipment
Content-Type: application/json

{
  "area_id": 1,
  "name": "新设备",
  "type": "堆垛机",
  "description": "设备描述"
}
            </div>
            
            <h4>创建零备件</h4>
            <div class="api-example">
POST /api/spare-parts
Content-Type: application/json

{
  "equipment_id": 1,
  "name": "新零备件",
  "maintenance_cycle_months": 6,
  "description": "零备件描述"
}
            </div>
        </div>
        
        <div class="demo-container">
            <h3>⚠️ 重要提示</h3>
            <ul>
                <li><span class="warning">删除保护</span>: 删除区域前需要先删除该区域下的所有设备</li>
                <li><span class="warning">关联检查</span>: 删除设备前需要先删除该设备下的所有零备件</li>
                <li><span class="warning">数据验证</span>: 所有必填项都需要填写完整</li>
                <li><span class="warning">维保周期</span>: 必须是正整数（月为单位）</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="http://127.0.0.1:5000/data" class="btn btn-success" target="_blank">
                🚀 立即体验CRUD功能
            </a>
            <a href="http://127.0.0.1:5000" class="btn" target="_blank">
                🏠 返回系统首页
            </a>
        </div>
    </div>
    
    <div class="demo-container">
        <h3>✅ 功能完成状态</h3>
        <div class="feature-grid">
            <div class="feature-card">
                <h4>后端API</h4>
                <div class="success">✅ 区域管理API (增删改查)</div>
                <div class="success">✅ 设备管理API (增删改查)</div>
                <div class="success">✅ 零备件管理API (增删改查)</div>
                <div class="success">✅ 数据验证和错误处理</div>
            </div>
            
            <div class="feature-card">
                <h4>前端界面</h4>
                <div class="success">✅ 模态框表单设计</div>
                <div class="success">✅ 表格操作按钮</div>
                <div class="success">✅ 数据实时更新</div>
                <div class="success">✅ 用户友好提示</div>
            </div>
            
            <div class="feature-card">
                <h4>数据安全</h4>
                <div class="success">✅ 级联删除保护</div>
                <div class="success">✅ 数据完整性检查</div>
                <div class="success">✅ 输入验证机制</div>
                <div class="success">✅ 事务处理保证</div>
            </div>
        </div>
    </div>
</body>
</html>
