#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API数据一致性
"""

import requests
import json

def test_api_consistency():
    """测试系统概览和维保进度概览的数据一致性"""
    base_url = "http://127.0.0.1:5000"
    
    print("🔍 测试API数据一致性")
    print("=" * 50)
    
    try:
        # 1. 获取系统概览数据
        print("\n1️⃣ 获取系统概览数据...")
        response = requests.get(f"{base_url}/api/overview")
        if response.status_code == 200:
            overview_data = response.json()
            if overview_data.get('success'):
                data = overview_data['data']
                print(f"   零备件总数: {data['total_parts']}")
                print(f"   逾期维保: {data['overdue_maintenance']}")
                print(f"   即将到期: {data['upcoming_maintenance']}")
                print(f"   库存不足: {data['low_stock']}")
                
                overview_overdue = data['overdue_maintenance']
            else:
                print(f"   ❌ 系统概览API失败: {overview_data.get('error')}")
                return False
        else:
            print(f"   ❌ 系统概览API请求失败: {response.status_code}")
            return False
        
        # 2. 获取统计数据
        print("\n2️⃣ 获取统计数据...")
        response = requests.get(f"{base_url}/api/statistics")
        if response.status_code == 200:
            stats_data = response.json()
            if stats_data.get('success'):
                status_stats = stats_data['data']['status_stats']
                print("   维保状态统计:")
                
                stats_overdue = 0
                for stat in status_stats:
                    status_name = {
                        'overdue': '逾期',
                        'warning': '即将到期',
                        'normal': '正常'
                    }.get(stat['status'], stat['status'])
                    print(f"     {status_name}: {stat['count']}")
                    
                    if stat['status'] == 'overdue':
                        stats_overdue = stat['count']
                        
            else:
                print(f"   ❌ 统计API失败: {stats_data.get('error')}")
                return False
        else:
            print(f"   ❌ 统计API请求失败: {response.status_code}")
            return False
        
        # 3. 获取维保状态列表
        print("\n3️⃣ 获取维保状态列表...")
        response = requests.get(f"{base_url}/api/maintenance/status?per_page=100")
        if response.status_code == 200:
            status_data = response.json()
            if status_data.get('success'):
                statuses = status_data['data']
                
                # 统计逾期数量
                list_overdue = 0
                for status in statuses:
                    if status.get('status') == 'overdue':
                        list_overdue += 1
                
                print(f"   维保状态列表总数: {len(statuses)}")
                print(f"   其中逾期数量: {list_overdue}")
                        
            else:
                print(f"   ❌ 维保状态列表API失败: {status_data.get('error')}")
                return False
        else:
            print(f"   ❌ 维保状态列表API请求失败: {response.status_code}")
            return False
        
        # 4. 获取维保提醒
        print("\n4️⃣ 获取维保提醒...")
        response = requests.get(f"{base_url}/api/maintenance/alerts?days=365")
        if response.status_code == 200:
            alerts_data = response.json()
            if alerts_data.get('success'):
                alerts = alerts_data['data']
                
                # 统计逾期提醒数量
                alerts_overdue = 0
                for alert in alerts:
                    if alert.get('days_remaining', 0) < 0:
                        alerts_overdue += 1
                
                print(f"   维保提醒总数: {len(alerts)}")
                print(f"   其中逾期提醒: {alerts_overdue}")
                        
            else:
                print(f"   ❌ 维保提醒API失败: {alerts_data.get('error')}")
                return False
        else:
            print(f"   ❌ 维保提醒API请求失败: {response.status_code}")
            return False
        
        # 5. 对比结果
        print("\n📊 数据一致性对比:")
        print(f"   系统概览逾期数: {overview_overdue}")
        print(f"   统计图表逾期数: {stats_overdue}")
        print(f"   维保状态列表逾期数: {list_overdue}")
        print(f"   维保提醒逾期数: {alerts_overdue}")
        
        # 检查一致性
        all_counts = [overview_overdue, stats_overdue, list_overdue, alerts_overdue]
        if len(set(all_counts)) == 1:
            print("   ✅ 所有API的逾期数据完全一致！")
            return True
        else:
            print("   ❌ 存在数据不一致的问题")
            
            # 详细分析差异
            if overview_overdue == stats_overdue:
                print("   ✅ 系统概览和统计图表一致")
            else:
                print("   ❌ 系统概览和统计图表不一致")
                
            if overview_overdue == list_overdue:
                print("   ✅ 系统概览和维保状态列表一致")
            else:
                print("   ❌ 系统概览和维保状态列表不一致")
                
            if overview_overdue == alerts_overdue:
                print("   ✅ 系统概览和维保提醒一致")
            else:
                print("   ❌ 系统概览和维保提醒不一致")
            
            return False
            
    except Exception as e:
        print(f"   💥 测试过程中出现异常: {e}")
        return False

if __name__ == "__main__":
    success = test_api_consistency()
    if success:
        print("\n🎉 API数据一致性测试通过！")
    else:
        print("\n❌ API数据一致性测试失败！")
