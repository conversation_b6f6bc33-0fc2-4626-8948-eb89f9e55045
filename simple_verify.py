#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单验证设备名称脚本
"""

import sqlite3
import os

def main():
    print("=== 验证设备名称是否已修复 ===")
    
    db_path = 'equipment_maintenance.db'
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查看当前设备名称
        cursor.execute("""
            SELECT e.id, a.name as area_name, e.name, e.type 
            FROM equipment e 
            JOIN areas a ON e.area_id = a.id 
            ORDER BY e.id
        """)
        equipment = cursor.fetchall()
        
        print("当前设备列表:")
        for eq in equipment:
            print(f"ID: {eq[0]}, 区域: {eq[1]}, 名称: {eq[2]}, 类型: {eq[3]}")
        
        # 检查是否有重复名称
        cursor.execute("SELECT name, COUNT(*) FROM equipment GROUP BY name HAVING COUNT(*) > 1")
        duplicates = cursor.fetchall()
        
        if duplicates:
            print("\n❌ 仍然发现重复的设备名称:")
            for dup in duplicates:
                print(f"名称: {dup[0]}, 重复次数: {dup[1]}")
        else:
            print("\n✅ 所有设备名称都是唯一的")
        
        # 检查设备名称格式
        print("\n设备名称格式检查:")
        for eq in equipment:
            area_name, eq_name, eq_type = eq[1], eq[2], eq[3]
            if area_name in eq_name:
                print(f"✅ {eq_name} - 格式正确")
            else:
                print(f"❌ {eq_name} - 格式可能有问题")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 验证过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
