#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import random

def fix_inventory_data():
    """修复库存数据"""
    db_path = 'equipment_maintenance.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("=== 检查并修复库存数据 ===")
        
        # 检查inventory表是否有数据
        cursor.execute("SELECT COUNT(*) FROM inventory")
        inventory_count = cursor.fetchone()[0]
        print(f"当前inventory记录数: {inventory_count}")
        
        # 检查spare_parts表
        cursor.execute("SELECT COUNT(*) FROM spare_parts")
        parts_count = cursor.fetchone()[0]
        print(f"spare_parts记录数: {parts_count}")
        
        if parts_count == 0:
            print("❌ spare_parts表为空，无法创建inventory数据")
            return
        
        # 如果inventory表为空，创建数据
        if inventory_count == 0:
            print("创建inventory数据...")
            
            # 获取所有零备件
            cursor.execute("SELECT id, name FROM spare_parts")
            spare_parts = cursor.fetchall()
            
            inventory_data = []
            locations = ['仓库A-01', '仓库A-02', '仓库B-01', '仓库B-02', '仓库C-01', '仓库C-02', '仓库D-01', '仓库D-02']
            
            for i, (part_id, part_name) in enumerate(spare_parts):
                quantity = random.randint(8, 25)  # 随机库存数量
                min_qty = random.randint(3, 8)    # 最低库存
                location = locations[i % len(locations)]
                inventory_data.append((part_id, quantity, min_qty, location))
            
            # 插入inventory数据
            cursor.executemany('''
                INSERT INTO inventory (spare_part_id, quantity, min_quantity, location) 
                VALUES (?, ?, ?, ?)
            ''', inventory_data)
            
            conn.commit()
            print(f"✅ 成功插入{len(inventory_data)}条inventory记录")
        
        # 验证数据
        cursor.execute("SELECT COUNT(*) FROM inventory")
        final_count = cursor.fetchone()[0]
        print(f"最终inventory记录数: {final_count}")
        
        if final_count > 0:
            # 测试JOIN查询
            cursor.execute("""
                SELECT i.id, sp.name, i.quantity, i.min_quantity, i.location
                FROM inventory i
                JOIN spare_parts sp ON i.spare_part_id = sp.id
                LIMIT 3
            """)
            results = cursor.fetchall()
            print(f"JOIN查询测试成功，前3条记录:")
            for result in results:
                print(f"  {result}")
        
    except Exception as e:
        print(f"❌ 修复inventory数据时出错: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    fix_inventory_data()
