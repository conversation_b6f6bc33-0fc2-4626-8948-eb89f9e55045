#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试维保计划执行功能
"""

import requests
import json
from datetime import datetime

def test_execute_plan_function():
    """测试维保计划执行功能"""
    print("🧪 测试维保计划执行功能...")
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 1. 获取待执行的维保计划
        print("\n1️⃣ 获取待执行的维保计划...")
        response = requests.get(f"{base_url}/api/maintenance/plans?status=pending")
        
        if response.status_code != 200:
            print(f"❌ 获取维保计划失败: {response.status_code}")
            return False
        
        plans_data = response.json()
        if not plans_data.get('success'):
            print(f"❌ 获取维保计划失败: {plans_data.get('error')}")
            return False
        
        plans = plans_data['data']
        if not plans:
            print("⚠️  没有待执行的维保计划")
            return True
        
        print(f"✅ 找到 {len(plans)} 个待执行的维保计划")
        for i, plan in enumerate(plans[:3]):  # 只显示前3个
            print(f"   {i+1}. {plan['spare_part_name']} - {plan['area_name']} - {plan['equipment_name']}")
        
        # 2. 测试获取单个维保计划详情
        test_plan = plans[0]
        plan_id = test_plan['id']
        
        print(f"\n2️⃣ 测试获取维保计划详情 (ID: {plan_id})...")
        response = requests.get(f"{base_url}/api/maintenance/plan/{plan_id}")
        
        if response.status_code != 200:
            print(f"❌ 获取维保计划详情失败: {response.status_code}")
            return False
        
        plan_data = response.json()
        if not plan_data.get('success'):
            print(f"❌ 获取维保计划详情失败: {plan_data.get('error')}")
            return False
        
        plan_detail = plan_data['data']
        print(f"✅ 维保计划详情:")
        print(f"   零备件: {plan_detail['spare_part_name']}")
        print(f"   设备: {plan_detail['area_name']} - {plan_detail['equipment_name']}")
        print(f"   计划日期: {plan_detail['planned_date']}")
        print(f"   维保周期: {plan_detail['maintenance_cycle_months']}个月")
        
        # 3. 测试执行维保计划
        print(f"\n3️⃣ 测试执行维保计划...")
        execute_data = {
            "maintenance_date": datetime.now().strftime('%Y-%m-%d'),
            "technician": "测试维保员",
            "quantity_used": 1,
            "notes": "测试执行维保计划功能"
        }
        
        response = requests.post(
            f"{base_url}/api/maintenance/plan/{plan_id}/execute",
            headers={'Content-Type': 'application/json'},
            data=json.dumps(execute_data)
        )
        
        if response.status_code != 200:
            print(f"❌ 执行维保计划失败: {response.status_code}")
            if response.text:
                print(f"   错误信息: {response.text}")
            return False
        
        execute_result = response.json()
        if not execute_result.get('success'):
            print(f"❌ 执行维保计划失败: {execute_result.get('error')}")
            return False
        
        print(f"✅ 维保计划执行成功!")
        print(f"   消息: {execute_result.get('message')}")
        
        # 4. 验证执行结果
        print(f"\n4️⃣ 验证执行结果...")
        
        # 检查维保计划状态是否更新
        response = requests.get(f"{base_url}/api/maintenance/plan/{plan_id}")
        if response.status_code == 200:
            updated_plan = response.json()['data']
            if updated_plan['plan_status'] == 'completed':
                print("✅ 维保计划状态已更新为已完成")
            else:
                print(f"⚠️  维保计划状态: {updated_plan['plan_status']}")
        
        # 检查是否生成了维保记录
        response = requests.get(f"{base_url}/api/maintenance/status")
        if response.status_code == 200:
            records_data = response.json()
            if records_data.get('success'):
                records = records_data['data']
                # 查找刚创建的维保记录
                new_record = None
                for record in records:
                    if (record['spare_part_name'] == plan_detail['spare_part_name'] and 
                        record['technician'] == execute_data['technician']):
                        new_record = record
                        break
                
                if new_record:
                    print("✅ 维保记录已创建")
                    print(f"   维保日期: {new_record['maintenance_date']}")
                    print(f"   下次维保: {new_record['next_maintenance_date']}")
                else:
                    print("⚠️  未找到对应的维保记录")
        
        # 5. 检查库存是否扣减
        response = requests.get(f"{base_url}/api/inventory/status")
        if response.status_code == 200:
            inventory_data = response.json()
            if inventory_data.get('success'):
                inventory = inventory_data['data']
                for item in inventory:
                    if item['spare_part_name'] == plan_detail['spare_part_name']:
                        print(f"✅ 库存状态: {item['spare_part_name']} 当前库存 {item['total_quantity']}")
                        break
        
        print(f"\n🎉 维保计划执行功能测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

def main():
    """主函数"""
    print("🔧 维保计划执行功能测试")
    print("=" * 40)
    
    success = test_execute_plan_function()
    
    if success:
        print("\n✅ 维保计划执行功能测试通过!")
        print("现在可以在Web界面中正常使用维保计划执行功能")
    else:
        print("\n❌ 维保计划执行功能测试失败")
    
    return success

if __name__ == "__main__":
    main()
