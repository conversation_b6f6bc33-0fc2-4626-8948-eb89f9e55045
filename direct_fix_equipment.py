#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接修复设备名称重复问题
"""

import sqlite3

def main():
    print("=== 直接修复设备名称重复问题 ===")
    
    # 连接数据库
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 查看当前设备名称
        cursor.execute("SELECT id, area_id, name, type FROM equipment ORDER BY id")
        equipment = cursor.fetchall()
        
        print("当前设备列表:")
        for eq in equipment:
            print(f"ID: {eq[0]}, 区域ID: {eq[1]}, 名称: {eq[2]}, 类型: {eq[3]}")
        
        # 获取区域信息
        cursor.execute("SELECT id, name FROM areas")
        areas = cursor.fetchall()
        area_dict = {area[0]: area[1] for area in areas}
        
        print("\n区域信息:")
        for area in areas:
            print(f"ID: {area[0]}, 名称: {area[1]}")
        
        # 更新设备名称
        updates = []
        for eq in equipment:
            eq_id, area_id, current_name, eq_type = eq
            area_name = area_dict.get(area_id, "未知区域")
            
            # 根据设备类型和区域生成新名称
            if eq_type == "堆垛机":
                # 计算同区域同类型设备的序号
                cursor.execute("""
                    SELECT COUNT(*) FROM equipment 
                    WHERE area_id = ? AND type = ? AND id < ?
                """, (area_id, eq_type, eq_id))
                count = cursor.fetchone()[0]
                new_name = f"{area_name}{eq_type}{count + 1}号"
            elif eq_type == "入库皮带":
                new_name = f"{area_name}{eq_type}"
            elif eq_type == "AGV小车":
                new_name = f"{area_name}{eq_type}"
            else:
                new_name = f"{area_name}{eq_type}"
            
            if new_name != current_name:
                updates.append((new_name, eq_id))
                print(f"将更新设备 ID {eq_id}: '{current_name}' -> '{new_name}'")
        
        # 执行更新
        if updates:
            for new_name, eq_id in updates:
                cursor.execute("UPDATE equipment SET name = ? WHERE id = ?", (new_name, eq_id))
            
            conn.commit()
            print(f"\n✅ 成功更新了 {len(updates)} 个设备的名称")
            
            # 验证更新结果
            cursor.execute("SELECT id, area_id, name, type FROM equipment ORDER BY id")
            updated_equipment = cursor.fetchall()
            
            print("\n更新后的设备列表:")
            for eq in updated_equipment:
                area_name = area_dict.get(eq[1], "未知区域")
                print(f"ID: {eq[0]}, 区域: {area_name}, 名称: {eq[2]}, 类型: {eq[3]}")
        else:
            print("\n没有需要更新的设备名称")
        
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    main()
