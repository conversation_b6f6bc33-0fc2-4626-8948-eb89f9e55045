#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复数据库结构
"""

import sqlite3
import os

def fix_database():
    """修复数据库结构"""
    db_path = 'equipment_maintenance.db'
    
    if not os.path.exists(db_path):
        print("数据库文件不存在，请先运行系统初始化")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 检查maintenance_records表结构
        cursor.execute("PRAGMA table_info(maintenance_records)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"当前maintenance_records表的列: {columns}")
        
        if 'area_id' not in columns:
            print("需要添加area_id和equipment_id列...")
            
            # 备份数据
            cursor.execute("CREATE TABLE maintenance_records_backup AS SELECT * FROM maintenance_records")
            
            # 删除旧表
            cursor.execute("DROP TABLE maintenance_records")
            
            # 创建新表
            cursor.execute('''
                CREATE TABLE maintenance_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    spare_part_id INTEGER NOT NULL,
                    area_id INTEGER NOT NULL,
                    equipment_id INTEGER NOT NULL,
                    maintenance_date DATE NOT NULL,
                    next_maintenance_date DATE NOT NULL,
                    technician TEXT,
                    notes TEXT,
                    quantity_used INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (spare_part_id) REFERENCES spare_parts (id),
                    FOREIGN KEY (area_id) REFERENCES areas (id),
                    FOREIGN KEY (equipment_id) REFERENCES equipment (id)
                )
            ''')
            
            # 获取默认区域和设备ID
            cursor.execute("SELECT id FROM areas LIMIT 1")
            area_result = cursor.fetchone()
            default_area_id = area_result[0] if area_result else 1
            
            cursor.execute("SELECT id FROM equipment LIMIT 1")
            equipment_result = cursor.fetchone()
            default_equipment_id = equipment_result[0] if equipment_result else 1
            
            # 迁移数据
            cursor.execute('''
                INSERT INTO maintenance_records 
                (spare_part_id, area_id, equipment_id, maintenance_date, next_maintenance_date, technician, notes, quantity_used, created_at)
                SELECT 
                    spare_part_id, 
                    ? as area_id,
                    ? as equipment_id,
                    maintenance_date, 
                    next_maintenance_date, 
                    technician, 
                    notes,
                    1 as quantity_used,
                    created_at
                FROM maintenance_records_backup
            ''', (default_area_id, default_equipment_id))
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_maintenance_spare_part ON maintenance_records(spare_part_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_maintenance_area ON maintenance_records(area_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_maintenance_equipment ON maintenance_records(equipment_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_maintenance_next_date ON maintenance_records(next_maintenance_date)')
            
            # 删除备份表
            cursor.execute("DROP TABLE maintenance_records_backup")
            
            print("数据库结构修复完成！")
        else:
            print("数据库结构已经是最新的")
        
        conn.commit()
        
    except Exception as e:
        print(f"修复数据库时出错: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    fix_database()
