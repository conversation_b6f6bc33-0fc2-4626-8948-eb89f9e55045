#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_apis():
    """测试修复后的API数据一致性"""
    
    # 测试概览API
    print('=== 测试概览API ===')
    try:
        response = requests.get('http://127.0.0.1:5000/api/overview')
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                overview = data['data']
                print(f'零备件总数: {overview.get("total_parts", 0)}')
                print(f'逾期维保: {overview.get("overdue_maintenance", 0)}')
                print(f'即将到期: {overview.get("upcoming_maintenance", 0)}')
                print(f'库存不足: {overview.get("low_stock", 0)}')
                
                # 保存概览数据用于对比
                overview_overdue = overview.get("overdue_maintenance", 0)
                overview_upcoming = overview.get("upcoming_maintenance", 0)
            else:
                print(f'API错误: {data.get("error")}')
                return
        else:
            print(f'HTTP错误: {response.status_code}')
            return
    except Exception as e:
        print(f'请求失败: {e}')
        return

    # 测试维保状态API
    print('\n=== 测试维保状态API ===')
    try:
        response = requests.get('http://127.0.0.1:5000/api/maintenance/status?per_page=100')
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                records = data['data']
                print(f'维保记录总数: {len(records)}')
                
                # 统计状态
                status_count = {}
                for record in records:
                    status = record['status']
                    status_count[status] = status_count.get(status, 0) + 1
                
                print('状态统计:')
                detail_overdue = 0
                detail_upcoming = 0
                
                for status, count in status_count.items():
                    status_text = {
                        'overdue': '逾期',
                        'warning': '即将到期', 
                        'normal': '正常',
                        'no_record': '无记录'
                    }.get(status, status)
                    print(f'  {status_text}: {count}')
                    
                    if status == 'overdue':
                        detail_overdue = count
                    elif status == 'warning':
                        detail_upcoming = count
                
                # 数据一致性检查
                print('\n=== 数据一致性检查 ===')
                print(f'概览逾期数: {overview_overdue} vs 详情逾期数: {detail_overdue}')
                print(f'概览即将到期数: {overview_upcoming} vs 详情即将到期数: {detail_upcoming}')
                
                if overview_overdue == detail_overdue and overview_upcoming == detail_upcoming:
                    print('✅ 数据一致性检查通过！')
                else:
                    print('❌ 数据不一致！')
                    
            else:
                print(f'API错误: {data.get("error")}')
        else:
            print(f'HTTP错误: {response.status_code}')
    except Exception as e:
        print(f'请求失败: {e}')

if __name__ == '__main__':
    test_apis()
