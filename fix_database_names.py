#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新创建数据库，修复设备名称重复问题
"""

import os
import sqlite3
from database import DatabaseManager

def main():
    print("=== 重新创建数据库，修复设备名称重复问题 ===")
    
    # 删除现有数据库
    if os.path.exists('equipment_maintenance.db'):
        os.remove('equipment_maintenance.db')
        print("✅ 已删除现有数据库")
    
    # 创建新的数据库管理器（会自动创建表和插入初始数据）
    try:
        db_manager = DatabaseManager()
        print("✅ 数据库重新创建完成")
        
        # 验证设备名称是否唯一
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT e.id, a.name as area_name, e.name, e.type 
            FROM equipment e 
            JOIN areas a ON e.area_id = a.id 
            ORDER BY e.id
        """)
        equipment = cursor.fetchall()
        
        print("\n新的设备列表:")
        for eq in equipment:
            print(f"ID: {eq[0]}, 区域: {eq[1]}, 名称: {eq[2]}, 类型: {eq[3]}")
        
        # 检查是否有重复名称
        cursor.execute("SELECT name, COUNT(*) FROM equipment GROUP BY name HAVING COUNT(*) > 1")
        duplicates = cursor.fetchall()
        
        if duplicates:
            print("\n❌ 仍然发现重复的设备名称:")
            for dup in duplicates:
                print(f"名称: {dup[0]}, 重复次数: {dup[1]}")
        else:
            print("\n✅ 所有设备名称都是唯一的")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 重新创建数据库时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
