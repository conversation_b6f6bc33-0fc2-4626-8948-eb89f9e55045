# 设备零备件周期管理系统

## 📋 项目概述

这是一个专业的设备零备件维保周期管理系统，旨在帮助企业高效管理三大生产区域（成品区、辅料区、片烟区）的设备零备件维保计划和库存状态。系统提供智能预警、可视化图表和完整的维保记录管理功能。

## 🌟 核心特性

### 🏭 三大区域管理
- **成品区**：4台堆垛机 + 1条入库皮带
- **辅料区**：3台堆垛机 + 1台AGV小车
- **片烟区**：3台堆垛机

### 🔧 设备零备件体系
- **堆垛机**：主接触器(6个月)、变频器(12个月)、传感器(3个月)
- **入库皮带**：皮带(3个月)、滚筒(6个月)
- **AGV小车**：保险丝(6个月)、电池(12个月)、轮子(9个月)

### ⚠️ 智能预警系统
- 🔴 **紧急警告**：维保时间剩余≤1个月时自动报警
- 🟡 **提前提醒**：即将到期的维保项目黄色标识
- 📦 **库存监控**：自动检查零备件库存状态
- 📊 **可视化进度**：横向条形图显示维保进度百分比

### 📈 数据可视化
- 翻转90°的条形图展示维保进度
- 颜色编码：绿色(正常) → 橙色(警告) → 红色(紧急)
- 实时进度百分比显示
- 区域统计分析图表

## 🏗️ 系统架构

### 数据库设计
```
区域 (areas)
├── 成品区、辅料区、片烟区
│
设备 (equipment)
├── 堆垛机、入库皮带、AGV小车
│
零备件 (spare_parts)
├── 主接触器、保险丝、传感器等
│   └── 维保周期配置（固定周期：3/6/9/12个月）
│
库存管理 (inventory) - 全局统一管理
├── 零件总库存数量（不区分设备/区域）
├── 最低库存预警阈值
└── 存放位置管理
│
维保记录 (maintenance_records)
├── 首次维保：手动建立维保单
├── 维保完成日期记录
├── 下次维保时间 = 维保完成时间 + 维保周期
├── 维保人员信息
└── 维保备注说明
```

### 层级关系
```
区域 → 设备 → 零备件
  ↓      ↓      ↓
成品区 → 堆垛机 → 主接触器(6个月维保周期)
辅料区 → AGV小车 → 保险丝(6个月维保周期)
片烟区 → 堆垛机 → 传感器(3个月维保周期)
```

## 🚀 版本说明

本项目提供两个版本：

### 🖥️ 桌面版 (tkinter)
- 基于Python tkinter的图形界面
- 适合单机使用，操作简单直观
- 启动方式：`python main_gui.py`

### 🌐 Web版 (Flask)
- 基于Flask + Bootstrap的现代化Web界面
- 支持多用户访问，响应式设计
- 启动方式：`python app.py`
- 访问地址：http://127.0.0.1:5000

## 📊 主要功能模块

### 1. 系统概览
- 实时统计面板：零备件总数、逾期维保、即将到期、库存不足
- 维保状态列表：显示所有零备件的详细状态
- 可视化图表：横向进度条、区域分布图、状态分析图

### 2. 维保管理
- **首次维保建立**：系统启动时手动创建第一次维保单
- **维保提醒列表**：可按时间范围筛选(7天/15天/30天/60天)
- **添加维保记录**：选择零备件、设置维保完成日期、记录维保人员
- **自动计算下次维保**：维保完成时间 + 维保周期 = 下次维保时间
- **维保历史查询**：完整的维保记录追踪

### 3. 库存管理（全局统一）
- **库存统计**：只统计零件总数量，不区分使用设备或区域
- **库存状态监控**：实时显示全局库存数量和状态
- **库存更新功能**：在线修改库存数量和存放位置
- **缺货提醒**：库存=0时自动警告
- **库存不足预警**：低于最低库存线时提醒

### 4. 数据管理
- 区域管理：查看和管理三大区域信息
- 设备管理：查看各区域下的设备列表
- 零备件管理：查看零备件详细信息和维保周期
- 关系绑定：新增零备件时自动绑定区域和设备关系

## 🛠️ 技术栈

### 后端技术
- **Python 3.7+**：主要开发语言
- **Flask**：轻量级Web框架
- **SQLite**：嵌入式数据库，无需额外配置
- **RESTful API**：标准化接口设计

### 前端技术
- **Bootstrap 5**：现代化UI框架，响应式设计
- **Chart.js**：强大的图表库，支持多种图表类型
- **jQuery**：简化JavaScript操作
- **Font Awesome**：丰富的图标库

### 桌面版技术
- **tkinter**：Python内置GUI库
- **matplotlib**：数据可视化图表库
- **numpy**：数值计算支持

## 🚀 快速开始

### 环境要求
- Python 3.7 或更高版本
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 方式一：一键启动（推荐）
```bash
# 使用快速启动脚本（自动检查环境和依赖）
python start.py
```

### 方式二：自动部署脚本
```bash
# Linux/Mac用户
chmod +x deploy.sh
./deploy.sh

# Windows用户
deploy.bat
```

### 方式三：手动安装
#### 1. 克隆项目
```bash
git clone <repository-url>
cd QuanShengMing02
```

#### 2. 安装依赖
```bash
pip install -r requirements.txt
```

#### 3. 初始化数据库
```bash
python initialize_system.py
```

#### 4. 启动系统
**Web版本（推荐）**
```bash
python app.py
```
然后在浏览器中访问：http://127.0.0.1:5000

**桌面版本**
```bash
python main_gui.py
```

### 方式四：Docker部署
```bash
# 使用docker-compose
docker-compose up -d

# 或直接使用Docker
docker build -t maintenance-system .
docker run -p 5000:5000 maintenance-system
```

## 📱 界面预览

### Web版界面特色
- **系统概览页面**：顶部统计卡片 + 维保状态表格 + 进度图表
- **维保管理页面**：维保提醒列表 + 时间筛选 + 添加维保记录
- **库存管理页面**：库存状态表格 + 统计面板 + 在线更新功能
- **数据管理页面**：折叠式数据展示 + 添加数据功能

### 桌面版界面特色
- 简洁直观的tkinter界面
- 实时数据刷新
- 本地图表显示
- 快速操作按钮

## 📊 API接口文档

### 主要API端点
```
GET  /api/overview          - 获取系统概览数据
GET  /api/maintenance/alerts - 获取维保提醒
POST /api/maintenance/record - 添加维保记录
GET  /api/inventory/status   - 获取库存状态
POST /api/inventory/update   - 更新库存
GET  /api/statistics        - 获取统计数据
GET  /api/areas             - 获取区域信息
GET  /api/spare-parts       - 获取零备件列表
```

### 数据格式
所有API返回标准JSON格式：
```json
{
    "success": true,
    "data": {...},
    "error": "错误信息"
}
```

## 🎯 使用指南

### 日常操作流程
1. **每日检查**：打开系统概览页面，查看红色警告项目
2. **维保执行**：完成维保后，在维保管理页面添加记录
3. **库存监控**：定期检查库存状态，及时补充不足的零备件
4. **数据维护**：根据需要添加新的区域、设备或零备件

### 预警处理
- 🔴 **红色警告**：立即处理，检查库存并安排维保
- 🟡 **黄色提醒**：提前准备，确保零备件库存充足
- 📦 **库存不足**：及时采购补充库存

### 最佳实践
1. 定期备份数据库文件 `maintenance_system.db`
2. 维保完成后立即更新系统记录
3. 保持库存信息的实时准确性
4. 利用图表分析功能优化维保计划

## 🔧 配置说明

### 数据库配置
- 数据库文件：`maintenance_system.db`
- 自动创建表结构和索引
- 支持数据备份和恢复

### Web服务配置
- 默认端口：5000
- 调试模式：开发时启用
- 支持局域网访问

### 安全配置
- Session密钥：可在app.py中修改
- 数据验证：输入数据自动验证
- 错误处理：完善的异常处理机制

## 📈 性能优化

### 数据库优化
- 关键字段建立索引
- 使用JOIN减少查询次数
- 及时关闭数据库连接

### 前端优化
- CDN加速加载外部资源
- 图片和图标优化
- 合理使用浏览器缓存

## 🔄 系统维护

### 数据备份
```bash
# 备份数据库
cp maintenance_system.db backup/maintenance_system_$(date +%Y%m%d).db
```

### 日志管理
- Flask自带日志系统
- 可配置日志级别和输出文件
- 支持日志轮转和清理

## 🚀 部署建议

### 开发环境
- 使用Flask内置服务器
- 启用调试模式便于开发

### 生产环境
- 使用Gunicorn或uWSGI
- 配置Nginx反向代理
- 启用HTTPS加密传输

### 容器化部署
- 支持Docker容器化
- 可使用docker-compose编排
- 便于扩展和维护

## 📞 技术支持

如有问题或需要功能扩展，请联系开发团队。

## 📄 许可证

本项目采用 MIT 许可证，详情请参阅 LICENSE 文件。

---

**版本**: 1.0
**开发时间**: 2025年7月
**技术栈**: Python + Flask + SQLite + Bootstrap + Chart.js
**访问地址**: http://127.0.0.1:5000

## 📁 项目文件说明

### 核心文件
- `start.py` - 🚀 **快速启动脚本**（推荐使用）
- `app.py` - 🌐 Flask Web应用主文件
- `main_gui.py` - 🖥️ tkinter桌面版主文件
- `database.py` - 🗄️ 数据库管理模块
- `maintenance_manager.py` - 🔧 维保管理业务逻辑

### 部署文件
- `deploy.sh` - 🐧 Linux/Mac自动部署脚本
- `deploy.bat` - 🪟 Windows自动部署脚本
- `Dockerfile` - 🐳 Docker容器配置
- `docker-compose.yml` - 🐳 Docker编排配置

### 配置文件
- `requirements.txt` - 📦 Python依赖包列表
- `initialize_system.py` - 🔧 系统初始化脚本

### 测试文件
- `test_system.py` - 🧪 系统功能测试
- `test_web_api.py` - 🧪 Web API测试

## 📝 需求记录与更新

### 最新需求更新 (2025-07-19)

#### 🔧 数据库设计优化需求
1. **库存统计简化**：
   - 库存统计只统计零件的数量，不区分用在哪个设备或区域
   - 零件库存是全局统一管理，不需要按设备或区域分别统计
   - 简化库存管理逻辑，提高系统效率

2. **维保流程优化**：
   - **首次维保**：系统启动时，用户需要手动建立第一次维保单
   - **后续维保**：完成维保后，系统根据维保结束时间 + 维保周期自动计算下一次维保时间
   - **维保周期**：每种零件都有固定的维保周期（如：主接触器6个月，传感器3个月等）

3. **状态展示完整性**：
   - 数据库中包含的所有状态都要在界面中体现
   - 确保系统功能的完整性和展示效果
   - 所有预警、提醒、统计功能都要正常工作

#### 🎯 后续需求记录
*此处将记录用户提出的新需求和功能改进建议*

---

## 🔗 相关文档

- [📖 Web版本详细说明](Web版本说明.md)
- [📖 使用说明文档](使用说明.md)
- [📖 项目结构说明](项目结构说明.md)
- [🔧 提示词文档](提示词.txt)