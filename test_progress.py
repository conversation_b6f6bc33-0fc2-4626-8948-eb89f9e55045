#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试维保进度计算逻辑
"""

import requests
import json
from datetime import datetime, timedelta

def test_progress_calculation():
    """测试进度计算逻辑"""
    try:
        # 获取维保状态数据
        response = requests.get('http://127.0.0.1:5000/api/maintenance/status')
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                records = data.get('data', [])
                print(f"获取到 {len(records)} 条维保记录")
                print("\n=== 维保进度分析 ===")
                
                # 分析不同状态的进度
                status_groups = {
                    'overdue': [],
                    'warning': [],
                    'normal': [],
                    'no_record': []
                }
                
                for record in records:
                    status = record.get('status', 'unknown')
                    if status in status_groups:
                        status_groups[status].append(record)
                
                # 显示各状态组的进度情况
                for status, group in status_groups.items():
                    if group:
                        print(f"\n--- {status.upper()} 状态 ({len(group)}条) ---")
                        for i, record in enumerate(group[:5]):  # 只显示前5条
                            progress = record.get('progress', 0)
                            days_remaining = record.get('days_remaining')
                            cycle_months = record.get('maintenance_cycle_months', 0)
                            
                            print(f"  {i+1}. {record.get('spare_part_name', '未知零件')}")
                            print(f"     区域: {record.get('area_name', '未知')}")
                            print(f"     设备: {record.get('equipment_name', '未知')}")
                            print(f"     维保周期: {cycle_months}个月")
                            print(f"     剩余天数: {days_remaining}天" if days_remaining is not None else "     剩余天数: 无记录")
                            print(f"     进度: {progress}%")
                            print(f"     状态: {record.get('status_text', '未知')}")
                            
                            # 验证进度逻辑
                            if status == 'overdue' and progress < 100:
                                print(f"     ❌ 错误：逾期状态进度应该 ≥100%，实际为 {progress}%")
                            elif status in ['warning', 'normal'] and (progress < 0 or progress > 100):
                                if days_remaining and days_remaining >= 0:
                                    print(f"     ❌ 错误：正常状态进度应该在0-100%之间，实际为 {progress}%")
                            else:
                                print(f"     ✅ 进度计算正确")
                            print()
                
                # 计算一些示例来验证逻辑
                print("\n=== 进度计算逻辑验证 ===")
                today = datetime.now().date()
                
                test_cases = [
                    {"name": "刚维保完", "cycle_months": 3, "days_remaining": 90, "expected_progress": 0},
                    {"name": "周期一半", "cycle_months": 3, "days_remaining": 45, "expected_progress": 50},
                    {"name": "即将到期", "cycle_months": 3, "days_remaining": 5, "expected_progress": 94.4},
                    {"name": "刚好到期", "cycle_months": 3, "days_remaining": 0, "expected_progress": 100},
                    {"name": "逾期5天", "cycle_months": 3, "days_remaining": -5, "expected_progress": 105.6},
                    {"name": "逾期30天", "cycle_months": 3, "days_remaining": -30, "expected_progress": 133.3},
                ]
                
                for case in test_cases:
                    cycle_days = case["cycle_months"] * 30
                    days_passed = cycle_days - case["days_remaining"]
                    calculated_progress = (days_passed / cycle_days) * 100
                    
                    print(f"{case['name']}:")
                    print(f"  周期: {case['cycle_months']}个月 ({cycle_days}天)")
                    print(f"  剩余天数: {case['days_remaining']}天")
                    print(f"  已过天数: {days_passed}天")
                    print(f"  计算进度: {calculated_progress:.1f}%")
                    print(f"  期望进度: {case['expected_progress']}%")
                    print(f"  结果: {'✅ 正确' if abs(calculated_progress - case['expected_progress']) < 1 else '❌ 错误'}")
                    print()
                    
            else:
                print(f"API错误: {data.get('error')}")
        else:
            print(f"HTTP错误: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"测试异常: {e}")

if __name__ == "__main__":
    test_progress_calculation()
