import requests

try:
    response = requests.get('http://127.0.0.1:5000/api/maintenance/alerts?days=30')
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"成功: {data.get('success')}")
        
        if data.get('success'):
            alerts = data.get('data', [])
            print(f"维保提醒数量: {len(alerts)}")
            
            print("\n维保提醒列表:")
            for i, alert in enumerate(alerts):
                spare_part = alert.get('spare_part_name', '未知')
                area = alert.get('area_name', '未知')
                equipment = alert.get('equipment_name', '未知')
                days = alert.get('days_remaining')
                priority = alert.get('priority_text', '未知')
                
                if days is not None:
                    if days < 0:
                        status = f"逾期{abs(days)}天"
                    else:
                        status = f"还剩{days}天"
                else:
                    status = "无记录"
                
                print(f"{i+1:2d}. {spare_part:12s} - {area:6s} - {equipment:12s} - {priority:4s} - {status}")
        else:
            print(f"API错误: {data.get('error')}")
    else:
        print(f"HTTP错误: {response.text}")
        
except Exception as e:
    print(f"请求异常: {e}")
