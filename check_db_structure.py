#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def check_database_structure():
    """检查数据库结构"""
    db_path = 'equipment_maintenance.db'
    
    if not os.path.exists(db_path):
        print("数据库文件不存在")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print("数据库中的表:")
        for table in tables:
            print(f"  - {table[0]}")
        
        # 检查maintenance_records表结构
        print("\n=== maintenance_records表结构 ===")
        try:
            cursor.execute("PRAGMA table_info(maintenance_records)")
            columns = cursor.fetchall()
            for col in columns:
                print(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else ''} {'DEFAULT ' + str(col[4]) if col[4] else ''}")
        except Exception as e:
            print(f"检查maintenance_records表出错: {e}")
        
        # 检查数据数量
        print("\n=== 数据数量 ===")
        try:
            cursor.execute("SELECT COUNT(*) FROM maintenance_records")
            count = cursor.fetchone()[0]
            print(f"maintenance_records: {count} 条记录")
            
            if count > 0:
                cursor.execute("SELECT * FROM maintenance_records LIMIT 3")
                records = cursor.fetchall()
                print("前3条记录:")
                for i, record in enumerate(records, 1):
                    print(f"  记录{i}: {record}")
        except Exception as e:
            print(f"查询数据出错: {e}")
        
        # 检查其他相关表
        for table_name in ['spare_parts', 'areas', 'equipment', 'inventory']:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"{table_name}: {count} 条记录")
            except Exception as e:
                print(f"查询{table_name}表出错: {e}")
                
    finally:
        conn.close()

if __name__ == "__main__":
    check_database_structure()
