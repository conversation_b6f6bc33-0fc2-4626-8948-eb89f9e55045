#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单修复维保记录重复问题
"""

import sqlite3
import os

def fix_duplicates():
    """修复维保记录重复问题"""
    print("=== 修复维保记录重复问题 ===")
    
    db_path = 'equipment_maintenance.db'
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查重复情况
        print("1. 检查重复的维保记录...")
        cursor.execute("""
            SELECT 
                area_id,
                equipment_id, 
                spare_part_id,
                COUNT(*) as count
            FROM maintenance_records
            GROUP BY area_id, equipment_id, spare_part_id
            HAVING COUNT(*) > 1
            ORDER BY count DESC
        """)
        
        duplicates = cursor.fetchall()
        
        if not duplicates:
            print("✅ 没有发现重复的维保记录")
            return True
        
        print(f"发现 {len(duplicates)} 组重复的维保记录:")
        
        total_removed = 0
        
        # 处理每组重复记录
        for area_id, equipment_id, spare_part_id, count in duplicates:
            print(f"\n处理重复组: 区域ID={area_id}, 设备ID={equipment_id}, 零件ID={spare_part_id}, 重复数={count}")
            
            # 获取这组重复记录
            cursor.execute("""
                SELECT id, maintenance_date, created_at
                FROM maintenance_records
                WHERE area_id = ? AND equipment_id = ? AND spare_part_id = ?
                ORDER BY 
                    CASE WHEN maintenance_date IS NOT NULL THEN maintenance_date ELSE '1900-01-01' END DESC,
                    CASE WHEN created_at IS NOT NULL THEN created_at ELSE '1900-01-01 00:00:00' END DESC,
                    id DESC
            """, (area_id, equipment_id, spare_part_id))
            
            records = cursor.fetchall()
            
            if len(records) <= 1:
                continue
            
            # 保留第一条记录，删除其他的
            keep_record = records[0]
            remove_records = records[1:]
            
            print(f"  保留记录: ID={keep_record[0]}")
            
            for record in remove_records:
                print(f"  删除记录: ID={record[0]}")
                cursor.execute("DELETE FROM maintenance_records WHERE id = ?", (record[0],))
                total_removed += 1
        
        # 提交更改
        conn.commit()
        print(f"\n✅ 修复完成！共删除了 {total_removed} 条重复记录")
        
        # 验证修复结果
        cursor.execute("""
            SELECT 
                area_id,
                equipment_id, 
                spare_part_id,
                COUNT(*) as count
            FROM maintenance_records
            GROUP BY area_id, equipment_id, spare_part_id
            HAVING COUNT(*) > 1
        """)
        
        remaining_duplicates = cursor.fetchall()
        
        if remaining_duplicates:
            print(f"❌ 仍有 {len(remaining_duplicates)} 组重复记录")
        else:
            print("✅ 所有重复记录已清理完毕")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        return False

if __name__ == "__main__":
    fix_duplicates()
    print("\n🎉 维保记录重复问题修复完成！")
