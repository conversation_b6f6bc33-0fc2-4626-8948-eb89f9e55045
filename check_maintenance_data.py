#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime, timed<PERSON><PERSON>

def check_maintenance_data():
    """检查维保数据"""
    
    conn = sqlite3.connect('equipment_maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        print("=== 检查维保数据 ===")
        
        # 检查维保记录表
        cursor.execute('SELECT COUNT(*) as count FROM maintenance_records')
        count = cursor.fetchone()['count']
        print(f"维保记录总数: {count}")
        
        if count > 0:
            # 查看所有维保记录
            cursor.execute('''
                SELECT mr.id, mr.equipment_id, mr.spare_part_id, mr.maintenance_date, 
                       mr.next_maintenance_date, mr.description, mr.notes,
                       e.name as equipment_name, sp.name as spare_part_name
                FROM maintenance_records mr
                LEFT JOIN equipment e ON mr.equipment_id = e.id
                LEFT JOIN spare_parts sp ON mr.spare_part_id = sp.id
                ORDER BY mr.id
            ''')
            records = cursor.fetchall()
            
            print(f"\n维保记录详情:")
            for record in records:
                print(f"  ID: {record['id']}")
                print(f"    设备: {record['equipment_name']} (ID: {record['equipment_id']})")
                print(f"    零备件: {record['spare_part_name']} (ID: {record['spare_part_id']})")
                print(f"    维保日期: {record['maintenance_date']}")
                print(f"    下次维保: {record['next_maintenance_date']}")
                print(f"    描述: {record['description']}")
                print(f"    备注: {record['notes']}")
                print()
        
        # 检查维保状态分布
        today = datetime.now().date()
        print(f"今天日期: {today}")
        
        cursor.execute('''
            SELECT 
                CASE 
                    WHEN mr.next_maintenance_date < ? THEN 'overdue'
                    WHEN mr.next_maintenance_date <= date(?, '+30 days') THEN 'warning'
                    ELSE 'normal'
                END as status,
                COUNT(*) as count
            FROM maintenance_records mr
            WHERE mr.next_maintenance_date IS NOT NULL
            GROUP BY status
        ''', (today, today))
        
        status_stats = cursor.fetchall()
        print(f"\n维保状态统计:")
        for stat in status_stats:
            status_name = {
                'overdue': '已过期',
                'warning': '即将到期', 
                'normal': '正常'
            }.get(stat['status'], stat['status'])
            print(f"  {status_name}: {stat['count']} 条记录")
        
        # 检查区域维保统计
        cursor.execute('''
            SELECT a.name as area_name, COUNT(mr.id) as maintenance_count
            FROM areas a
            LEFT JOIN equipment e ON a.id = e.area_id
            LEFT JOIN maintenance_records mr ON e.id = mr.equipment_id
            GROUP BY a.id, a.name
            ORDER BY a.name
        ''')
        
        area_stats = cursor.fetchall()
        print(f"\n区域维保统计:")
        for stat in area_stats:
            print(f"  {stat['area_name']}: {stat['maintenance_count']} 条维保记录")
        
        # 检查设备和零备件数据
        cursor.execute('SELECT COUNT(*) as count FROM equipment')
        equipment_count = cursor.fetchone()['count']
        print(f"\n设备总数: {equipment_count}")
        
        cursor.execute('SELECT COUNT(*) as count FROM spare_parts')
        parts_count = cursor.fetchone()['count']
        print(f"零备件总数: {parts_count}")
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(maintenance_records)")
        columns = cursor.fetchall()
        print(f"\nmaintenance_records表结构:")
        for col in columns:
            print(f"  {col['name']}: {col['type']}")
        
    except Exception as e:
        print(f"❌ 检查维保数据时出错: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        conn.close()

if __name__ == "__main__":
    check_maintenance_data()
