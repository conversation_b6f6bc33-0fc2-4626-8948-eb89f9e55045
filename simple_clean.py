#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime, timedelta
import random

def simple_clean():
    """简单清理逾期维保记录"""
    try:
        conn = sqlite3.connect('equipment_maintenance.db')
        cursor = conn.cursor()
        
        print("开始清理逾期维保记录...")
        
        # 查找所有逾期记录
        cursor.execute("""
            SELECT id, next_maintenance_date
            FROM maintenance_records 
            WHERE DATE(next_maintenance_date) < DATE('now')
            ORDER BY next_maintenance_date DESC
        """)
        
        overdue_records = cursor.fetchall()
        print(f"找到 {len(overdue_records)} 条逾期记录")
        
        if len(overdue_records) <= 2:
            print("逾期记录数量已经符合要求")
            return
        
        # 保留最近的2条，删除其他的
        keep_ids = [record[0] for record in overdue_records[:2]]
        delete_ids = [record[0] for record in overdue_records[2:]]
        
        print(f"保留 {len(keep_ids)} 条记录")
        print(f"删除 {len(delete_ids)} 条记录")
        
        # 执行删除
        if delete_ids:
            placeholders = ','.join(['?' for _ in delete_ids])
            cursor.execute(f"DELETE FROM maintenance_records WHERE id IN ({placeholders})", delete_ids)
            print(f"已删除 {cursor.rowcount} 条记录")
        
        # 调整剩余记录的时间分布
        cursor.execute("""
            SELECT mr.id, sp.maintenance_cycle_months
            FROM maintenance_records mr
            JOIN spare_parts sp ON mr.spare_part_id = sp.id
        """)
        
        all_records = cursor.fetchall()
        today = datetime.now().date()
        
        updates = []
        for record_id, cycle_months in all_records:
            # 随机生成过去的维保日期
            days_ago = random.randint(1, 180)
            maintenance_date = today - timedelta(days=days_ago)
            next_maintenance_date = maintenance_date + timedelta(days=cycle_months * 30)
            
            updates.append((
                maintenance_date.strftime('%Y-%m-%d'),
                next_maintenance_date.strftime('%Y-%m-%d'),
                record_id
            ))
        
        # 批量更新时间
        cursor.executemany("""
            UPDATE maintenance_records 
            SET maintenance_date = ?, next_maintenance_date = ?
            WHERE id = ?
        """, updates)
        
        print(f"更新了 {len(updates)} 条记录的时间")
        
        conn.commit()
        
        # 验证结果
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN DATE(next_maintenance_date) < DATE('now') THEN 1 ELSE 0 END) as overdue
            FROM maintenance_records
        """)
        
        result = cursor.fetchone()
        print(f"清理完成！总记录: {result[0]}, 逾期记录: {result[1]}")
        
        conn.close()
        
    except Exception as e:
        print(f"清理失败: {e}")

if __name__ == "__main__":
    simple_clean()
