#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    print("开始创建多样化测试数据...")
    from create_diverse_test_data import create_diverse_test_data
    create_diverse_test_data()
    
    print("\n开始验证数据...")
    from verify_data import verify_data
    verify_data()
    
except Exception as e:
    print(f"执行过程中出错: {e}")
    import traceback
    traceback.print_exc()
