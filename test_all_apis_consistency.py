#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_all_apis_consistency():
    """测试所有API端点的数据一致性"""
    
    print("=== 全面API数据一致性测试 ===\n")
    
    # 1. 测试概览API
    print("1. 测试概览API (/api/overview)")
    try:
        response = requests.get('http://127.0.0.1:5000/api/overview')
        if response.status_code == 200:
            overview_data = response.json()
            if overview_data.get('success'):
                overview = overview_data['data']
                print(f"   零备件总数: {overview.get('total_parts', 0)}")
                print(f"   逾期维保: {overview.get('overdue_maintenance', 0)}")
                print(f"   即将到期: {overview.get('upcoming_maintenance', 0)}")
                print(f"   库存不足: {overview.get('low_stock', 0)}")
                
                # 保存数据用于对比
                overview_overdue = overview.get('overdue_maintenance', 0)
                overview_upcoming = overview.get('upcoming_maintenance', 0)
            else:
                print(f"   ❌ API错误: {overview_data.get('error')}")
                return
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
        return
    
    # 2. 测试维保状态API
    print("\n2. 测试维保状态API (/api/maintenance/status)")
    try:
        response = requests.get('http://127.0.0.1:5000/api/maintenance/status?per_page=100')
        if response.status_code == 200:
            status_data = response.json()
            if status_data.get('success'):
                records = status_data['data']
                print(f"   维保记录总数: {len(records)}")
                
                # 统计各种状态
                status_count = {}
                for record in records:
                    status = record.get('status', 'unknown')
                    status_count[status] = status_count.get(status, 0) + 1
                
                print("   状态统计:")
                for status, count in status_count.items():
                    status_text = {
                        'normal': '正常',
                        'warning': '即将到期',
                        'overdue': '逾期',
                        'no_record': '无记录'
                    }.get(status, status)
                    print(f"     {status_text}: {count}")
                
                # 保存数据用于对比
                status_overdue = status_count.get('overdue', 0)
                status_upcoming = status_count.get('warning', 0)
            else:
                print(f"   ❌ API错误: {status_data.get('error')}")
                return
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
        return
    
    # 3. 测试统计API
    print("\n3. 测试统计API (/api/statistics)")
    try:
        response = requests.get('http://127.0.0.1:5000/api/statistics')
        if response.status_code == 200:
            stats_data = response.json()
            if stats_data.get('success'):
                stats = stats_data['data']
                
                # 区域统计
                area_stats = stats.get('area_stats', [])
                print(f"   区域统计 ({len(area_stats)} 个区域):")
                total_area_records = 0
                for area in area_stats:
                    count = area.get('count', 0)
                    print(f"     {area.get('area', '未知')}: {count} 条维保记录")
                    total_area_records += count
                
                # 状态统计
                status_stats = stats.get('status_stats', [])
                print(f"   维保状态统计 ({len(status_stats)} 种状态):")
                stats_overdue = 0
                stats_upcoming = 0
                total_stats_records = 0
                for status in status_stats:
                    status_name = status.get('status', '未知')
                    count = status.get('count', 0)
                    status_text = {
                        'overdue': '逾期',
                        'warning': '即将到期', 
                        'normal': '正常'
                    }.get(status_name, status_name)
                    print(f"     {status_text}: {count} 条记录")
                    
                    if status_name == 'overdue':
                        stats_overdue = count
                    elif status_name == 'warning':
                        stats_upcoming = count
                    
                    total_stats_records += count
                
                print(f"   统计API记录总数: {total_stats_records}")
            else:
                print(f"   ❌ API错误: {stats_data.get('error')}")
                return
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
        return
    
    # 4. 数据一致性检查
    print("\n=== 数据一致性检查 ===")
    
    # 检查逾期数据一致性
    print(f"逾期维保数据对比:")
    print(f"  概览API: {overview_overdue}")
    print(f"  状态API: {status_overdue}")
    print(f"  统计API: {stats_overdue}")
    
    if overview_overdue == status_overdue == stats_overdue:
        print("  ✅ 逾期数据一致")
    else:
        print("  ❌ 逾期数据不一致")
    
    # 检查即将到期数据一致性
    print(f"\n即将到期数据对比:")
    print(f"  概览API: {overview_upcoming}")
    print(f"  状态API: {status_upcoming}")
    print(f"  统计API: {stats_upcoming}")
    
    if overview_upcoming == status_upcoming == stats_upcoming:
        print("  ✅ 即将到期数据一致")
    else:
        print("  ❌ 即将到期数据不一致")
    
    # 总结
    if (overview_overdue == status_overdue == stats_overdue and 
        overview_upcoming == status_upcoming == stats_upcoming):
        print("\n🎉 所有API数据一致性检查通过！")
        return True
    else:
        print("\n❌ 存在数据不一致问题")
        return False

if __name__ == "__main__":
    test_all_apis_consistency()
