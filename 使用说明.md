# 设备零备件周期管理系统 - 使用说明

## 🚀 快速开始

### 启动系统
1. **推荐方式**: 双击运行 `start.py` 或在命令行执行 `python start.py`
2. **Windows用户**: 双击运行 `deploy.bat` 批处理文件
3. **手动启动**: 先运行 `python initialize_system.py` 初始化，再运行 `python app.py`

### 访问系统
- 启动成功后，在浏览器中访问: `http://127.0.0.1:5000`
- 推荐使用Chrome、Firefox、Edge等现代浏览器
- 支持手机、平板等移动设备访问

## 📋 功能模块详解

### 1. 系统概览 📊
**功能**: 查看系统整体运行状态和关键指标

#### 主要内容
- **统计卡片**: 显示零备件总数、逾期维保、即将到期、库存不足等关键指标
- **维保进度图表**: 横向条形图展示各零备件的维保进度百分比
- **区域分布图**: 饼图显示各区域零备件分布情况
- **维保状态表**: 详细列表显示所有零备件的维保状态

#### 操作说明
- 点击"刷新数据"按钮更新最新数据
- 表格支持滚动查看更多内容
- 图表支持鼠标悬停查看详细信息

#### 状态说明
- 🔴 **红色(逾期)**: 维保时间已过期，需立即处理
- 🟡 **黄色(警告)**: 30天内需要维保，提前准备
- 🟢 **绿色(正常)**: 维保时间充足，状态良好
- ⚪ **灰色(无记录)**: 尚未建立维保记录

### 2. 维保管理 🔧（全新升级）
**功能**: 管理设备零备件的维保计划和记录，支持首次维保计划创建

#### 创建维保计划（新功能）
1. 点击"创建维保计划"按钮
2. 选择区域和设备
3. 选择需要维保的零备件
4. 设置计划维保日期
5. 添加计划备注（可选）
6. 点击"创建计划"完成

#### 维保计划管理
- **待执行计划**: 显示所有待执行的维保计划
- **已完成计划**: 显示已完成的维保计划
- **计划执行**: 点击"执行"按钮将计划转为维保记录
- **计划状态**: 自动跟踪计划的执行状态

#### 维保提醒
- **时间筛选**: 可选择查看7天、15天、30天、60天内的维保提醒
- **优先级标识**:
  - 🔴 **紧急**: 逾期或7天内到期
  - 🟡 **重要**: 30天内到期
  - 🔵 **一般**: 60天内到期
- **快速维保**: 点击"维保"按钮快速添加维保记录

#### 添加维保记录
1. 点击"添加维保记录"按钮
2. 选择区域、设备和零备件
3. 设置维保完成日期（默认为当天）
4. 填写维保人员姓名
5. 添加维保备注（可选）
6. 点击"保存"完成添加
7. **系统自动计算下次维保时间**

#### 维保记录表
- 显示所有零备件的维保历史和状态
- 包含上次维保时间、下次维保时间、维保人员等信息
- 支持直接点击"维保"按钮进行快速维保
- **自动扣减全局库存**：维保完成后自动扣减相应零件库存

### 3. 库存管理 📦（全局统一管理）
**功能**: 全局统一监控零备件库存状态，不区分设备或区域

#### 全局库存特点
- **统一管理**: 所有零件库存在中央仓库统一管理
- **不区分用途**: 不区分零件用在哪个设备或区域
- **自动扣减**: 维保完成后自动扣减相应数量
- **实时监控**: 实时显示全局库存状态

#### 库存统计
- **库存充足**: 绿色显示，库存量高于最低库存线
- **库存不足**: 黄色显示，库存量等于或低于最低库存线
- **缺货**: 红色显示，库存量为0，需紧急补充
- **总库存项目**: 系统中所有库存项目的总数

#### 库存管理操作
1. 点击表格中的"编辑"按钮
2. 修改全局库存总数量
3. 更新存放位置信息（如：中央仓库A区）
4. 点击"保存"完成更新

#### 库存状态说明
- **零备件名称**: 零件的标准名称
- **总库存数量**: 全局统一的库存总数
- **最低库存**: 库存预警线，系统自动设置
- **存放位置**: 零备件在中央仓库中的具体位置
- **库存状态**: 自动计算的状态（充足/不足/缺货）

### 4. 数据管理 🗄️
**功能**: 查看和管理系统基础数据

#### 区域管理
- 查看三大生产区域：成品区、辅料区、片烟区
- 显示每个区域的设备数量和描述信息
- 展示区域创建时间等基础信息

#### 设备管理
- 选择区域后查看该区域下的所有设备
- 设备类型包括：堆垛机、入库皮带、AGV小车
- 显示每台设备的零备件数量

#### 零备件管理
- 选择设备后查看该设备的所有零备件
- 显示零备件的维保周期（月数）
- 查看零备件的详细描述信息

#### 系统信息
- **数据统计**: 显示系统中各类数据的总数
- **系统状态**: 显示数据库状态、最后更新时间等
- **版本信息**: 系统版本和技术栈信息

## 🎯 日常使用流程

### 每日检查流程
1. **打开系统概览页面**
   - 查看红色警告项目，优先处理逾期维保
   - 关注黄色提醒项目，提前准备维保工作

2. **检查库存状态**
   - 进入库存管理页面
   - 重点关注红色缺货和黄色库存不足项目
   - 及时补充库存或调整最低库存线

3. **处理维保任务**
   - 进入维保管理页面
   - 查看当日或近期需要维保的项目
   - 完成维保后及时添加维保记录

### 维保执行流程
1. **确认维保项目**
   - 在维保提醒中找到需要维保的零备件
   - 检查库存是否充足

2. **执行维保工作**
   - 按照标准流程进行维保操作
   - 记录维保过程中的问题和处理方法

3. **更新系统记录**
   - 添加维保记录，填写维保日期和人员
   - 系统自动计算下次维保时间
   - 如有库存消耗，及时更新库存数量

### 库存管理流程
1. **定期盘点**
   - 定期检查实际库存与系统记录是否一致
   - 及时更新库存数量和存放位置

2. **采购计划**
   - 根据库存不足提醒制定采购计划
   - 考虑维保频率和库存周转率

3. **入库管理**
   - 新零备件到货后及时更新库存
   - 记录准确的存放位置

## ⚠️ 注意事项

### 数据安全
- 定期备份数据库文件 `maintenance_system.db`
- 建议每周备份一次，保存在安全位置
- 重要操作前建议先备份数据

### 操作建议
- 维保完成后立即更新系统记录，避免遗忘
- 保持库存信息的实时准确性
- 定期检查系统数据的完整性

### 性能优化
- 大量数据时建议分页查看
- 定期清理过期的维保记录
- 避免同时打开过多页面

### 浏览器兼容
- 推荐使用Chrome、Firefox、Edge等现代浏览器
- 确保JavaScript已启用
- 建议使用最新版本浏览器以获得最佳体验

## 🔧 故障排除

### 常见问题
1. **无法访问系统**
   - 检查系统是否正常启动
   - 确认端口5000未被占用
   - 检查防火墙设置

2. **数据显示异常**
   - 点击"刷新数据"按钮
   - 检查数据库文件是否存在
   - 重启系统服务

3. **图表不显示**
   - 检查网络连接（需要加载外部资源）
   - 确认浏览器支持JavaScript
   - 清除浏览器缓存

### 技术支持
如遇到其他问题，请检查：
- Python版本是否为3.7+
- 依赖包是否正确安装
- 数据库文件是否完整

---

**系统版本**: v1.0  
**更新时间**: 2025年7月  
**技术支持**: 开发团队
