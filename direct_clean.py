import sqlite3
from datetime import datetime, timedelta
import random

# 直接连接数据库并清理
conn = sqlite3.connect('equipment_maintenance.db')
cursor = conn.cursor()

print("=== 清理逾期维保记录 ===")

# 查看当前逾期记录
cursor.execute("""
    SELECT id, next_maintenance_date
    FROM maintenance_records 
    WHERE DATE(next_maintenance_date) < DATE('now')
    ORDER BY next_maintenance_date DESC
""")

overdue_records = cursor.fetchall()
print(f"当前逾期记录数: {len(overdue_records)}")

if len(overdue_records) > 2:
    # 保留最近的2条
    keep_ids = [record[0] for record in overdue_records[:2]]
    delete_ids = [record[0] for record in overdue_records[2:]]
    
    print(f"将保留 {len(keep_ids)} 条记录")
    print(f"将删除 {len(delete_ids)} 条记录")
    
    # 删除多余的逾期记录
    for delete_id in delete_ids:
        cursor.execute("DELETE FROM maintenance_records WHERE id = ?", (delete_id,))
    
    print(f"已删除 {len(delete_ids)} 条逾期记录")

# 重新生成合理的维保时间分布
print("\n=== 重新生成维保时间 ===")

cursor.execute("""
    SELECT mr.id, sp.maintenance_cycle_months
    FROM maintenance_records mr
    JOIN spare_parts sp ON mr.spare_part_id = sp.id
""")

all_records = cursor.fetchall()
today = datetime.now().date()

# 更新所有记录的时间，让分布更合理
for record_id, cycle_months in all_records:
    # 随机生成过去1-180天的维保日期
    days_ago = random.randint(1, 180)
    maintenance_date = today - timedelta(days=days_ago)
    next_maintenance_date = maintenance_date + timedelta(days=cycle_months * 30)
    
    cursor.execute("""
        UPDATE maintenance_records 
        SET maintenance_date = ?, next_maintenance_date = ?
        WHERE id = ?
    """, (maintenance_date.strftime('%Y-%m-%d'), next_maintenance_date.strftime('%Y-%m-%d'), record_id))

print(f"更新了 {len(all_records)} 条记录的时间")

# 提交更改
conn.commit()

# 验证结果
cursor.execute("""
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN DATE(next_maintenance_date) < DATE('now') THEN 1 ELSE 0 END) as overdue,
        SUM(CASE WHEN DATE(next_maintenance_date) <= DATE('now', '+30 days') AND DATE(next_maintenance_date) >= DATE('now') THEN 1 ELSE 0 END) as upcoming,
        SUM(CASE WHEN DATE(next_maintenance_date) > DATE('now', '+30 days') THEN 1 ELSE 0 END) as normal
    FROM maintenance_records
""")

result = cursor.fetchone()
print(f"\n=== 清理完成 ===")
print(f"总记录数: {result[0]}")
print(f"逾期记录: {result[1]}")
print(f"即将到期: {result[2]}")
print(f"正常记录: {result[3]}")

conn.close()
print("数据库清理完成！")
