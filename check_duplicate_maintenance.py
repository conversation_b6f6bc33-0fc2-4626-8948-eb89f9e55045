#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查维保记录中的重复情况
"""

import sqlite3

def check_duplicate_maintenance():
    """检查维保记录中的重复情况"""
    print("=== 检查维保记录重复情况 ===")
    
    try:
        conn = sqlite3.connect('equipment_maintenance.db')
        cursor = conn.cursor()
        
        # 检查是否存在相同区域、设备、零件的重复维保记录
        cursor.execute("""
            SELECT 
                a.name as area_name,
                e.name as equipment_name, 
                sp.name as spare_part_name,
                COUNT(*) as count
            FROM maintenance_records mr
            JOIN areas a ON mr.area_id = a.id
            JOIN equipment e ON mr.equipment_id = e.id
            JOIN spare_parts sp ON mr.spare_part_id = sp.id
            GROUP BY mr.area_id, mr.equipment_id, mr.spare_part_id
            HAVING COUNT(*) > 1
            ORDER BY count DESC
        """)
        
        duplicates = cursor.fetchall()
        
        if duplicates:
            print("❌ 发现重复的维保记录:")
            for dup in duplicates:
                print(f"  区域: {dup[0]}, 设备: {dup[1]}, 零件: {dup[2]}, 重复次数: {dup[3]}")
            
            # 显示具体的重复记录
            print("\n详细的重复记录:")
            for dup in duplicates:
                area_name, equipment_name, spare_part_name, count = dup
                cursor.execute("""
                    SELECT 
                        mr.id,
                        mr.maintenance_date,
                        mr.next_maintenance_date,
                        mr.technician,
                        mr.notes
                    FROM maintenance_records mr
                    JOIN areas a ON mr.area_id = a.id
                    JOIN equipment e ON mr.equipment_id = e.id
                    JOIN spare_parts sp ON mr.spare_part_id = sp.id
                    WHERE a.name = ? AND e.name = ? AND sp.name = ?
                    ORDER BY mr.maintenance_date DESC
                """, (area_name, equipment_name, spare_part_name))
                
                records = cursor.fetchall()
                print(f"\n{area_name} - {equipment_name} - {spare_part_name}:")
                for record in records:
                    print(f"  ID: {record[0]}, 维保日期: {record[1]}, 下次维保: {record[2]}, 技师: {record[3]}")
        else:
            print("✅ 没有发现重复的维保记录")
        
        # 统计总的维保记录数
        cursor.execute("SELECT COUNT(*) FROM maintenance_records")
        total_count = cursor.fetchone()[0]
        print(f"\n总维保记录数: {total_count}")
        
        # 统计不同组合的数量
        cursor.execute("""
            SELECT COUNT(DISTINCT area_id, equipment_id, spare_part_id) 
            FROM maintenance_records
        """)
        unique_combinations = cursor.fetchone()[0]
        print(f"唯一的区域-设备-零件组合数: {unique_combinations}")
        
        conn.close()
        return len(duplicates) > 0
        
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_duplicate_maintenance()
