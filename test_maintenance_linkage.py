#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试维保提醒、维保计划和维保状态的联动功能
"""

import requests
import json
from datetime import datetime, timedelta

def test_maintenance_linkage():
    """测试维保联动功能"""
    base_url = "http://127.0.0.1:5000"
    
    print("🔗 测试维保提醒、维保计划和维保状态的联动功能")
    print("=" * 60)
    
    # 1. 获取维保提醒
    print("\n1️⃣ 获取维保提醒...")
    response = requests.get(f"{base_url}/api/maintenance/alerts?days=30")
    if response.status_code == 200:
        alerts_data = response.json()
        if alerts_data.get('success') and alerts_data['data']:
            alerts = alerts_data['data']
            print(f"   找到 {len(alerts)} 个维保提醒")
            
            # 选择第一个逾期的提醒
            overdue_alert = None
            for alert in alerts:
                if alert.get('days_remaining', 0) < 0:
                    overdue_alert = alert
                    break
            
            if overdue_alert:
                print(f"   选择逾期提醒: {overdue_alert['spare_part_name']} - {overdue_alert['equipment_name']}")
                print(f"   逾期天数: {abs(overdue_alert['days_remaining'])}天")
                
                # 2. 获取区域和设备ID
                print("\n2️⃣ 获取区域和设备ID...")

                # 获取区域ID
                response = requests.get(f"{base_url}/api/areas")
                area_id = None
                if response.status_code == 200:
                    areas_data = response.json()
                    if areas_data.get('success'):
                        for area in areas_data['data']:
                            if area['name'] == overdue_alert['area_name']:
                                area_id = area['id']
                                break

                # 获取设备ID（需要先有区域ID）
                equipment_id = None
                if area_id:
                    response = requests.get(f"{base_url}/api/equipment/{area_id}")
                    if response.status_code == 200:
                        equipment_data = response.json()
                        if equipment_data.get('success'):
                            for equipment in equipment_data['data']:
                                if equipment['name'] == overdue_alert['equipment_name']:
                                    equipment_id = equipment['id']
                                    break

                if not area_id or not equipment_id:
                    print(f"   ❌ 未找到对应的区域ID({area_id})或设备ID({equipment_id})")
                    return False

                print(f"   找到区域ID: {area_id}, 设备ID: {equipment_id}")

                # 3. 从提醒创建维保计划
                print("\n3️⃣ 从维保提醒创建维保计划...")
                plan_data = {
                    'spare_part_id': overdue_alert['id'],
                    'area_id': area_id,
                    'equipment_id': equipment_id,
                    'planned_date': (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d'),
                    'notes': '从维保提醒创建的测试计划',
                    'from_alert': True
                }
                
                response = requests.post(f"{base_url}/api/maintenance/plan", json=plan_data)
                print(f"   创建计划响应状态: {response.status_code}")
                if response.status_code != 200:
                    print(f"   错误响应: {response.text}")
                if response.status_code == 200:
                    plan_result = response.json()
                    if plan_result.get('success'):
                        print("   ✅ 维保计划创建成功")
                        
                        # 4. 获取刚创建的维保计划
                        print("\n4️⃣ 获取维保计划列表...")
                        response = requests.get(f"{base_url}/api/maintenance/plans?status=pending")
                        if response.status_code == 200:
                            plans_data = response.json()
                            if plans_data.get('success'):
                                plans = plans_data['data']
                                print(f"   找到 {len(plans)} 个待执行的维保计划")

                                # 找到刚创建的计划
                                target_plan = None
                                for plan in plans:
                                    if (plan['spare_part_name'] == overdue_alert['spare_part_name'] and
                                        plan['equipment_name'] == overdue_alert['equipment_name']):
                                        target_plan = plan
                                        break

                                if target_plan:
                                    print(f"   找到目标计划: ID={target_plan['id']}")

                                    # 5. 执行维保计划
                                    print("\n5️⃣ 执行维保计划...")
                                    execute_data = {
                                        'maintenance_date': datetime.now().strftime('%Y-%m-%d'),
                                        'technician': '测试技术员',
                                        'notes': '联动测试执行',
                                        'quantity_used': 1
                                    }
                                    
                                    response = requests.post(
                                        f"{base_url}/api/maintenance/plan/{target_plan['id']}/execute",
                                        json=execute_data
                                    )
                                    
                                    if response.status_code == 200:
                                        execute_result = response.json()
                                        if execute_result.get('success'):
                                            print("   ✅ 维保计划执行成功")
                                            
                                            # 6. 验证联动效果
                                            print("\n6️⃣ 验证联动效果...")

                                            # 检查维保状态
                                            response = requests.get(f"{base_url}/api/maintenance/status?per_page=50")
                                            if response.status_code == 200:
                                                status_data = response.json()
                                                if status_data.get('success'):
                                                    statuses = status_data['data']

                                                    # 查找相关的维保状态
                                                    related_status = None
                                                    for status in statuses:
                                                        if (status['spare_part_name'] == overdue_alert['spare_part_name'] and
                                                            status['equipment_name'] == overdue_alert['equipment_name'] and
                                                            status['technician'] == '测试技术员'):
                                                            related_status = status
                                                            break

                                                    if related_status:
                                                        print("   ✅ 找到更新后的维保状态")
                                                        print(f"   维保日期: {related_status['maintenance_date']}")
                                                        print(f"   下次维保: {related_status['next_maintenance_date']}")
                                                        print(f"   状态: {related_status['status_text']}")
                                                        print(f"   剩余天数: {related_status.get('days_text', '未知')}")

                                                        # 检查维保提醒是否更新
                                                        print("\n7️⃣ 检查维保提醒更新...")
                                                        response = requests.get(f"{base_url}/api/maintenance/alerts?days=30")
                                                        if response.status_code == 200:
                                                            new_alerts_data = response.json()
                                                            if new_alerts_data.get('success'):
                                                                new_alerts = new_alerts_data['data']

                                                                # 检查原来的逾期提醒是否还存在
                                                                still_overdue = False
                                                                for alert in new_alerts:
                                                                    if (alert['spare_part_name'] == overdue_alert['spare_part_name'] and
                                                                        alert['equipment_name'] == overdue_alert['equipment_name'] and
                                                                        alert.get('days_remaining', 0) < 0):
                                                                        still_overdue = True
                                                                        break

                                                                if not still_overdue:
                                                                    print("   ✅ 逾期提醒已消除，联动成功！")
                                                                    return True
                                                                else:
                                                                    print("   ❌ 逾期提醒仍然存在，联动失败")
                                                                    return False
                                                    else:
                                                        print("   ❌ 未找到更新后的维保状态")
                                                        return False
                                        else:
                                            print(f"   ❌ 维保计划执行失败: {execute_result.get('error')}")
                                            return False
                                    else:
                                        print(f"   ❌ 维保计划执行请求失败: {response.status_code}")
                                        return False
                                else:
                                    print("   ❌ 未找到刚创建的维保计划")
                                    return False
                    else:
                        print(f"   ❌ 维保计划创建失败: {plan_result.get('error')}")
                        return False
                else:
                    print(f"   ❌ 维保计划创建请求失败: {response.status_code}")
                    return False
            else:
                print("   ⚠️  未找到逾期的维保提醒")
                return False
        else:
            print("   ⚠️  没有维保提醒数据")
            return False
    else:
        print(f"   ❌ 获取维保提醒失败: {response.status_code}")
        return False

if __name__ == "__main__":
    try:
        success = test_maintenance_linkage()
        if success:
            print("\n🎉 维保联动功能测试通过！")
        else:
            print("\n❌ 维保联动功能测试失败！")
    except Exception as e:
        print(f"\n💥 测试过程中出现异常: {e}")
