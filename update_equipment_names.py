#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新设备名称脚本
只修改设备名称，其他内容保持不变
"""

import sqlite3

def update_equipment_names():
    """更新设备名称"""
    print("🔧 开始更新设备名称...")
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 首先查看当前的设备情况
        cursor.execute("""
            SELECT e.id, e.name, e.type, a.name as area_name
            FROM equipment e
            JOIN areas a ON e.area_id = a.id
            ORDER BY a.name, e.name
        """)
        current_equipment = cursor.fetchall()
        
        print("当前设备列表:")
        for eq in current_equipment:
            print(f"  ID:{eq[0]} - {eq[3]} - {eq[1]} ({eq[2]})")
        
        # 获取区域ID
        cursor.execute("SELECT id, name FROM areas")
        areas = {name: id for id, name in cursor.fetchall()}
        
        # 定义新的设备名称映射
        equipment_updates = []
        
        # 成品区：堆垛机1、2、3、4号
        chengpin_area_id = areas.get('成品区')
        if chengpin_area_id:
            cursor.execute("SELECT id, type FROM equipment WHERE area_id = ? ORDER BY id", (chengpin_area_id,))
            chengpin_equipment = cursor.fetchall()
            
            stacker_count = 1
            for eq_id, eq_type in chengpin_equipment:
                if eq_type == '堆垛机':
                    new_name = f'堆垛机{stacker_count}号'
                    equipment_updates.append((new_name, eq_id))
                    stacker_count += 1
                elif eq_type == '入库皮带':
                    # 入库皮带名称保持不变
                    equipment_updates.append(('入库皮带', eq_id))
        
        # 辅料区：堆垛机1、2、3号
        fuliao_area_id = areas.get('辅料区')
        if fuliao_area_id:
            cursor.execute("SELECT id, type FROM equipment WHERE area_id = ? ORDER BY id", (fuliao_area_id,))
            fuliao_equipment = cursor.fetchall()
            
            stacker_count = 1
            for eq_id, eq_type in fuliao_equipment:
                if eq_type == '堆垛机':
                    new_name = f'堆垛机{stacker_count}号'
                    equipment_updates.append((new_name, eq_id))
                    stacker_count += 1
                elif eq_type == 'AGV小车':
                    # AGV小车名称保持不变
                    equipment_updates.append(('AGV小车', eq_id))
        
        # 片烟区：堆垛机1、2、3号
        pianyan_area_id = areas.get('片烟区')
        if pianyan_area_id:
            cursor.execute("SELECT id, type FROM equipment WHERE area_id = ? ORDER BY id", (pianyan_area_id,))
            pianyan_equipment = cursor.fetchall()
            
            stacker_count = 1
            for eq_id, eq_type in pianyan_equipment:
                if eq_type == '堆垛机':
                    new_name = f'堆垛机{stacker_count}号'
                    equipment_updates.append((new_name, eq_id))
                    stacker_count += 1
        
        # 执行更新
        print(f"\n准备更新 {len(equipment_updates)} 个设备名称:")
        for new_name, eq_id in equipment_updates:
            cursor.execute("UPDATE equipment SET name = ? WHERE id = ?", (new_name, eq_id))
            print(f"  设备ID {eq_id} -> {new_name}")
        
        conn.commit()
        
        # 验证更新结果
        print("\n✅ 更新完成，新的设备列表:")
        cursor.execute("""
            SELECT e.id, e.name, e.type, a.name as area_name
            FROM equipment e
            JOIN areas a ON e.area_id = a.id
            ORDER BY a.name, e.name
        """)
        updated_equipment = cursor.fetchall()
        
        for eq in updated_equipment:
            print(f"  {eq[3]} - {eq[1]} ({eq[2]})")
        
        print(f"\n🎉 设备名称更新成功！共更新了 {len(equipment_updates)} 个设备")
        
    except Exception as e:
        print(f"❌ 更新设备名称失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()
    
    return True

def verify_equipment_names():
    """验证设备名称更新结果"""
    print("\n🔍 验证设备名称更新结果...")
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 按区域统计设备
        cursor.execute("""
            SELECT a.name as area_name, e.name as equipment_name, e.type, COUNT(*) as count
            FROM equipment e
            JOIN areas a ON e.area_id = a.id
            GROUP BY a.name, e.name, e.type
            ORDER BY a.name, e.name
        """)
        
        results = cursor.fetchall()
        current_area = None
        
        for area_name, equipment_name, equipment_type, count in results:
            if current_area != area_name:
                print(f"\n📍 {area_name}:")
                current_area = area_name
            print(f"  - {equipment_name} ({equipment_type}) x{count}")
        
        # 检查是否符合要求
        print("\n✅ 验证结果:")
        print("  成品区：应有堆垛机1、2、3、4号 + 入库皮带")
        print("  辅料区：应有堆垛机1、2、3号 + AGV小车")
        print("  片烟区：应有堆垛机1、2、3号")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False
    finally:
        conn.close()
    
    return True

def main():
    """主函数"""
    print("🔧 设备名称更新工具")
    print("=" * 40)
    print("目标:")
    print("  成品区：堆垛机1、2、3、4号")
    print("  辅料区：堆垛机1、2、3号")
    print("  片烟区：堆垛机1、2、3号")
    print("=" * 40)
    
    # 更新设备名称
    success = update_equipment_names()
    
    if success:
        # 验证更新结果
        verify_equipment_names()
        print("\n🎉 设备名称更新完成！")
    else:
        print("\n❌ 设备名称更新失败")
    
    return success

if __name__ == "__main__":
    main()
