#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复维保记录重复问题
"""

import sqlite3
import os
from datetime import datetime

def fix_duplicate_maintenance():
    """修复维保记录中的重复问题"""
    print("=== 修复维保记录重复问题 ===")
    
    db_path = 'equipment_maintenance.db'
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 首先检查重复情况
        print("1. 检查重复的维保记录...")
        cursor.execute("""
            SELECT 
                area_id,
                equipment_id, 
                spare_part_id,
                COUNT(*) as count
            FROM maintenance_records
            GROUP BY area_id, equipment_id, spare_part_id
            HAVING COUNT(*) > 1
            ORDER BY count DESC
        """)
        
        duplicates = cursor.fetchall()
        
        if not duplicates:
            print("✅ 没有发现重复的维保记录")
            return True
        
        print(f"发现 {len(duplicates)} 组重复的维保记录:")
        
        total_removed = 0
        
        # 2. 处理每组重复记录
        for area_id, equipment_id, spare_part_id, count in duplicates:
            print(f"\n处理重复组: 区域ID={area_id}, 设备ID={equipment_id}, 零件ID={spare_part_id}, 重复数={count}")
            
            # 获取这组重复记录的详细信息
            cursor.execute("""
                SELECT 
                    id, maintenance_date, next_maintenance_date, technician, notes,
                    created_at
                FROM maintenance_records
                WHERE area_id = ? AND equipment_id = ? AND spare_part_id = ?
                ORDER BY 
                    CASE WHEN maintenance_date IS NOT NULL THEN maintenance_date ELSE '1900-01-01' END DESC,
                    CASE WHEN created_at IS NOT NULL THEN created_at ELSE '1900-01-01 00:00:00' END DESC,
                    id DESC
            """, (area_id, equipment_id, spare_part_id))
            
            records = cursor.fetchall()
            
            if len(records) <= 1:
                continue
            
            # 保留第一条记录（最新的），删除其他的
            keep_record = records[0]
            remove_records = records[1:]
            
            print(f"  保留记录: ID={keep_record[0]}, 维保日期={keep_record[1]}, 技师={keep_record[3]}")
            
            for record in remove_records:
                print(f"  删除记录: ID={record[0]}, 维保日期={record[1]}, 技师={record[3]}")
                cursor.execute("DELETE FROM maintenance_records WHERE id = ?", (record[0],))
                total_removed += 1
        
        # 3. 提交更改
        conn.commit()
        print(f"\n✅ 修复完成！共删除了 {total_removed} 条重复记录")
        
        # 4. 验证修复结果
        print("\n4. 验证修复结果...")
        cursor.execute("""
            SELECT 
                area_id,
                equipment_id, 
                spare_part_id,
                COUNT(*) as count
            FROM maintenance_records
            GROUP BY area_id, equipment_id, spare_part_id
            HAVING COUNT(*) > 1
        """)
        
        remaining_duplicates = cursor.fetchall()
        
        if remaining_duplicates:
            print(f"❌ 仍有 {len(remaining_duplicates)} 组重复记录")
            for dup in remaining_duplicates:
                print(f"  区域ID: {dup[0]}, 设备ID: {dup[1]}, 零件ID: {dup[2]}, 数量: {dup[3]}")
        else:
            print("✅ 所有重复记录已清理完毕")
        
        # 5. 统计最终结果
        cursor.execute("SELECT COUNT(*) FROM maintenance_records")
        final_count = cursor.fetchone()[0]
        print(f"\n最终维保记录数: {final_count}")
        
        cursor.execute("""
            SELECT COUNT(DISTINCT area_id, equipment_id, spare_part_id) 
            FROM maintenance_records
        """)
        unique_combinations = cursor.fetchone()[0]
        print(f"唯一的区域-设备-零件组合数: {unique_combinations}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def add_unique_constraint():
    """为维保记录表添加唯一约束"""
    print("\n=== 添加唯一约束 ===")
    
    try:
        conn = sqlite3.connect('equipment_maintenance.db')
        cursor = conn.cursor()
        
        # 检查是否已经有唯一约束
        cursor.execute("PRAGMA index_list(maintenance_records)")
        indexes = cursor.fetchall()
        
        has_unique_constraint = False
        for index in indexes:
            cursor.execute(f"PRAGMA index_info({index[1]})")
            index_info = cursor.fetchall()
            if len(index_info) == 3:  # area_id, equipment_id, spare_part_id
                has_unique_constraint = True
                break
        
        if not has_unique_constraint:
            print("添加唯一约束...")
            cursor.execute("""
                CREATE UNIQUE INDEX IF NOT EXISTS idx_maintenance_unique 
                ON maintenance_records(area_id, equipment_id, spare_part_id)
            """)
            conn.commit()
            print("✅ 唯一约束添加成功")
        else:
            print("✅ 唯一约束已存在")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 添加唯一约束时出错: {e}")
        return False

if __name__ == "__main__":
    success = fix_duplicate_maintenance()
    if success:
        add_unique_constraint()
        print("\n🎉 维保记录重复问题修复完成！")
    else:
        print("\n❌ 修复失败")
