#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
根据README.MD文档内容创建真实的设备零件数据
严格按照文档中的三大区域、设备类型和零备件规格
"""

import sqlite3
import os
from datetime import datetime, timedelta
import random

def create_real_database():
    """根据README文档创建真实的数据库"""
    
    # 删除现有数据库，重新创建
    db_path = 'equipment_maintenance.db'
    if os.path.exists(db_path):
        os.remove(db_path)
        print("已删除现有数据库")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("=== 创建数据库表结构 ===")
        
        # 创建区域表
        cursor.execute('''
            CREATE TABLE areas (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建设备表
        cursor.execute('''
            CREATE TABLE equipment (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                area_id INTEGER NOT NULL,
                equipment_type TEXT NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (area_id) REFERENCES areas (id)
            )
        ''')
        
        # 创建零备件表
        cursor.execute('''
            CREATE TABLE spare_parts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                maintenance_cycle_months INTEGER NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建库存表
        cursor.execute('''
            CREATE TABLE inventory (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                spare_part_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL DEFAULT 0,
                min_quantity INTEGER NOT NULL DEFAULT 5,
                location TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (spare_part_id) REFERENCES spare_parts (id)
            )
        ''')
        
        # 创建维保记录表
        cursor.execute('''
            CREATE TABLE maintenance_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                spare_part_id INTEGER NOT NULL,
                area_id INTEGER NOT NULL,
                equipment_id INTEGER NOT NULL,
                maintenance_date DATE NOT NULL,
                next_maintenance_date DATE NOT NULL,
                technician TEXT,
                notes TEXT,
                quantity_used INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (spare_part_id) REFERENCES spare_parts (id),
                FOREIGN KEY (area_id) REFERENCES areas (id),
                FOREIGN KEY (equipment_id) REFERENCES equipment (id)
            )
        ''')
        
        print("✓ 数据库表结构创建完成")
        
        print("\n=== 插入区域数据 ===")
        # 根据README文档插入三大区域
        areas_data = [
            ('成品区', '4台堆垛机 + 1条入库皮带'),
            ('辅料区', '3台堆垛机 + 1台AGV小车'),
            ('片烟区', '3台堆垛机')
        ]
        
        cursor.executemany('INSERT INTO areas (name, description) VALUES (?, ?)', areas_data)
        print("✓ 插入了3个区域")
        
        print("\n=== 插入设备数据 ===")
        # 根据README文档插入设备
        equipment_data = []
        
        # 成品区设备 (area_id=1)
        for i in range(1, 5):  # 4台堆垛机
            equipment_data.append((f'堆垛机{i:03d}', 1, '堆垛机', f'成品区第{i}台堆垛机'))
        equipment_data.append(('入库皮带001', 1, '入库皮带', '成品区入库皮带'))
        
        # 辅料区设备 (area_id=2)
        for i in range(1, 4):  # 3台堆垛机
            equipment_data.append((f'堆垛机{i+4:03d}', 2, '堆垛机', f'辅料区第{i}台堆垛机'))
        equipment_data.append(('AGV小车001', 2, 'AGV小车', '辅料区AGV小车'))
        
        # 片烟区设备 (area_id=3)
        for i in range(1, 4):  # 3台堆垛机
            equipment_data.append((f'堆垛机{i+7:03d}', 3, '堆垛机', f'片烟区第{i}台堆垛机'))
        
        cursor.executemany('INSERT INTO equipment (name, area_id, equipment_type, description) VALUES (?, ?, ?, ?)', equipment_data)
        print(f"✓ 插入了{len(equipment_data)}台设备")
        
        print("\n=== 插入零备件数据 ===")
        # 根据README文档插入零备件及其维保周期
        spare_parts_data = [
            # 堆垛机零备件
            ('主接触器', 6, '堆垛机主要电气元件，6个月维保周期'),
            ('变频器', 12, '堆垛机变频控制器，12个月维保周期'),
            ('传感器', 3, '堆垛机位置传感器，3个月维保周期'),
            
            # 入库皮带零备件
            ('皮带', 3, '入库皮带传送带，3个月维保周期'),
            ('滚筒', 6, '入库皮带滚筒，6个月维保周期'),
            
            # AGV小车零备件
            ('保险丝', 6, 'AGV小车保险丝，6个月维保周期'),
            ('电池', 12, 'AGV小车动力电池，12个月维保周期'),
            ('轮子', 9, 'AGV小车行走轮子，9个月维保周期')
        ]
        
        cursor.executemany('INSERT INTO spare_parts (name, maintenance_cycle_months, description) VALUES (?, ?, ?)', spare_parts_data)
        print(f"✓ 插入了{len(spare_parts_data)}种零备件")
        
        print("\n=== 插入库存数据 ===")
        # 为每种零备件创建库存记录
        inventory_data = []
        locations = ['仓库A-01', '仓库A-02', '仓库B-01', '仓库B-02', '仓库C-01', '仓库C-02', '仓库D-01', '仓库D-02']
        
        for i, (part_name, cycle, desc) in enumerate(spare_parts_data, 1):
            quantity = random.randint(8, 25)  # 随机库存数量
            min_qty = random.randint(3, 8)    # 最低库存
            location = locations[i % len(locations)]
            inventory_data.append((i, quantity, min_qty, location))
        
        cursor.executemany('INSERT INTO inventory (spare_part_id, quantity, min_quantity, location) VALUES (?, ?, ?, ?)', inventory_data)
        print(f"✓ 插入了{len(inventory_data)}条库存记录")
        
        print("\n=== 创建维保记录 ===")
        # 创建真实的维保记录
        maintenance_records = []
        technicians = ['张师傅', '李师傅', '王师傅', '赵师傅', '陈师傅']
        
        # 获取所有设备和零备件的对应关系
        cursor.execute('SELECT id, name, equipment_type, area_id FROM equipment')
        equipment_list = cursor.fetchall()
        
        cursor.execute('SELECT id, name, maintenance_cycle_months FROM spare_parts')
        spare_parts_list = cursor.fetchall()
        
        today = datetime.now().date()
        
        # 为每台设备创建相应的维保记录
        for eq_id, eq_name, eq_type, area_id in equipment_list:
            # 根据设备类型选择对应的零备件
            if eq_type == '堆垛机':
                # 堆垛机使用：主接触器、变频器、传感器
                applicable_parts = [(1, '主接触器', 6), (2, '变频器', 12), (3, '传感器', 3)]
            elif eq_type == '入库皮带':
                # 入库皮带使用：皮带、滚筒
                applicable_parts = [(4, '皮带', 3), (5, '滚筒', 6)]
            elif eq_type == 'AGV小车':
                # AGV小车使用：保险丝、电池、轮子
                applicable_parts = [(6, '保险丝', 6), (7, '电池', 12), (8, '轮子', 9)]
            else:
                continue
            
            # 为每个适用的零备件创建维保记录
            for part_id, part_name, cycle_months in applicable_parts:
                # 创建多条历史维保记录
                for record_num in range(1, 4):  # 每个零备件创建3条记录
                    # 计算维保日期（过去的日期）
                    days_ago = random.randint(30, 365)
                    maintenance_date = today - timedelta(days=days_ago)
                    next_maintenance_date = maintenance_date + timedelta(days=cycle_months * 30)
                    
                    technician = random.choice(technicians)
                    notes = f'维保记录 - {eq_name} - {part_name}'
                    
                    maintenance_records.append((
                        part_id, area_id, eq_id, 
                        maintenance_date.strftime('%Y-%m-%d'),
                        next_maintenance_date.strftime('%Y-%m-%d'),
                        technician, notes, 1
                    ))
        
        cursor.executemany('''
            INSERT INTO maintenance_records 
            (spare_part_id, area_id, equipment_id, maintenance_date, next_maintenance_date, technician, notes, quantity_used)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', maintenance_records)
        
        print(f"✓ 插入了{len(maintenance_records)}条维保记录")
        
        # 创建索引
        print("\n=== 创建数据库索引 ===")
        cursor.execute('CREATE INDEX idx_maintenance_next_date ON maintenance_records(next_maintenance_date)')
        cursor.execute('CREATE INDEX idx_equipment_area ON equipment(area_id)')
        cursor.execute('CREATE INDEX idx_inventory_spare_part ON inventory(spare_part_id)')
        print("✓ 数据库索引创建完成")
        
        conn.commit()
        
        # 验证数据
        print("\n=== 数据验证 ===")
        cursor.execute('SELECT COUNT(*) FROM areas')
        areas_count = cursor.fetchone()[0]
        print(f"区域数量: {areas_count}")
        
        cursor.execute('SELECT COUNT(*) FROM equipment')
        equipment_count = cursor.fetchone()[0]
        print(f"设备数量: {equipment_count}")
        
        cursor.execute('SELECT COUNT(*) FROM spare_parts')
        parts_count = cursor.fetchone()[0]
        print(f"零备件种类: {parts_count}")
        
        cursor.execute('SELECT COUNT(*) FROM inventory')
        inventory_count = cursor.fetchone()[0]
        print(f"库存记录: {inventory_count}")
        
        cursor.execute('SELECT COUNT(*) FROM maintenance_records')
        maintenance_count = cursor.fetchone()[0]
        print(f"维保记录: {maintenance_count}")
        
        print("\n=== 数据库创建完成 ===")
        print("✓ 严格按照README.MD文档规格创建")
        print("✓ 三大区域：成品区、辅料区、片烟区")
        print("✓ 真实设备：堆垛机、入库皮带、AGV小车")
        print("✓ 标准零备件：主接触器、变频器、传感器等")
        print("✓ 维保周期：3个月、6个月、9个月、12个月")
        
    except Exception as e:
        print(f"创建数据库时出错: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    create_real_database()
