帮我做一个设备零备件周期管理系统，主要用途是分三大区域：成品区、辅料区、片烟区
每个区有设备：比如堆垛机，成品4台，辅料3台，片烟3台
每个设备有多个零备件比如：每个堆垛机上有主接触器（维保周期6个月） 成品区有入库皮带（维保周期3个月） 辅料区有AGV小车，AGV的保险丝（维保周期6个月）
要求维保时间还剩一个月时候，系统会报警：快要更换某个区某个设备上的某个零件提示请查看是否有库存
要记录维保时间并根据维保周期计算下次维保时间
可以用图表的形式展示还剩多少时间维保，可以用百分比了解进度：类似翻转90°的条形图

数据库设置可以为
区域：成品区、辅料区、片烟区
设备：堆垛机、入库皮带、AGV小车
零备件：主接触器、入库皮带、AGV小车的保险丝
符合上述所说的关联，比如成品区包含堆垛机，堆垛机包含主接触器，主接触器的维保周期为6个月
                  比如辅料区包含AGV小车，AGV小车包含保险丝，保险丝的维保周期为6个月等

包含零备件关系管理：可以新增区域、设备，新增零备件的同时绑定区域和设备的关系以及维保周期
主页默认区域为成品区（可选择区域如果没有选择就把所有区域显示出来） 显示该区域所有设备并显示不同零件上次维保时间，离维保时间还有多久，根据维保周期计算下次维保时间，并且还剩一个月就要维保的零件要报警标红显示

区域、设备、零备件要有一定的层级，要不然看着不舒服