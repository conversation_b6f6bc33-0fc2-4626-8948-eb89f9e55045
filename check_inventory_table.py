#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def check_inventory_table():
    """检查inventory表结构"""
    db_path = 'equipment_maintenance.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("=== 检查inventory表结构 ===")
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(inventory)")
        columns = cursor.fetchall()
        print("inventory表列:")
        for col in columns:
            print(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else ''}")
        
        # 检查数据数量
        cursor.execute("SELECT COUNT(*) FROM inventory")
        count = cursor.fetchone()[0]
        print(f"\ninventory表记录数: {count}")
        
        if count > 0:
            # 显示前几条记录
            cursor.execute("SELECT * FROM inventory LIMIT 3")
            records = cursor.fetchall()
            print("\n前3条记录:")
            for record in records:
                print(f"  {record}")
        
        # 测试库存状态查询
        print("\n=== 测试库存状态查询 ===")
        try:
            query = """
                SELECT
                    i.id,
                    sp.id as spare_part_id,
                    sp.name as spare_part_name,
                    sp.description as spare_part_description,
                    i.quantity,
                    i.min_quantity,
                    i.location,
                    i.updated_at
                FROM inventory i
                JOIN spare_parts sp ON i.spare_part_id = sp.id
                ORDER BY sp.name
                LIMIT 3
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            print(f"查询成功，返回{len(results)}条记录")
            for result in results:
                print(f"  {result}")
                
        except Exception as e:
            print(f"查询失败: {e}")
            
            # 尝试使用last_updated字段
            try:
                query_alt = """
                    SELECT
                        i.id,
                        sp.id as spare_part_id,
                        sp.name as spare_part_name,
                        sp.description as spare_part_description,
                        i.quantity,
                        i.min_quantity,
                        i.location,
                        i.last_updated
                    FROM inventory i
                    JOIN spare_parts sp ON i.spare_part_id = sp.id
                    ORDER BY sp.name
                    LIMIT 3
                """
                
                cursor.execute(query_alt)
                results = cursor.fetchall()
                print(f"使用last_updated字段查询成功，返回{len(results)}条记录")
                
            except Exception as e2:
                print(f"使用last_updated字段也失败: {e2}")
        
    except Exception as e:
        print(f"❌ 检查inventory表时出错: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    check_inventory_table()
