import sqlite3

def check_inventory():
    conn = sqlite3.connect('equipment_maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    print("=== 检查库存表结构 ===")
    cursor.execute("PRAGMA table_info(inventory)")
    columns = cursor.fetchall()
    for col in columns:
        print(f"列: {col['name']}, 类型: {col['type']}")
    
    print("\n=== 检查库存数据 ===")
    cursor.execute("SELECT * FROM inventory")
    inventory_rows = cursor.fetchall()
    print(f"库存记录数: {len(inventory_rows)}")
    
    for row in inventory_rows:
        print(f"ID: {row['id']}, 零备件ID: {row['spare_part_id']}, 数量: {row['quantity']}, 最小数量: {row['min_quantity']}")
    
    print("\n=== 检查零备件表 ===")
    cursor.execute("SELECT * FROM spare_parts")
    spare_parts = cursor.fetchall()
    print(f"零备件记录数: {len(spare_parts)}")
    
    for part in spare_parts:
        print(f"ID: {part['id']}, 名称: {part['name']}, 描述: {part['description']}")
    
    print("\n=== 测试库存查询 ===")
    try:
        query = """
            SELECT 
                i.id,
                sp.id as spare_part_id,
                sp.name as spare_part_name,
                sp.description as spare_part_description,
                i.quantity,
                i.min_quantity,
                i.location,
                i.last_updated
            FROM inventory i
            JOIN spare_parts sp ON i.spare_part_id = sp.id
            ORDER BY sp.name
        """
        cursor.execute(query)
        results = cursor.fetchall()
        
        print(f"查询结果数: {len(results)}")
        for row in results:
            print(f"零备件: {row['spare_part_name']}, 库存: {row['quantity']}, 最小: {row['min_quantity']}")
            
    except Exception as e:
        print(f"查询错误: {e}")
    
    conn.close()

if __name__ == "__main__":
    check_inventory()
