<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分页滚动测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>🧪 维保记录分页滚动功能测试</h1>
    
    <div class="test-section">
        <h3>测试说明</h3>
        <p>此测试页面用于验证维保记录分页功能的滚动行为：</p>
        <ul>
            <li>✅ 点击分页按钮后，页面应该自动滚动到维保记录表格位置</li>
            <li>✅ 滚动应该是平滑的，不会突然跳转</li>
            <li>✅ 滚动位置应该留出适当的顶部空间，避免被导航栏遮挡</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>测试步骤</h3>
        <ol>
            <li>打开维保管理页面：<a href="http://127.0.0.1:5000/maintenance" target="_blank">http://127.0.0.1:5000/maintenance</a></li>
            <li>滚动到页面顶部（维保提醒区域）</li>
            <li>点击维保记录表格下方的分页按钮（如第2页、第3页等）</li>
            <li>观察页面是否自动滚动到维保记录表格位置</li>
            <li>验证滚动是否平滑，位置是否合适</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>快速测试</h3>
        <button class="test-button" onclick="testPaginationAPI()">测试分页API</button>
        <button class="test-button" onclick="openMaintenancePage()">打开维保页面</button>
        <div id="testResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h3>预期行为</h3>
        <ul>
            <li><strong>正确行为</strong>：点击分页按钮后，页面平滑滚动到维保记录表格，用户可以立即看到新加载的数据</li>
            <li><strong>错误行为</strong>：点击分页按钮后，页面跳转到顶部，用户需要手动滚动才能看到维保记录</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>技术实现</h3>
        <p>修改内容：</p>
        <ul>
            <li>添加了 <code>handlePaginationClick()</code> 函数处理分页点击事件</li>
            <li>添加了 <code>scrollToMaintenanceTable()</code> 函数实现平滑滚动</li>
            <li>修改了 <code>loadMaintenanceRecords()</code> 函数支持滚动参数</li>
            <li>更新了所有分页按钮的 onclick 事件</li>
        </ul>
    </div>

    <script>
        function testPaginationAPI() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '🔄 正在测试分页API...';
            
            // 测试第一页
            fetch('http://127.0.0.1:5000/api/maintenance/status?page=1&per_page=5')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.pagination) {
                        const pagination = data.pagination;
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = `
                            ✅ 分页API测试成功！<br>
                            📊 总记录数: ${pagination.total_records}<br>
                            📄 总页数: ${pagination.total_pages}<br>
                            📋 当前页: ${pagination.current_page}<br>
                            📝 每页记录数: ${pagination.per_page}<br>
                            ⬅️ 有上一页: ${pagination.has_prev}<br>
                            ➡️ 有下一页: ${pagination.has_next}
                        `;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = '❌ 分页API测试失败：' + (data.error || '未知错误');
                    }
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ 分页API测试失败：' + error.message;
                });
        }

        function openMaintenancePage() {
            window.open('http://127.0.0.1:5000/maintenance', '_blank');
        }

        // 页面加载时自动测试API
        window.onload = function() {
            setTimeout(testPaginationAPI, 1000);
        };
    </script>
</body>
</html>
