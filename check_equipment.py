#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查当前设备数据
"""

import sqlite3

def main():
    print("=== 检查当前设备数据 ===")
    
    # 连接数据库
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 查看当前设备名称
        cursor.execute("""
            SELECT e.id, a.name as area_name, e.name, e.type 
            FROM equipment e 
            JOIN areas a ON e.area_id = a.id 
            ORDER BY e.id
        """)
        equipment = cursor.fetchall()
        
        print("当前设备列表:")
        for eq in equipment:
            print(f"ID: {eq[0]}, 区域: {eq[1]}, 名称: {eq[2]}, 类型: {eq[3]}")
        
        # 检查是否有重复名称
        cursor.execute("SELECT name, COUNT(*) FROM equipment GROUP BY name HAVING COUNT(*) > 1")
        duplicates = cursor.fetchall()
        
        if duplicates:
            print("\n发现重复的设备名称:")
            for dup in duplicates:
                print(f"名称: {dup[0]}, 重复次数: {dup[1]}")
        else:
            print("\n✅ 没有发现重复的设备名称")
        
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        conn.close()

if __name__ == "__main__":
    main()
