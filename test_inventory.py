import requests

def test_inventory():
    try:
        # 测试库存状态API
        response = requests.get('http://127.0.0.1:5000/api/inventory/status')
        print(f"库存API状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"成功: {data.get('success')}")
            
            if data.get('success'):
                inventory = data.get('data', [])
                print(f"库存项目数量: {len(inventory)}")
                
                print("\n库存详情:")
                print(f"{'序号':<4} {'零备件名称':<15} {'当前库存':<8} {'最小库存':<8} {'状态':<8} {'位置':<12}")
                print("-" * 70)
                
                for i, item in enumerate(inventory):
                    name = item.get('spare_part_name', '未知')[:14]
                    quantity = item.get('quantity', 0)
                    min_qty = item.get('min_quantity', 0)
                    status = item.get('status_text', '未知')
                    location = item.get('location', '未知')[:11]
                    
                    print(f"{i+1:<4} {name:<15} {quantity:<8} {min_qty:<8} {status:<8} {location:<12}")
                
                # 统计库存状态
                status_count = {}
                for item in inventory:
                    status = item.get('status_text', '未知')
                    status_count[status] = status_count.get(status, 0) + 1
                
                print(f"\n库存状态统计:")
                for status, count in status_count.items():
                    print(f"  {status}: {count} 项")
                    
            else:
                print(f"API错误: {data.get('error')}")
        else:
            print(f"HTTP错误: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

if __name__ == "__main__":
    test_inventory()
