-- 检查维保记录重复情况的SQL脚本

-- 检查表是否存在
.tables

-- 检查维保记录表结构
.schema maintenance_records

-- 统计总的维保记录数
SELECT COUNT(*) as total_records FROM maintenance_records;

-- 检查是否存在相同区域、设备、零件的重复维保记录
SELECT 
    area_id,
    equipment_id, 
    spare_part_id,
    COUNT(*) as count
FROM maintenance_records
GROUP BY area_id, equipment_id, spare_part_id
HAVING COUNT(*) > 1
ORDER BY count DESC;

-- 显示重复记录的详细信息
SELECT 
    mr.id,
    a.name as area_name,
    e.name as equipment_name,
    sp.name as spare_part_name,
    mr.maintenance_date,
    mr.next_maintenance_date,
    mr.technician
FROM maintenance_records mr
LEFT JOIN areas a ON mr.area_id = a.id
LEFT JOIN equipment e ON mr.equipment_id = e.id
LEFT JOIN spare_parts sp ON mr.spare_part_id = sp.id
WHERE (mr.area_id, mr.equipment_id, mr.spare_part_id) IN (
    SELECT area_id, equipment_id, spare_part_id
    FROM maintenance_records
    GROUP BY area_id, equipment_id, spare_part_id
    HAVING COUNT(*) > 1
)
ORDER BY mr.area_id, mr.equipment_id, mr.spare_part_id, mr.maintenance_date DESC;

-- 统计唯一的区域-设备-零件组合数
SELECT COUNT(DISTINCT area_id, equipment_id, spare_part_id) as unique_combinations
FROM maintenance_records;

-- 显示最近的5条维保记录
SELECT 
    mr.id,
    a.name as area_name,
    e.name as equipment_name,
    sp.name as spare_part_name,
    mr.maintenance_date,
    mr.next_maintenance_date
FROM maintenance_records mr
LEFT JOIN areas a ON mr.area_id = a.id
LEFT JOIN equipment e ON mr.equipment_id = e.id
LEFT JOIN spare_parts sp ON mr.spare_part_id = sp.id
ORDER BY mr.maintenance_date DESC
LIMIT 5;
