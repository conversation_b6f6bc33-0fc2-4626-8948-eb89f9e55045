<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>零备件逻辑测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <h1>🧪 零备件逻辑修正测试</h1>
    
    <div class="test-section">
        <h3>测试说明</h3>
        <p>此测试页面用于验证零备件逻辑修正后的功能：</p>
        <ul>
            <li>✅ 零备件现在是独立实体，不再绑定到特定设备</li>
            <li>✅ 新增零备件只需要填写名称、维保周期和描述</li>
            <li>✅ 零备件可以用于多个不同的设备</li>
            <li>✅ 零备件名称必须唯一</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>API测试</h3>
        <button class="test-button" onclick="testGetAllSpareParts()">获取所有零备件</button>
        <button class="test-button" onclick="testCreateSparePart()">测试新增零备件</button>
        <button class="test-button" onclick="testUpdateSparePart()">测试更新零备件</button>
        <div id="apiResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h3>界面测试</h3>
        <button class="test-button" onclick="openDataPage()">打开数据管理页面</button>
        <p>在数据管理页面中：</p>
        <ol>
            <li>展开"零备件管理"部分</li>
            <li>查看零备件列表（应该显示所有零备件，不需要选择设备）</li>
            <li>点击"新增零备件"按钮</li>
            <li>验证表单只有：零备件名称、维保周期、描述三个字段</li>
            <li>尝试添加一个新的零备件</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>预期结果</h3>
        <div class="info">
            <h4>✅ 正确行为：</h4>
            <ul>
                <li>零备件列表直接显示所有零备件，无需选择设备</li>
                <li>新增零备件表单不包含"所属设备"字段</li>
                <li>零备件名称必须唯一，不能重复</li>
                <li>零备件可以在维保记录中用于任何设备</li>
            </ul>
        </div>
        <div class="error">
            <h4>❌ 错误行为：</h4>
            <ul>
                <li>零备件列表显示"请选择设备"</li>
                <li>新增零备件表单包含"所属设备"字段</li>
                <li>零备件被绑定到特定设备</li>
            </ul>
        </div>
    </div>

    <script>
        function testGetAllSpareParts() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '🔄 正在获取所有零备件...';
            
            fetch('http://127.0.0.1:5000/api/spare-parts')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = `
                            ✅ 获取零备件成功！<br>
                            📊 零备件总数: ${data.data.length}<br>
                            <strong>零备件列表:</strong><br>
                            ${data.data.map(part => 
                                `• ${part.name} (${part.maintenance_cycle_months}个月) - ${part.description || '无描述'}`
                            ).join('<br>')}
                        `;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = '❌ 获取零备件失败：' + (data.error || '未知错误');
                    }
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ 请求失败：' + error.message;
                });
        }

        function testCreateSparePart() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '🔄 正在测试新增零备件...';
            
            const testData = {
                name: '测试零备件_' + Date.now(),
                maintenance_cycle_months: 6,
                description: '这是一个测试零备件，用于验证新增功能'
            };
            
            fetch('http://127.0.0.1:5000/api/spare-parts', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testData)
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = `
                            ✅ 新增零备件成功！<br>
                            📝 零备件名称: ${testData.name}<br>
                            🔧 维保周期: ${testData.maintenance_cycle_months}个月<br>
                            📄 描述: ${testData.description}<br>
                            🆔 分配的ID: ${data.data.id}
                        `;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = '❌ 新增零备件失败：' + (data.error || '未知错误');
                    }
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ 请求失败：' + error.message;
                });
        }

        function testUpdateSparePart() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '🔄 正在测试更新零备件...';
            
            // 首先获取一个零备件ID
            fetch('http://127.0.0.1:5000/api/spare-parts')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data.length > 0) {
                        const firstPart = data.data[0];
                        const updateData = {
                            name: firstPart.name,
                            maintenance_cycle_months: firstPart.maintenance_cycle_months + 1,
                            description: (firstPart.description || '') + ' [已更新]'
                        };
                        
                        return fetch(`http://127.0.0.1:5000/api/spare-parts/${firstPart.id}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(updateData)
                        });
                    } else {
                        throw new Error('没有找到可更新的零备件');
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = '✅ 更新零备件成功！';
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = '❌ 更新零备件失败：' + (data.error || '未知错误');
                    }
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ 测试失败：' + error.message;
                });
        }

        function openDataPage() {
            window.open('http://127.0.0.1:5000/data', '_blank');
        }

        // 页面加载时自动测试获取零备件
        window.onload = function() {
            setTimeout(testGetAllSpareParts, 1000);
        };
    </script>
</body>
</html>
