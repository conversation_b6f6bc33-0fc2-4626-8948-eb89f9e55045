#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查维保计划数据
"""

import sqlite3
from datetime import datetime

def check_maintenance_plans():
    """检查维保计划数据"""
    print("🔍 检查维保计划数据...")
    
    try:
        # 连接数据库
        conn = sqlite3.connect('equipment_maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 1. 检查维保计划表结构
        print("\n1️⃣ 检查维保计划表结构...")
        cursor.execute("PRAGMA table_info(maintenance_plans)")
        columns = cursor.fetchall()
        print("维保计划表字段:")
        for col in columns:
            print(f"   {col['name']} - {col['type']} - {'NOT NULL' if col['notnull'] else 'NULL'}")
        
        # 2. 检查维保计划数据
        print("\n2️⃣ 检查维保计划数据...")
        cursor.execute("SELECT COUNT(*) as count FROM maintenance_plans")
        total_count = cursor.fetchone()['count']
        print(f"维保计划总数: {total_count}")
        
        if total_count > 0:
            cursor.execute("""
                SELECT 
                    mp.id,
                    mp.spare_part_id,
                    sp.name as spare_part_name,
                    mp.area_id,
                    a.name as area_name,
                    mp.equipment_id,
                    e.name as equipment_name,
                    mp.planned_date,
                    mp.plan_status,
                    mp.notes,
                    mp.created_at
                FROM maintenance_plans mp
                LEFT JOIN spare_parts sp ON mp.spare_part_id = sp.id
                LEFT JOIN areas a ON mp.area_id = a.id
                LEFT JOIN equipment e ON mp.equipment_id = e.id
                ORDER BY mp.id DESC
                LIMIT 10
            """)
            
            plans = cursor.fetchall()
            
            print("最近的维保计划:")
            for plan in plans:
                print(f"   ID:{plan['id']} - {plan['spare_part_name']} - {plan['area_name']} - {plan['equipment_name']} - 状态:{plan['plan_status']}")
        
        # 3. 检查待执行的维保计划
        print("\n3️⃣ 检查待执行的维保计划...")
        cursor.execute("""
            SELECT 
                mp.id,
                mp.spare_part_id,
                sp.name as spare_part_name,
                mp.area_id,
                a.name as area_name,
                mp.equipment_id,
                e.name as equipment_name,
                mp.planned_date,
                mp.plan_status,
                mp.notes
            FROM maintenance_plans mp
            LEFT JOIN spare_parts sp ON mp.spare_part_id = sp.id
            LEFT JOIN areas a ON mp.area_id = a.id
            LEFT JOIN equipment e ON mp.equipment_id = e.id
            WHERE mp.plan_status = 'pending'
            ORDER BY mp.planned_date
        """)
        
        pending_plans = cursor.fetchall()
        print(f"待执行的维保计划数量: {len(pending_plans)}")
        
        if pending_plans:
            print("待执行的维保计划:")
            for plan in pending_plans:
                print(f"   ID:{plan['id']} - {plan['spare_part_name']} - {plan['area_name']} - {plan['equipment_name']} - 计划日期:{plan['planned_date']}")
        else:
            print("   没有待执行的维保计划")
        
        # 4. 创建一个测试维保计划
        print("\n4️⃣ 创建测试维保计划...")
        
        # 获取第一个零备件
        cursor.execute("SELECT id, name FROM spare_parts LIMIT 1")
        spare_part = cursor.fetchone()
        
        # 获取第一个区域
        cursor.execute("SELECT id, name FROM areas LIMIT 1")
        area = cursor.fetchone()
        
        # 获取第一个设备
        cursor.execute("SELECT id, name FROM equipment LIMIT 1")
        equipment = cursor.fetchone()
        
        if spare_part and area and equipment:
            # 检查是否已存在相同的计划
            cursor.execute("""
                SELECT COUNT(*) as count FROM maintenance_plans 
                WHERE spare_part_id = ? AND area_id = ? AND equipment_id = ? AND plan_status = 'pending'
            """, (spare_part['id'], area['id'], equipment['id']))
            
            existing_count = cursor.fetchone()['count']
            
            if existing_count == 0:
                # 创建测试计划
                cursor.execute("""
                    INSERT INTO maintenance_plans
                    (spare_part_id, area_id, equipment_id, planned_date, notes, plan_status)
                    VALUES (?, ?, ?, ?, ?, 'pending')
                """, (spare_part['id'], area['id'], equipment['id'], 
                      datetime.now().strftime('%Y-%m-%d'), '测试维保计划'))
                
                conn.commit()
                print(f"✅ 创建测试维保计划成功:")
                print(f"   零备件: {spare_part['name']}")
                print(f"   区域: {area['name']}")
                print(f"   设备: {equipment['name']}")
            else:
                print(f"⚠️  已存在相同的待执行维保计划")
        else:
            print("❌ 缺少基础数据，无法创建测试计划")
        
        conn.close()
        print("\n✅ 数据库检查完成!")
        
    except Exception as e:
        print(f"❌ 检查数据库时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_maintenance_plans()
