#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比系统概览和维保进度概览的逾期数据查询差异
"""

import sqlite3
from datetime import datetime, timed<PERSON><PERSON>

def compare_overdue_queries():
    """对比两个查询的结果差异"""
    print("🔍 对比系统概览和维保进度概览的逾期数据查询")
    print("=" * 60)
    
    conn = sqlite3.connect('equipment_maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        today = datetime.now().date()
        print(f"今天日期: {today}")
        
        # 1. 系统概览卡片的查询（原始）
        print("\n1️⃣ 系统概览卡片查询（原始）:")
        cursor.execute("""
            SELECT COUNT(*)
            FROM maintenance_records mr
            WHERE mr.next_maintenance_date < ?
        """, (today,))
        overview_count = cursor.fetchone()[0]
        print(f"   逾期数量: {overview_count}")
        
        # 显示详细记录
        cursor.execute("""
            SELECT mr.id, sp.name, e.name, mr.next_maintenance_date, mr.maintenance_status
            FROM maintenance_records mr
            JOIN spare_parts sp ON mr.spare_part_id = sp.id
            JOIN equipment e ON mr.equipment_id = e.id
            WHERE mr.next_maintenance_date < ?
            ORDER BY mr.next_maintenance_date
        """, (today,))
        
        overview_records = cursor.fetchall()
        print("   详细记录:")
        for record in overview_records:
            print(f"   ID:{record['id']} | {record['name']} | {record['name']} | {record['next_maintenance_date']} | {record['maintenance_status']}")
        
        # 2. 系统概览卡片的查询（修复后，过滤replaced_by_plan）
        print("\n2️⃣ 系统概览卡片查询（修复后）:")
        cursor.execute("""
            SELECT COUNT(*)
            FROM maintenance_records mr
            WHERE mr.next_maintenance_date < ?
            AND (mr.maintenance_status IS NULL OR mr.maintenance_status != 'replaced_by_plan')
        """, (today,))
        overview_fixed_count = cursor.fetchone()[0]
        print(f"   逾期数量: {overview_fixed_count}")
        
        # 显示详细记录
        cursor.execute("""
            SELECT mr.id, sp.name, e.name, mr.next_maintenance_date, mr.maintenance_status
            FROM maintenance_records mr
            JOIN spare_parts sp ON mr.spare_part_id = sp.id
            JOIN equipment e ON mr.equipment_id = e.id
            WHERE mr.next_maintenance_date < ?
            AND (mr.maintenance_status IS NULL OR mr.maintenance_status != 'replaced_by_plan')
            ORDER BY mr.next_maintenance_date
        """, (today,))
        
        overview_fixed_records = cursor.fetchall()
        print("   详细记录:")
        for record in overview_fixed_records:
            print(f"   ID:{record['id']} | {record['name']} | {record['name']} | {record['next_maintenance_date']} | {record['maintenance_status']}")
        
        # 3. 维保进度概览图表的查询
        print("\n3️⃣ 维保进度概览图表查询:")
        cursor.execute("""
            SELECT mr.id, mr.next_maintenance_date, sp.maintenance_cycle_months, sp.name, e.name, mr.maintenance_status
            FROM maintenance_records mr
            JOIN spare_parts sp ON mr.spare_part_id = sp.id
            JOIN equipment e ON mr.equipment_id = e.id
            WHERE mr.next_maintenance_date IS NOT NULL
        """)
        
        records = cursor.fetchall()
        overdue_count = 0
        overdue_details = []
        
        for record in records:
            next_date = record['next_maintenance_date']
            if next_date:
                next_date_obj = datetime.strptime(next_date, '%Y-%m-%d').date()
                days_remaining = (next_date_obj - today).days
                
                if days_remaining < 0:  # 逾期
                    overdue_count += 1
                    overdue_details.append({
                        'id': record['id'],
                        'spare_part': record['name'],
                        'equipment': record['name'],
                        'next_date': next_date,
                        'days_overdue': abs(days_remaining),
                        'status': record['maintenance_status']
                    })
        
        print(f"   逾期数量: {overdue_count}")
        print("   详细记录:")
        for detail in overdue_details:
            print(f"   ID:{detail['id']} | {detail['spare_part']} | {detail['equipment']} | {detail['next_date']} | 逾期{detail['days_overdue']}天 | {detail['status']}")
        
        # 4. 维保进度概览图表的查询（修复后）
        print("\n4️⃣ 维保进度概览图表查询（修复后）:")
        cursor.execute("""
            SELECT mr.id, mr.next_maintenance_date, sp.maintenance_cycle_months, sp.name, e.name, mr.maintenance_status
            FROM maintenance_records mr
            JOIN spare_parts sp ON mr.spare_part_id = sp.id
            JOIN equipment e ON mr.equipment_id = e.id
            WHERE mr.next_maintenance_date IS NOT NULL
            AND (mr.maintenance_status IS NULL OR mr.maintenance_status != 'replaced_by_plan')
        """)
        
        records_fixed = cursor.fetchall()
        overdue_fixed_count = 0
        overdue_fixed_details = []
        
        for record in records_fixed:
            next_date = record['next_maintenance_date']
            if next_date:
                next_date_obj = datetime.strptime(next_date, '%Y-%m-%d').date()
                days_remaining = (next_date_obj - today).days
                
                if days_remaining < 0:  # 逾期
                    overdue_fixed_count += 1
                    overdue_fixed_details.append({
                        'id': record['id'],
                        'spare_part': record['name'],
                        'equipment': record['name'],
                        'next_date': next_date,
                        'days_overdue': abs(days_remaining),
                        'status': record['maintenance_status']
                    })
        
        print(f"   逾期数量: {overdue_fixed_count}")
        print("   详细记录:")
        for detail in overdue_fixed_details:
            print(f"   ID:{detail['id']} | {detail['spare_part']} | {detail['equipment']} | {detail['next_date']} | 逾期{detail['days_overdue']}天 | {detail['status']}")
        
        # 5. 总结差异
        print("\n📊 查询结果对比:")
        print(f"   系统概览（原始）: {overview_count} 个逾期")
        print(f"   系统概览（修复后）: {overview_fixed_count} 个逾期")
        print(f"   维保进度概览（原始）: {overdue_count} 个逾期")
        print(f"   维保进度概览（修复后）: {overdue_fixed_count} 个逾期")
        
        if overview_fixed_count == overdue_fixed_count:
            print("   ✅ 修复后两个查询结果一致！")
        else:
            print("   ❌ 修复后仍有差异，需要进一步检查")
            
    finally:
        conn.close()

if __name__ == "__main__":
    compare_overdue_queries()
