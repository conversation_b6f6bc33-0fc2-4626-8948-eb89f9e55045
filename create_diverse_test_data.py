#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime, timedelta
import random

def create_diverse_test_data():
    """创建多样化的测试数据，包括不同维保状态和库存状态各两条"""
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 清除现有数据
        print("清除现有数据...")
        cursor.execute('DELETE FROM maintenance_records')
        cursor.execute('DELETE FROM inventory')
        cursor.execute('DELETE FROM spare_parts')
        cursor.execute('DELETE FROM equipment')
        cursor.execute('DELETE FROM areas')
        
        # 创建区域数据
        print("创建区域数据...")
        areas = [
            (1, '生产车间A', '主要生产线区域'),
            (2, '生产车间B', '辅助生产线区域'),
            (3, '仓储区', '原料和成品存储区域')
        ]
        
        cursor.executemany('''
            INSERT INTO areas (id, name, description) 
            VALUES (?, ?, ?)
        ''', areas)
        
        # 创建设备数据
        print("创建设备数据...")
        equipment_list = [
            (1, '冲压机001', 'hydraulic_press', 1, '大型液压冲压设备'),
            (2, '焊接机器人002', 'welding_robot', 1, '自动化焊接机器人'),
            (3, '包装机003', 'packaging_machine', 2, '自动包装设备'),
            (4, '叉车004', 'forklift', 3, '电动叉车'),
            (5, '传送带005', 'conveyor', 2, '自动传送系统'),
            (6, '检测设备006', 'inspection_device', 1, '质量检测设备')
        ]
        
        cursor.executemany('''
            INSERT INTO equipment (id, name, type, area_id, description) 
            VALUES (?, ?, ?, ?, ?)
        ''', equipment_list)
        
        # 创建零备件数据（12种不同的零备件）
        print("创建零备件数据...")
        spare_parts = [
            (1, '液压油缸密封圈', '用于液压系统的密封'),
            (2, '焊接电极', '焊接机器人专用电极'),
            (3, '传动皮带', '设备传动系统皮带'),
            (4, '轴承6205', '标准深沟球轴承'),
            (5, '电机碳刷', '电机换向器碳刷'),
            (6, '液压油滤芯', '液压系统过滤器'),
            (7, '气动密封件', '气动系统密封组件'),
            (8, '传感器探头', '位置检测传感器'),
            (9, '控制器模块', 'PLC控制模块'),
            (10, '减速机齿轮', '减速机内部齿轮'),
            (11, '冷却风扇', '设备散热风扇'),
            (12, '安全开关', '设备安全保护开关')
        ]
        
        cursor.executemany('''
            INSERT INTO spare_parts (id, name, description) 
            VALUES (?, ?, ?)
        ''', spare_parts)
        
        # 创建库存数据 - 不同库存状态各两条
        print("创建库存数据...")
        now = datetime.now()
        inventory_data = [
            # 充足状态 - 2条
            (1, 1, 50, 10, '仓库A-01', now.strftime('%Y-%m-%d %H:%M:%S')),  # 液压油缸密封圈
            (2, 2, 100, 20, '仓库A-02', now.strftime('%Y-%m-%d %H:%M:%S')), # 焊接电极
            
            # 库存不足状态 - 2条  
            (3, 3, 8, 15, '仓库B-01', now.strftime('%Y-%m-%d %H:%M:%S')),   # 传动皮带
            (4, 4, 5, 10, '仓库B-02', now.strftime('%Y-%m-%d %H:%M:%S')),   # 轴承6205
            
            # 缺货状态 - 2条
            (5, 5, 0, 12, '仓库C-01', now.strftime('%Y-%m-%d %H:%M:%S')),   # 电机碳刷
            (6, 6, 0, 8, '仓库C-02', now.strftime('%Y-%m-%d %H:%M:%S')),    # 液压油滤芯
            
            # 其他库存
            (7, 7, 25, 5, '仓库A-03', now.strftime('%Y-%m-%d %H:%M:%S')),   # 气动密封件
            (8, 8, 15, 8, '仓库B-03', now.strftime('%Y-%m-%d %H:%M:%S')),   # 传感器探头
            (9, 9, 3, 5, '仓库C-03', now.strftime('%Y-%m-%d %H:%M:%S')),    # 控制器模块
            (10, 10, 20, 6, '仓库A-04', now.strftime('%Y-%m-%d %H:%M:%S')), # 减速机齿轮
            (11, 11, 30, 10, '仓库B-04', now.strftime('%Y-%m-%d %H:%M:%S')), # 冷却风扇
            (12, 12, 12, 15, '仓库C-04', now.strftime('%Y-%m-%d %H:%M:%S'))  # 安全开关
        ]
        
        cursor.executemany('''
            INSERT INTO inventory (id, spare_part_id, quantity, min_quantity, location, last_updated) 
            VALUES (?, ?, ?, ?, ?, ?)
        ''', inventory_data)
        
        # 创建维保记录数据 - 不同维保状态各两条
        print("创建维保记录数据...")
        
        # 维保状态定义：
        # 正常 - 距离下次维保还有较长时间
        # 即将到期 - 距离下次维保7天内
        # 已过期 - 已超过维保日期
        
        maintenance_records = []
        record_id = 1
        
        # 正常状态 - 2条（下次维保在30天后）
        future_date_1 = now + timedelta(days=30)
        future_date_2 = now + timedelta(days=45)
        
        maintenance_records.extend([
            (record_id, 1, 1, now.strftime('%Y-%m-%d'), future_date_1.strftime('%Y-%m-%d'), 
             'routine', '定期保养', '更换液压油，检查密封件', 2, now.strftime('%Y-%m-%d %H:%M:%S')),
            (record_id + 1, 2, 2, now.strftime('%Y-%m-%d'), future_date_2.strftime('%Y-%m-%d'), 
             'routine', '定期维护', '清洁焊接头，校准参数', 3, now.strftime('%Y-%m-%d %H:%M:%S'))
        ])
        record_id += 2
        
        # 即将到期状态 - 2条（下次维保在3-7天内）
        near_date_1 = now + timedelta(days=3)
        near_date_2 = now + timedelta(days=5)
        
        maintenance_records.extend([
            (record_id, 3, 3, (now - timedelta(days=27)).strftime('%Y-%m-%d'), near_date_1.strftime('%Y-%m-%d'), 
             'routine', '月度检查', '检查包装机运行状态', 1, now.strftime('%Y-%m-%d %H:%M:%S')),
            (record_id + 1, 4, 4, (now - timedelta(days=25)).strftime('%Y-%m-%d'), near_date_2.strftime('%Y-%m-%d'), 
             'routine', '电池检查', '检查叉车电池状态', 1, now.strftime('%Y-%m-%d %H:%M:%S'))
        ])
        record_id += 2
        
        # 已过期状态 - 2条（维保日期已过）
        overdue_date_1 = now - timedelta(days=5)
        overdue_date_2 = now - timedelta(days=10)
        
        maintenance_records.extend([
            (record_id, 5, 5, (now - timedelta(days=35)).strftime('%Y-%m-%d'), overdue_date_1.strftime('%Y-%m-%d'), 
             'routine', '传送带维护', '需要更换传动皮带', 1, now.strftime('%Y-%m-%d %H:%M:%S')),
            (record_id + 1, 6, 6, (now - timedelta(days=40)).strftime('%Y-%m-%d'), overdue_date_2.strftime('%Y-%m-%d'), 
             'routine', '检测设备校准', '需要重新校准检测参数', 1, now.strftime('%Y-%m-%d %H:%M:%S'))
        ])
        record_id += 2
        
        # 添加一些其他状态的记录
        maintenance_records.extend([
            # 紧急维修记录
            (record_id, 1, 7, (now - timedelta(days=2)).strftime('%Y-%m-%d'), (now + timedelta(days=60)).strftime('%Y-%m-%d'), 
             'emergency', '紧急维修', '气动系统泄漏修复', 2, now.strftime('%Y-%m-%d %H:%M:%S')),
            
            # 预防性维护记录
            (record_id + 1, 2, 8, (now - timedelta(days=1)).strftime('%Y-%m-%d'), (now + timedelta(days=90)).strftime('%Y-%m-%d'), 
             'preventive', '预防性维护', '传感器精度校准', 3, now.strftime('%Y-%m-%d %H:%M:%S')),
            
            # 故障维修记录
            (record_id + 2, 3, 9, (now - timedelta(days=3)).strftime('%Y-%m-%d'), (now + timedelta(days=30)).strftime('%Y-%m-%d'), 
             'breakdown', '故障维修', '控制器模块更换', 1, now.strftime('%Y-%m-%d %H:%M:%S')),
            
            # 升级改造记录
            (record_id + 3, 4, 10, (now - timedelta(days=7)).strftime('%Y-%m-%d'), (now + timedelta(days=120)).strftime('%Y-%m-%d'), 
             'upgrade', '设备升级', '减速机齿轮组更换升级', 2, now.strftime('%Y-%m-%d %H:%M:%S'))
        ])
        
        cursor.executemany('''
            INSERT INTO maintenance_records 
            (id, equipment_id, spare_part_id, maintenance_date, next_maintenance_date, 
             maintenance_type, description, notes, quantity_used, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', maintenance_records)
        
        conn.commit()
        
        # 验证数据
        print("\n=== 数据创建完成，验证结果 ===")
        
        cursor.execute('SELECT COUNT(*) FROM areas')
        print(f"区域数量: {cursor.fetchone()[0]}")
        
        cursor.execute('SELECT COUNT(*) FROM equipment')
        print(f"设备数量: {cursor.fetchone()[0]}")
        
        cursor.execute('SELECT COUNT(*) FROM spare_parts')
        print(f"零备件数量: {cursor.fetchone()[0]}")
        
        cursor.execute('SELECT COUNT(*) FROM inventory')
        print(f"库存记录数量: {cursor.fetchone()[0]}")
        
        cursor.execute('SELECT COUNT(*) FROM maintenance_records')
        print(f"维保记录数量: {cursor.fetchone()[0]}")
        
        # 验证库存状态分布
        print("\n=== 库存状态分布 ===")
        cursor.execute('''
            SELECT 
                CASE 
                    WHEN quantity = 0 THEN '缺货'
                    WHEN quantity <= min_quantity THEN '库存不足'
                    ELSE '充足'
                END as status,
                COUNT(*) as count
            FROM inventory 
            GROUP BY 
                CASE 
                    WHEN quantity = 0 THEN '缺货'
                    WHEN quantity <= min_quantity THEN '库存不足'
                    ELSE '充足'
                END
        ''')
        
        for row in cursor.fetchall():
            print(f"{row[0]}: {row[1]} 条")
        
        # 验证维保状态分布
        print("\n=== 维保状态分布 ===")
        cursor.execute('''
            SELECT 
                CASE 
                    WHEN next_maintenance_date < date('now') THEN '已过期'
                    WHEN next_maintenance_date <= date('now', '+7 days') THEN '即将到期'
                    ELSE '正常'
                END as status,
                COUNT(*) as count
            FROM maintenance_records 
            GROUP BY 
                CASE 
                    WHEN next_maintenance_date < date('now') THEN '已过期'
                    WHEN next_maintenance_date <= date('now', '+7 days') THEN '即将到期'
                    ELSE '正常'
                END
        ''')
        
        for row in cursor.fetchall():
            print(f"{row[0]}: {row[1]} 条")
            
        print("\n✅ 多样化测试数据创建成功！")
        
    except Exception as e:
        print(f"❌ 创建数据时出错: {e}")
        conn.rollback()
        
    finally:
        conn.close()

if __name__ == "__main__":
    create_diverse_test_data()
