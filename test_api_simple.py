#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试API接口
"""

import urllib.request
import urllib.parse
import json

def test_get_areas():
    """测试获取区域列表"""
    print("测试获取区域列表...")
    
    try:
        url = "http://127.0.0.1:5000/api/areas"
        with urllib.request.urlopen(url) as response:
            data = json.loads(response.read().decode())
            print(f"✅ 获取区域成功: {len(data['data'])} 个区域")
            return data['data']
    except Exception as e:
        print(f"❌ 获取区域失败: {e}")
        return []

def test_create_area():
    """测试创建区域"""
    print("测试创建区域...")
    
    try:
        url = "http://127.0.0.1:5000/api/areas"
        data = {
            "name": "API测试区域",
            "description": "通过API创建的测试区域"
        }
        
        # 准备POST请求
        json_data = json.dumps(data).encode('utf-8')
        req = urllib.request.Request(url, data=json_data)
        req.add_header('Content-Type', 'application/json')
        
        with urllib.request.urlopen(req) as response:
            result = json.loads(response.read().decode())
            if result.get('success'):
                print(f"✅ 区域创建成功: ID {result['data']['id']}")
                return result['data']['id']
            else:
                print(f"❌ 区域创建失败: {result.get('error')}")
                return None
    except Exception as e:
        print(f"❌ 区域创建异常: {e}")
        return None

def test_delete_area(area_id):
    """测试删除区域"""
    print(f"测试删除区域 ID: {area_id}...")
    
    try:
        url = f"http://127.0.0.1:5000/api/areas/{area_id}"
        req = urllib.request.Request(url, method='DELETE')
        
        with urllib.request.urlopen(req) as response:
            result = json.loads(response.read().decode())
            if result.get('success'):
                print("✅ 区域删除成功")
                return True
            else:
                print(f"❌ 区域删除失败: {result.get('error')}")
                return False
    except Exception as e:
        print(f"❌ 区域删除异常: {e}")
        return False

def main():
    """主函数"""
    print("🔧 简单API测试")
    print("=" * 30)
    
    # 1. 获取现有区域
    areas = test_get_areas()
    
    # 2. 创建新区域
    new_area_id = test_create_area()
    
    if new_area_id:
        # 3. 再次获取区域列表验证
        print("\n验证创建结果...")
        new_areas = test_get_areas()
        
        # 4. 删除测试区域
        test_delete_area(new_area_id)
        
        # 5. 最终验证
        print("\n最终验证...")
        final_areas = test_get_areas()
        
        print(f"\n📊 测试结果:")
        print(f"   初始区域数: {len(areas)}")
        print(f"   创建后区域数: {len(new_areas)}")
        print(f"   删除后区域数: {len(final_areas)}")
        
        if len(final_areas) == len(areas):
            print("✅ CRUD测试通过!")
        else:
            print("❌ CRUD测试失败!")
    else:
        print("❌ 无法进行完整测试")

if __name__ == "__main__":
    main()
