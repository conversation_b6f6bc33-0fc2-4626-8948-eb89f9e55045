#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正零备件逻辑：将零备件从绑定设备改为独立实体
"""

import sqlite3
import os

def fix_spare_parts_logic():
    """修正零备件逻辑"""
    db_path = 'equipment_maintenance.db'
    
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("🔧 开始修正零备件逻辑...")
        
        # 1. 检查当前spare_parts表结构
        print("\n1️⃣ 检查当前表结构...")
        cursor.execute("PRAGMA table_info(spare_parts)")
        columns = cursor.fetchall()
        
        has_equipment_id = any(col['name'] == 'equipment_id' for col in columns)
        print(f"当前spare_parts表是否有equipment_id字段: {has_equipment_id}")
        
        if not has_equipment_id:
            print("✅ 零备件表已经是正确的结构，无需修改")
            return True
        
        # 2. 备份当前数据
        print("\n2️⃣ 备份当前零备件数据...")
        cursor.execute("SELECT * FROM spare_parts")
        old_spare_parts = cursor.fetchall()
        print(f"备份了 {len(old_spare_parts)} 条零备件记录")
        
        # 3. 创建新的零备件表（无equipment_id）
        print("\n3️⃣ 创建新的零备件表结构...")
        cursor.execute("DROP TABLE IF EXISTS spare_parts_new")
        cursor.execute('''
            CREATE TABLE spare_parts_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                maintenance_cycle_months INTEGER NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 4. 迁移数据：去重并合并相同名称的零备件
        print("\n4️⃣ 迁移零备件数据（去重合并）...")
        
        # 按名称分组，选择最常见的维保周期和最详细的描述
        cursor.execute('''
            INSERT INTO spare_parts_new (name, maintenance_cycle_months, description)
            SELECT 
                name,
                maintenance_cycle_months,
                description
            FROM (
                SELECT 
                    name,
                    maintenance_cycle_months,
                    description,
                    ROW_NUMBER() OVER (
                        PARTITION BY name 
                        ORDER BY 
                            CASE WHEN description IS NOT NULL AND description != '' THEN 0 ELSE 1 END,
                            LENGTH(description) DESC,
                            id
                    ) as rn
                FROM spare_parts
            ) ranked
            WHERE rn = 1
            ORDER BY name
        ''')
        
        migrated_count = cursor.rowcount
        print(f"✅ 迁移了 {migrated_count} 种独立零备件")
        
        # 5. 更新维保记录表的spare_part_id引用
        print("\n5️⃣ 更新维保记录的零备件引用...")
        
        # 创建临时映射表
        cursor.execute('''
            CREATE TEMP TABLE spare_part_mapping AS
            SELECT 
                old_sp.id as old_id,
                new_sp.id as new_id,
                old_sp.name
            FROM spare_parts old_sp
            JOIN spare_parts_new new_sp ON old_sp.name = new_sp.name
        ''')
        
        # 更新maintenance_records表
        cursor.execute('''
            UPDATE maintenance_records 
            SET spare_part_id = (
                SELECT new_id 
                FROM spare_part_mapping 
                WHERE old_id = maintenance_records.spare_part_id
            )
            WHERE spare_part_id IN (SELECT old_id FROM spare_part_mapping)
        ''')
        
        updated_records = cursor.rowcount
        print(f"✅ 更新了 {updated_records} 条维保记录")
        
        # 6. 更新维保计划表的spare_part_id引用（如果存在）
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='maintenance_plans'")
        if cursor.fetchone():
            print("\n6️⃣ 更新维保计划的零备件引用...")
            cursor.execute('''
                UPDATE maintenance_plans 
                SET spare_part_id = (
                    SELECT new_id 
                    FROM spare_part_mapping 
                    WHERE old_id = maintenance_plans.spare_part_id
                )
                WHERE spare_part_id IN (SELECT old_id FROM spare_part_mapping)
            ''')
            updated_plans = cursor.rowcount
            print(f"✅ 更新了 {updated_plans} 条维保计划")
        
        # 7. 更新库存表的spare_part_id引用（如果使用旧结构）
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='inventory'")
        if cursor.fetchone():
            print("\n7️⃣ 更新库存表的零备件引用...")
            cursor.execute('''
                UPDATE inventory 
                SET spare_part_id = (
                    SELECT new_id 
                    FROM spare_part_mapping 
                    WHERE old_id = inventory.spare_part_id
                )
                WHERE spare_part_id IN (SELECT old_id FROM spare_part_mapping)
            ''')
            updated_inventory = cursor.rowcount
            print(f"✅ 更新了 {updated_inventory} 条库存记录")
        
        # 8. 替换旧表
        print("\n8️⃣ 替换旧的零备件表...")
        cursor.execute("DROP TABLE spare_parts")
        cursor.execute("ALTER TABLE spare_parts_new RENAME TO spare_parts")
        
        # 9. 确保全局库存表有对应记录
        print("\n9️⃣ 同步全局库存表...")
        cursor.execute('''
            INSERT OR IGNORE INTO inventory_global (spare_part_name, total_quantity, min_quantity, location)
            SELECT name, 0, 1, '中央仓库'
            FROM spare_parts
        ''')
        
        sync_count = cursor.rowcount
        print(f"✅ 同步了 {sync_count} 条全局库存记录")
        
        # 10. 验证结果
        print("\n🔍 验证修正结果...")
        cursor.execute("SELECT COUNT(*) FROM spare_parts")
        final_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT name, maintenance_cycle_months FROM spare_parts ORDER BY name")
        final_parts = cursor.fetchall()
        
        print(f"✅ 最终零备件数量: {final_count}")
        print("零备件列表:")
        for part in final_parts:
            print(f"  - {part['name']} ({part['maintenance_cycle_months']}个月)")
        
        conn.commit()
        conn.close()
        
        print("\n🎉 零备件逻辑修正完成！")
        print("现在零备件是独立实体，不再绑定到特定设备")
        return True
        
    except Exception as e:
        print(f"❌ 修正过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 零备件逻辑修正工具")
    print("=" * 50)
    
    success = fix_spare_parts_logic()
    
    if success:
        print("\n✅ 修正完成！现在可以重新启动应用")
    else:
        print("\n❌ 修正失败，请检查错误信息")

if __name__ == "__main__":
    main()
