#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

# 确保在正确的目录
os.chdir(r'F:\CSProj\BookCursorProj\QuanShengMing02')

print("当前工作目录:", os.getcwd())
print("数据库文件存在:", os.path.exists('equipment_maintenance.db'))

if os.path.exists('equipment_maintenance.db'):
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 检查表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print("数据库中的表:", [table[0] for table in tables])
        
        # 检查维保记录
        cursor.execute("SELECT COUNT(*) FROM maintenance_records")
        count = cursor.fetchone()[0]
        print(f"维保记录数量: {count}")
        
        if count == 0:
            print("❌ 没有维保记录，需要创建测试数据")
            
            # 检查基础数据
            cursor.execute("SELECT COUNT(*) FROM areas")
            areas_count = cursor.fetchone()[0]
            print(f"区域数量: {areas_count}")
            
            cursor.execute("SELECT COUNT(*) FROM equipment")
            equipment_count = cursor.fetchone()[0]
            print(f"设备数量: {equipment_count}")
            
            cursor.execute("SELECT COUNT(*) FROM spare_parts")
            parts_count = cursor.fetchone()[0]
            print(f"零备件数量: {parts_count}")
            
    except Exception as e:
        print(f"检查出错: {e}")
    finally:
        conn.close()
else:
    print("❌ 数据库文件不存在")
