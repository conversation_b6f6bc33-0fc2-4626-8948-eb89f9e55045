import sqlite3

conn = sqlite3.connect('equipment_maintenance.db')
cursor = conn.cursor()

# 清除现有记录
cursor.execute("DELETE FROM maintenance_records")

# 插入9条精简记录
records = [
    (1, 1, 1, '2024-01-15', '2024-12-04', '张师傅', '严重逾期，需立即处理', 1),
    (2, 2, 2, '2024-02-10', '2024-12-30', '李师傅', '中度逾期，尽快安排维保', 1),
    (3, 3, 3, '2024-03-01', '2025-01-14', '王师傅', '轻度逾期，请及时维保', 1),
    (4, 1, 4, '2024-07-16', '2025-01-22', '张师傅', '紧急！还剩3天，请立即安排', 1),
    (5, 2, 5, '2024-06-04', '2025-02-03', '李师傅', '警告：还剩15天，需要准备', 1),
    (6, 3, 6, '2024-05-17', '2025-02-16', '王师傅', '提醒：还剩28天，可以开始计划', 1),
    (7, 1, 7, '2024-04-14', '2025-03-20', '张师傅', '正常状态，还剩2个月', 1),
    (8, 2, 8, '2024-02-13', '2025-05-19', '李师傅', '状态良好，还剩4个月', 1),
    (9, 3, 9, '2025-01-19', '2026-01-19', '王师傅', '长期维保，还剩1年', 1)
]

cursor.executemany('''
    INSERT INTO maintenance_records 
    (spare_part_id, area_id, equipment_id, maintenance_date, next_maintenance_date, technician, notes, quantity_used)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
''', records)

# 更新库存
cursor.execute("UPDATE inventory SET quantity = 2, min_quantity = 5 WHERE spare_part_id = 1")
cursor.execute("UPDATE inventory SET quantity = 1, min_quantity = 5 WHERE spare_part_id = 2")

conn.commit()

# 检查结果
cursor.execute("SELECT COUNT(*) FROM maintenance_records")
count = cursor.fetchone()[0]
print(f"插入了 {count} 条维保记录")

cursor.execute("SELECT COUNT(*) FROM inventory WHERE quantity < min_quantity")
low_stock = cursor.fetchone()[0]
print(f"库存不足: {low_stock} 条")

conn.close()
print("数据插入完成！")
