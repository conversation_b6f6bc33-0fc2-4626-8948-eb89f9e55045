#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复维保记录表的唯一约束问题
删除不合理的唯一约束，允许同一零备件有多次维保记录
"""

import sqlite3
import os
from datetime import datetime

def fix_maintenance_records_constraint():
    """修复维保记录表的唯一约束"""
    print("🔧 修复维保记录表的唯一约束...")
    
    # 备份数据库
    backup_name = f'equipment_maintenance_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
    if os.path.exists('equipment_maintenance.db'):
        os.system(f'copy equipment_maintenance.db {backup_name}')
        print(f"✅ 数据库已备份为: {backup_name}")
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 1. 检查当前的索引
        print("\n1️⃣ 检查当前索引...")
        cursor.execute("PRAGMA index_list(maintenance_records)")
        indexes = cursor.fetchall()
        
        print("当前索引:")
        for index in indexes:
            index_name = index[1]
            print(f"  - {index_name}")
            
            # 获取索引详情
            cursor.execute(f"PRAGMA index_info({index_name})")
            index_info = cursor.fetchall()
            columns = [info[2] for info in index_info]
            print(f"    列: {', '.join(columns)}")
        
        # 2. 删除有问题的唯一索引
        print("\n2️⃣ 删除有问题的唯一索引...")
        try:
            cursor.execute("DROP INDEX IF EXISTS idx_maintenance_unique")
            print("✅ 删除了 idx_maintenance_unique 索引")
        except Exception as e:
            print(f"⚠️  删除索引时出错: {e}")
        
        # 3. 重新创建合理的索引
        print("\n3️⃣ 重新创建合理的索引...")
        
        # 创建非唯一的复合索引，用于查询优化
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_maintenance_composite 
            ON maintenance_records(spare_part_id, equipment_id, maintenance_date)
        """)
        print("✅ 创建了复合索引 idx_maintenance_composite")
        
        # 创建下次维保日期索引
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_maintenance_next_date_new 
            ON maintenance_records(next_maintenance_date)
        """)
        print("✅ 创建了下次维保日期索引")
        
        conn.commit()
        
        # 4. 验证修复结果
        print("\n4️⃣ 验证修复结果...")
        cursor.execute("PRAGMA index_list(maintenance_records)")
        new_indexes = cursor.fetchall()
        
        print("修复后的索引:")
        unique_indexes = []
        for index in new_indexes:
            index_name = index[1]
            is_unique = index[2]  # 1表示唯一索引
            
            cursor.execute(f"PRAGMA index_info({index_name})")
            index_info = cursor.fetchall()
            columns = [info[2] for info in index_info]
            
            status = "唯一" if is_unique else "普通"
            print(f"  - {index_name} ({status}): {', '.join(columns)}")
            
            if is_unique:
                unique_indexes.append(index_name)
        
        if not unique_indexes:
            print("✅ 没有唯一索引，维保记录可以正常添加")
        else:
            print(f"⚠️  仍有唯一索引: {', '.join(unique_indexes)}")
        
        # 5. 测试插入维保记录
        print("\n5️⃣ 测试插入维保记录...")
        
        # 获取一个测试用的零备件
        cursor.execute("""
            SELECT sp.id, sp.name, e.id as equipment_id, e.area_id
            FROM spare_parts sp
            JOIN equipment e ON sp.equipment_id = e.id
            LIMIT 1
        """)
        test_part = cursor.fetchone()
        
        if test_part:
            spare_part_id, spare_part_name, equipment_id, area_id = test_part
            
            # 尝试插入两条相同零备件的维保记录
            test_records = [
                (spare_part_id, area_id, equipment_id, '2025-07-19', '2025-10-19', '测试员1', '测试记录1', 1, 0, 'completed'),
                (spare_part_id, area_id, equipment_id, '2025-07-20', '2025-10-20', '测试员2', '测试记录2', 1, 0, 'completed')
            ]
            
            try:
                cursor.executemany("""
                    INSERT INTO maintenance_records
                    (spare_part_id, area_id, equipment_id, maintenance_date, next_maintenance_date,
                     technician, notes, quantity_used, is_first_maintenance, maintenance_status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, test_records)
                
                print(f"✅ 成功插入测试维保记录: {spare_part_name}")
                
                # 删除测试记录
                cursor.execute("""
                    DELETE FROM maintenance_records 
                    WHERE notes IN ('测试记录1', '测试记录2')
                """)
                print("✅ 清理了测试记录")
                
            except Exception as e:
                print(f"❌ 插入测试记录失败: {e}")
                return False
        
        conn.commit()
        print("\n🎉 维保记录表约束修复完成！")
        return True
        
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def main():
    """主函数"""
    print("🔧 维保记录表约束修复工具")
    print("=" * 40)
    print("问题: 维保记录表有不合理的唯一约束")
    print("解决: 删除唯一约束，允许同一零备件有多次维保记录")
    print("=" * 40)
    
    success = fix_maintenance_records_constraint()
    
    if success:
        print("\n✅ 修复完成！现在可以正常执行维保计划了")
    else:
        print("\n❌ 修复失败")
    
    return success

if __name__ == "__main__":
    main()
