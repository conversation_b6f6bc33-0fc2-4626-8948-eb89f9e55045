#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速检查维保记录重复情况
"""

import sqlite3
import sys
import os

def main():
    print("=== 快速检查维保记录重复情况 ===")
    
    db_path = 'equipment_maintenance.db'
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查维保记录表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='maintenance_records'")
        if not cursor.fetchone():
            print("❌ maintenance_records表不存在")
            return
        
        # 检查是否存在相同区域、设备、零件的重复维保记录
        cursor.execute("""
            SELECT 
                area_id,
                equipment_id, 
                spare_part_id,
                COUNT(*) as count
            FROM maintenance_records
            GROUP BY area_id, equipment_id, spare_part_id
            HAVING COUNT(*) > 1
            ORDER BY count DESC
        """)
        
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"❌ 发现 {len(duplicates)} 组重复的维保记录:")
            for i, dup in enumerate(duplicates, 1):
                area_id, equipment_id, spare_part_id, count = dup
                print(f"  {i}. 区域ID: {area_id}, 设备ID: {equipment_id}, 零件ID: {spare_part_id}, 重复次数: {count}")
                
                # 显示具体的重复记录
                cursor.execute("""
                    SELECT id, maintenance_date, next_maintenance_date, technician
                    FROM maintenance_records
                    WHERE area_id = ? AND equipment_id = ? AND spare_part_id = ?
                    ORDER BY maintenance_date DESC
                """, (area_id, equipment_id, spare_part_id))
                
                records = cursor.fetchall()
                for record in records:
                    print(f"     记录ID: {record[0]}, 维保日期: {record[1]}, 下次维保: {record[2]}, 技师: {record[3]}")
                print()
        else:
            print("✅ 没有发现重复的维保记录")
        
        # 统计总的维保记录数
        cursor.execute("SELECT COUNT(*) FROM maintenance_records")
        total_count = cursor.fetchone()[0]
        print(f"总维保记录数: {total_count}")
        
        # 统计不同组合的数量
        cursor.execute("""
            SELECT COUNT(DISTINCT area_id, equipment_id, spare_part_id) 
            FROM maintenance_records
        """)
        unique_combinations = cursor.fetchone()[0]
        print(f"唯一的区域-设备-零件组合数: {unique_combinations}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
