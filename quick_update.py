#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime, timedelta
import random

def quick_update():
    """快速更新维保数据，减少即将到期的记录到5条"""
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        print("=== 快速更新维保数据 ===")
        
        # 1. 检查当前状态
        today = datetime.now().date()
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date < ?", (today,))
        overdue = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date BETWEEN ? AND ?", 
                       (today, today + timedelta(days=30)))
        upcoming = cursor.fetchone()[0]
        
        print(f"当前状态: 逾期 {overdue} 条, 即将到期 {upcoming} 条")
        
        # 2. 如果即将到期的记录超过5条，删除多余的
        if upcoming > 5:
            print(f"即将到期记录过多({upcoming}条)，删除多余的...")
            
            # 获取即将到期的记录ID，按日期排序，保留前5条
            cursor.execute("""
                SELECT id FROM maintenance_records 
                WHERE next_maintenance_date BETWEEN ? AND ?
                ORDER BY next_maintenance_date
                LIMIT -1 OFFSET 5
            """, (today, today + timedelta(days=30)))
            
            records_to_delete = cursor.fetchall()
            
            if records_to_delete:
                ids_to_delete = [str(record[0]) for record in records_to_delete]
                cursor.execute(f"DELETE FROM maintenance_records WHERE id IN ({','.join(ids_to_delete)})")
                print(f"删除了 {len(records_to_delete)} 条多余的即将到期记录")
        
        # 3. 如果即将到期的记录少于5条，添加一些
        elif upcoming < 5:
            needed = 5 - upcoming
            print(f"即将到期记录不足({upcoming}条)，添加 {needed} 条...")
            
            # 获取基础数据
            cursor.execute("SELECT id FROM areas ORDER BY RANDOM() LIMIT 1")
            area_id = cursor.fetchone()[0]
            
            cursor.execute("SELECT id FROM equipment WHERE area_id = ? ORDER BY RANDOM() LIMIT 1", (area_id,))
            equipment_id = cursor.fetchone()[0]
            
            cursor.execute("SELECT id FROM spare_parts ORDER BY RANDOM() LIMIT ?", (needed,))
            spare_parts = cursor.fetchall()
            
            # 添加记录
            for i, (sp_id,) in enumerate(spare_parts):
                days_remaining = random.randint(1, 30)
                maintenance_date = today - timedelta(days=300)
                next_maintenance_date = today + timedelta(days=days_remaining)
                
                cursor.execute("""
                    INSERT INTO maintenance_records 
                    (spare_part_id, area_id, equipment_id, maintenance_date, next_maintenance_date, 
                     technician, notes, quantity_used)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (sp_id, area_id, equipment_id, 
                      maintenance_date.isoformat(), next_maintenance_date.isoformat(),
                      '测试师傅', f'即将到期测试记录{i+1}', 1))
            
            print(f"添加了 {needed} 条即将到期记录")
        
        conn.commit()
        
        # 4. 验证结果
        cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date < ?", (today,))
        final_overdue = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date BETWEEN ? AND ?", 
                       (today, today + timedelta(days=30)))
        final_upcoming = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date > ?", 
                       (today + timedelta(days=30),))
        final_normal = cursor.fetchone()[0]
        
        print(f"\n最终状态:")
        print(f"  逾期: {final_overdue} 条")
        print(f"  即将到期: {final_upcoming} 条")
        print(f"  正常: {final_normal} 条")
        print("更新完成！")
        
    except Exception as e:
        print(f"更新出错: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    quick_update()
