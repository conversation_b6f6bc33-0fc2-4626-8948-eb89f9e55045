#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime, timedelta

def check_data_status():
    """检查数据库状态"""
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        print("=== 数据库状态检查 ===")
        
        # 检查各表的记录数量
        tables = ['areas', 'equipment', 'spare_parts', 'maintenance_records', 'inventory']
        
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"{table}: {count} 条记录")
            except Exception as e:
                print(f"{table}: 表不存在或查询失败 - {e}")
        
        # 检查维保记录的详细状态
        cursor.execute("SELECT COUNT(*) FROM maintenance_records")
        total_maintenance = cursor.fetchone()[0]
        
        if total_maintenance > 0:
            today = datetime.now().date()
            
            cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date < ?", (today,))
            overdue = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date BETWEEN ? AND ?", 
                           (today, today + timedelta(days=30)))
            upcoming = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date > ?", 
                           (today + timedelta(days=30),))
            normal = cursor.fetchone()[0]
            
            print(f"\n维保记录状态分布:")
            print(f"  逾期: {overdue} 条")
            print(f"  即将到期: {upcoming} 条")
            print(f"  正常: {normal} 条")
            
            # 显示前几条维保记录
            cursor.execute("""
                SELECT mr.id, sp.name, e.name, mr.next_maintenance_date, mr.notes
                FROM maintenance_records mr
                JOIN spare_parts sp ON mr.spare_part_id = sp.id
                JOIN equipment e ON mr.equipment_id = e.id
                ORDER BY mr.next_maintenance_date
                LIMIT 5
            """)
            
            records = cursor.fetchall()
            print(f"\n前5条维保记录:")
            for record in records:
                print(f"  ID:{record[0]} - {record[1]} - {record[2]} - {record[3]} - {record[4]}")
        else:
            print("\n❌ 没有维保记录！")
            
        # 检查库存记录
        cursor.execute("SELECT COUNT(*) FROM inventory")
        inventory_count = cursor.fetchone()[0]
        print(f"\n库存记录: {inventory_count} 条")
        
    except Exception as e:
        print(f"检查出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        conn.close()

if __name__ == "__main__":
    check_data_status()
