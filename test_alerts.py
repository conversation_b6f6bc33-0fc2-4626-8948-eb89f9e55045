import requests
import json

def test_alerts():
    try:
        # 测试维保提醒API
        response = requests.get('http://127.0.0.1:5000/api/maintenance/alerts?days=30')
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"成功: {data.get('success')}")
            if data.get('success'):
                alerts = data.get('data', [])
                print(f"提醒数量: {len(alerts)}")
                
                for i, alert in enumerate(alerts[:5]):
                    print(f"{i+1}. {alert.get('spare_part_name')} - {alert.get('area_name')} - {alert.get('equipment_name')}")
                    print(f"   优先级: {alert.get('priority_text')} - 剩余天数: {alert.get('days_remaining')}")
            else:
                print(f"API错误: {data.get('error')}")
        else:
            print(f"HTTP错误: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

if __name__ == "__main__":
    test_alerts()
