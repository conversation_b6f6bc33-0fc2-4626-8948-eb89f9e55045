#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建精简的测试数据
"""

import sqlite3
import random
from datetime import datetime, timed<PERSON><PERSON>

def create_minimal_test_data():
    """创建精简的测试数据"""
    print("开始创建精简的测试数据...")
    
    conn = sqlite3.connect('equipment_maintenance.db')
    cursor = conn.cursor()
    
    try:
        # 清除现有的维保记录
        cursor.execute("DELETE FROM maintenance_records")
        
        # 获取基础数据
        cursor.execute("SELECT id, name FROM areas")
        areas = cursor.fetchall()
        
        cursor.execute("SELECT id, name, area_id FROM equipment")
        equipment = cursor.fetchall()
        
        cursor.execute("SELECT id, name, maintenance_cycle_months FROM spare_parts")
        spare_parts = cursor.fetchall()
        
        # 创建精简的维保记录
        today = datetime.now().date()
        maintenance_records = []
        
        # 技师名单
        technicians = ["张师傅", "李师傅", "王师傅"]
        
        record_id = 1
        
        # 1. 逾期维保记录（3条：严重逾期、中度逾期、轻度逾期）
        print("创建逾期维保记录...")
        overdue_scenarios = [
            (45, "严重逾期，需立即处理"),
            (20, "中度逾期，尽快安排维保"),
            (5, "轻度逾期，请及时维保")
        ]
        
        for i, (overdue_days, desc) in enumerate(overdue_scenarios):
            area = areas[i % len(areas)]
            eq = random.choice([e for e in equipment if e[2] == area[0]])
            spare_part = spare_parts[i % len(spare_parts)]
            
            maintenance_date = today - timedelta(days=365 + overdue_days)
            next_maintenance_date = today - timedelta(days=overdue_days)
            
            maintenance_records.append((
                record_id, spare_part[0], area[0], eq[0], maintenance_date, next_maintenance_date,
                technicians[i % len(technicians)], desc, 1
            ))
            record_id += 1
        
        # 2. 即将到期维保记录（3条：紧急、警告、提醒）
        print("创建即将到期维保记录...")
        warning_scenarios = [
            (3, "紧急！还剩3天，请立即安排"),
            (15, "警告：还剩15天，需要准备"),
            (28, "提醒：还剩28天，可以开始计划")
        ]
        
        for i, (days_remaining, desc) in enumerate(warning_scenarios):
            area = areas[i % len(areas)]
            eq = random.choice([e for e in equipment if e[2] == area[0]])
            spare_part = spare_parts[(i + 3) % len(spare_parts)]
            
            maintenance_date = today - timedelta(days=365 - days_remaining)
            next_maintenance_date = today + timedelta(days=days_remaining)
            
            maintenance_records.append((
                record_id, spare_part[0], area[0], eq[0], maintenance_date, next_maintenance_date,
                technicians[i % len(technicians)], desc, 1
            ))
            record_id += 1
        
        # 3. 正常状态维保记录（3条：短期、中期、长期）
        print("创建正常状态维保记录...")
        normal_scenarios = [
            (60, "正常状态，还剩2个月"),
            (120, "状态良好，还剩4个月"),
            (365, "长期维保，还剩1年")
        ]
        
        for i, (days_remaining, desc) in enumerate(normal_scenarios):
            area = areas[i % len(areas)]
            eq = random.choice([e for e in equipment if e[2] == area[0]])
            spare_part = spare_parts[(i + 6) % len(spare_parts)]
            
            maintenance_date = today - timedelta(days=365 - days_remaining)
            next_maintenance_date = today + timedelta(days=days_remaining)
            
            maintenance_records.append((
                record_id, spare_part[0], area[0], eq[0], maintenance_date, next_maintenance_date,
                technicians[i % len(technicians)], desc, 1
            ))
            record_id += 1
        
        # 插入维保记录
        cursor.executemany('''
            INSERT INTO maintenance_records 
            (id, spare_part_id, area_id, equipment_id, maintenance_date, next_maintenance_date, technician, notes, quantity_used)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', maintenance_records)
        
        # 更新库存数据，设置一些库存不足的情况
        print("更新库存数据...")
        cursor.execute("SELECT id FROM spare_parts")
        spare_part_ids = [row[0] for row in cursor.fetchall()]
        
        # 设置库存状态：2个库存不足，其他正常
        for i, sp_id in enumerate(spare_part_ids):
            if i < 2:
                # 库存不足
                quantity = random.randint(1, 3)
                min_quantity = 5
            else:
                # 库存正常
                quantity = random.randint(15, 30)
                min_quantity = 5
            
            cursor.execute('''
                UPDATE inventory 
                SET quantity = ?, min_quantity = ?, last_updated = ?
                WHERE spare_part_id = ?
            ''', (quantity, min_quantity, datetime.now(), sp_id))
        
        conn.commit()
        
        # 统计创建的数据
        cursor.execute("SELECT COUNT(*) FROM maintenance_records")
        total_records = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date < ?", (today,))
        overdue_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date BETWEEN ? AND ?", 
                      (today, today + timedelta(days=30)))
        upcoming_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_records WHERE next_maintenance_date > ?", 
                      (today + timedelta(days=30),))
        normal_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM inventory WHERE quantity < min_quantity")
        low_stock_count = cursor.fetchone()[0]
        
        print(f"✅ 精简数据创建完成！")
        print(f"   总维保记录: {total_records}")
        print(f"   逾期维保: {overdue_count} 条")
        print(f"   即将到期: {upcoming_count} 条")
        print(f"   正常状态: {normal_count} 条")
        print(f"   库存不足: {low_stock_count} 条")
        
        # 显示具体记录
        print(f"\n📋 维保记录详情:")
        cursor.execute("""
            SELECT sp.name, a.name, mr.notes, 
                   CASE 
                       WHEN mr.next_maintenance_date < ? THEN '逾期'
                       WHEN mr.next_maintenance_date <= ? THEN '即将到期'
                       ELSE '正常'
                   END as status
            FROM maintenance_records mr
            JOIN spare_parts sp ON mr.spare_part_id = sp.id
            JOIN areas a ON mr.area_id = a.id
            ORDER BY mr.next_maintenance_date
        """, (today, today + timedelta(days=30)))
        
        for i, (part_name, area_name, notes, status) in enumerate(cursor.fetchall(), 1):
            print(f"   {i}. {part_name:12s} - {area_name:6s} - {status:6s} - {notes}")
        
    except Exception as e:
        print(f"创建测试数据时出错: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    create_minimal_test_data()
