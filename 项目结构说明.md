# 项目结构说明

## 📁 项目文件结构

```
QuanShengMing02/
├── 📄 README.MD                    # 项目详细说明文档
├── 📄 提示词.txt                   # 开发提示词文档
├── 📄 项目结构说明.md              # 本文件
├── 
├── 🚀 核心启动文件
├── 📄 start.py                     # 快速启动脚本（推荐使用）
├── 📄 app.py                       # Flask Web应用主文件
├── 📄 deploy.bat                   # Windows自动部署脚本
├── 
├── 🗄️ 数据库相关
├── 📄 database.py                  # 数据库管理模块
├── 📄 maintenance_manager.py       # 维保管理业务逻辑
├── 📄 initialize_system.py         # 系统初始化脚本
├── 📄 maintenance_system.db        # SQLite数据库文件
├── 
├── 📦 依赖配置
├── 📄 requirements.txt             # Python依赖包列表
├── 
├── 🎨 前端资源
├── 📁 templates/                   # HTML模板目录
│   ├── 📄 base.html               # 基础模板
│   ├── 📄 index.html              # 系统概览页面
│   ├── 📄 maintenance.html        # 维保管理页面
│   ├── 📄 inventory.html          # 库存管理页面
│   ├── 📄 data.html               # 数据管理页面
│   ├── 📄 404.html                # 404错误页面
│   └── 📄 500.html                # 500错误页面
├── 
├── 📁 static/                      # 静态资源目录
│   ├── 📁 css/                    # CSS样式文件
│   ├── 📁 js/                     # JavaScript脚本
│   └── 📁 images/                 # 图片资源
└── 
└── 🔧 系统生成文件
    └── 📁 __pycache__/            # Python缓存文件
```

## 🔧 核心模块说明

### 1. 启动模块
- **start.py**: 一键启动脚本，自动检查环境、安装依赖、初始化数据库
- **app.py**: Flask Web应用，提供HTTP服务和API接口
- **deploy.bat**: Windows批处理部署脚本

### 2. 数据库模块
- **database.py**: 数据库连接和基础操作
- **maintenance_manager.py**: 维保业务逻辑处理
- **initialize_system.py**: 数据库初始化和示例数据生成

### 3. 前端模块
- **templates/**: Jinja2模板文件，使用Bootstrap 5框架
- **static/**: 静态资源文件（CSS、JS、图片）

## 🗄️ 数据库设计

### 表结构
1. **areas** - 区域表
   - id, name, description, created_at

2. **equipment** - 设备表
   - id, area_id, name, type, description, created_at

3. **spare_parts** - 零备件表
   - id, equipment_id, name, maintenance_cycle_months, description, created_at

4. **inventory** - 库存表
   - id, spare_part_id, quantity, min_quantity, location, updated_at

5. **maintenance_records** - 维保记录表
   - id, spare_part_id, maintenance_date, next_maintenance_date, technician, notes, created_at

### 关系设计
```
区域 (1:N) 设备 (1:N) 零备件 (1:1) 库存
                      ↓
                   维保记录 (N:1)
```

## 🌐 API接口说明

### 系统概览
- `GET /api/overview` - 获取系统概览统计数据

### 维保管理
- `GET /api/maintenance/status` - 获取维保状态列表
- `GET /api/maintenance/alerts` - 获取维保提醒
- `POST /api/maintenance/record` - 添加维保记录

### 库存管理
- `GET /api/inventory/status` - 获取库存状态
- `POST /api/inventory/update` - 更新库存信息

### 数据管理
- `GET /api/areas` - 获取区域列表
- `GET /api/equipment/<area_id>` - 获取指定区域的设备
- `GET /api/spare-parts/<equipment_id>` - 获取指定设备的零备件
- `GET /api/statistics` - 获取统计数据

## 🎨 界面设计特色

### 响应式设计
- 使用Bootstrap 5框架
- 适配桌面、平板、手机等设备
- 大屏展示优化

### 可视化图表
- Chart.js图表库
- 横向条形图显示维保进度
- 饼图显示区域分布
- 实时数据更新

### 用户体验
- 现代化扁平设计
- 渐变色彩搭配
- 动画过渡效果
- 直观的状态指示

## 🚀 部署方式

### 方式一：快速启动（推荐）
```bash
python start.py
```

### 方式二：Windows批处理
```bash
deploy.bat
```

### 方式三：手动启动
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 初始化数据库
python initialize_system.py

# 3. 启动应用
python app.py
```

## 🔧 配置说明

### 系统配置
- 默认端口: 5000
- 数据库: SQLite (maintenance_system.db)
- 调试模式: 开发时启用

### 安全配置
- Session密钥: 可在app.py中修改
- CORS支持: 允许跨域请求
- 输入验证: 自动数据验证

## 📊 功能特色

### 智能预警
- 🔴 逾期维保自动标红
- 🟡 即将到期黄色提醒
- 📦 库存不足实时监控

### 数据可视化
- 横向进度条显示维保状态
- 区域分布饼图
- 实时统计数据更新

### 操作便捷
- 一键添加维保记录
- 快速库存更新
- 智能筛选和搜索

## 🎯 适用场景

### 企业应用
- 制造业设备管理
- 仓储物流管理
- 工厂维保计划

### 展示场景
- 领导汇报展示
- 大屏监控显示
- 移动端查看

## 📈 扩展建议

### 功能扩展
- 用户权限管理
- 数据导入导出
- 报表生成功能
- 移动端APP

### 技术升级
- 数据库升级到PostgreSQL
- 使用Redis缓存
- 容器化部署
- 微服务架构

---

**开发时间**: 2025年7月  
**技术栈**: Python + Flask + SQLite + Bootstrap + Chart.js  
**版本**: v1.0
